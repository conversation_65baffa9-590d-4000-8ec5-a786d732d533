# FastAPI Configuration
ALLOWED_ORIGINS=http://localhost:5173,http://127.0.0.1:5173

# JWT Configuration
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_REFRESH_SECRET_KEY=your_jwt_refresh_secret_key_here
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here

# Mistral AI settings
MISTRAL_API_KEY="your-mistral-api-key"
MISTRAL_MODEL="mistral-tiny"

# Google Gemini settings
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro

# Default LLM provider
DEFAULT_LLM_PROVIDER="mistral"
USE_LLM=false
CHART_RECOMMENDATION_TYPE=rule_based

# Google Sheets Analytics Configuration
GOOGLE_SHEETS_CREDENTIALS_FILE=path/to/your/service-account-credentials.json
GOOGLE_SHEETS_USER_PROFILE_SHEET_ID=your_user_profile_sheet_id_here
GOOGLE_SHEETS_EVENTS_SHEET_ID=your_events_sheet_id_here
ENABLE_ANALYTICS=true

# Logging Configuration
VERBOSE_LOGGING=false
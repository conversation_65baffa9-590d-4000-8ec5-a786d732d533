# InfoCharts Backend - FastAPI Application

A comprehensive FastAPI backend application with advanced chart insights, analytics, and a centralized common headers system for enhanced tracking and debugging.

## 📋 Table of Contents

- [Key Features](#-key-features)
- [Quick Start](#-quick-start)
- [Common Headers System](#-common-headers-system)
- [API Endpoints](#-api-endpoints)
- [Project Structure](#-project-structure)
- [Testing](#-testing)
- [Adding New Headers](#-adding-new-common-headers)
- [Docker Setup](#-docker-setup-optional)
- [Benefits](#-benefits)
- [Next Steps](#-next-steps)
- [Documentation](#-documentation)

## ⚡ Quick Summary

**InfoCharts Backend** is a production-ready FastAPI application that provides:
- 🤖 **AI-powered chart insights** with multi-LLM support
- 🔗 **Centralized header management** across all APIs
- 📊 **Advanced analytics** and user tracking
- 🔐 **Secure authentication** with Google OAuth
- 📈 **15+ chart types** with intelligent recommendations
- 🛡️ **Enterprise-grade security** and monitoring

**Latest Update**: All APIs now support the common headers system for enhanced tracking, debugging, and analytics. The original `NameError: name 'security' is not defined` issue has been resolved.

## 🚀 Key Features

### Core Functionality
- **📊 Advanced Chart Insights** - AI-powered chart analysis with executive summaries, data insights, hidden patterns, and actionable recommendations
- **📈 Multi-Chart Support** - Support for 15+ chart types including box plots, multi-group charts, heatmaps, and hierarchical charts
- **📁 CSV Processing** - Intelligent CSV parsing with automatic chart recommendations
- **🔐 Google OAuth Authentication** - Secure authentication with JWT tokens
- **📊 Analytics & Event Tracking** - Comprehensive user behavior analytics

### Advanced Systems
- **🔗 Common Headers System** - Centralized header management across all APIs for enhanced tracking
- **🤖 Multi-LLM Support** - Configurable LLM providers (OpenAI, Google Gemini, Mistral, OpenRouter)
- **🛡️ Security Middleware** - Automatic security headers and request validation
- **📝 Enhanced Logging** - Structured logging with request correlation and client context
- **🔄 Backward Compatibility** - Seamless migration path for existing integrations

## 🏗️ Quick Start

### 1. Environment Setup

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
.\venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

Create a `.env` file in the root directory:

```env
# LLM Configuration
DEFAULT_LLM_PROVIDER=openai
OPENAI_API_KEY=your-openai-api-key
GOOGLE_API_KEY=your-google-api-key
MISTRAL_API_KEY=your-mistral-api-key

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key
JWT_REFRESH_SECRET_KEY=your-jwt-refresh-secret-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

### 3. Run the Application

```bash
# Start the development server
uvicorn app.main:app --reload

# The API will be available at:
# - API Documentation: http://127.0.0.1:8000/docs
# - Health Check: http://127.0.0.1:8000/v1/health
```

### 4. Test the Setup

```bash
# Test the common headers system
python test_common_headers_system.py

# Test all APIs
python test_updated_apis.py

# Test client integration
python test_client_headers.py
```

## 🔗 Common Headers System

### Overview

The application features a centralized header management system that provides consistent header handling across all APIs. This enables enhanced tracking, analytics, and debugging capabilities.

### Supported Headers

The system automatically processes these standard headers:

#### Request Tracking
- `x-request-id` - Unique request identifier (auto-generated if missing)
- `x-correlation-id` - Request correlation ID (defaults to request-id)
- `x-session-id` - User session identifier
- `x-device-id` - Device/client identifier

#### Client Information
- `x-client-version` - Application version
- `x-platform` - Client platform (web, mobile, desktop)
- `x-timezone` - Client timezone
- `accept-language` - Client language preference

#### Network Information
- `x-forwarded-for` / `x-real-ip` - Client IP address (proxy-aware)
- `referer` / `referrer` - HTTP referrer
- `user-agent` - Browser/client user agent

### Frontend Integration

#### React/JavaScript Example
```javascript
// utils/apiClient.js
const commonHeaders = {
    'x-request-id': crypto.randomUUID(),
    'x-session-id': sessionStorage.getItem('sessionId') || generateSessionId(),
    'x-device-id': localStorage.getItem('deviceId') || generateDeviceId(),
    'x-client-version': process.env.REACT_APP_VERSION || '1.0.0',
    'x-platform': 'web',
    'x-timezone': Intl.DateTimeFormat().resolvedOptions().timeZone
};

// Use with all API calls
fetch('/v1/api/endpoint', {
    method: 'POST',
    headers: {
        ...commonHeaders,
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
});
```

#### Mobile App Example
```javascript
// React Native
const commonHeaders = {
    'x-request-id': generateUUID(),
    'x-session-id': await AsyncStorage.getItem('sessionId'),
    'x-device-id': await DeviceInfo.getUniqueId(),
    'x-client-version': await DeviceInfo.getVersion(),
    'x-platform': Platform.OS, // 'ios' or 'android'
    'x-timezone': await DeviceInfo.getTimezone()
};
```

### Backend Usage

#### Simple Header Access
```python
from app.core.headers import CommonHeaders
from app.core.context import RequestContext

@router.get("/my-endpoint")
async def my_endpoint(context: RequestContext = CommonHeaders):
    request_id = context.request_id
    user_ip = context.ip_address
    platform = context.platform
    return {"request_id": request_id}
```

#### With Authentication
```python
from app.core.auth_dependencies import AuthenticatedUser, RequireAuth

@router.get("/protected")
async def protected_endpoint(user: AuthenticatedUser = RequireAuth):
    return {
        "user": user.email,
        "request_id": user.context.request_id,
        "client_platform": user.context.platform
    }
```

## 📊 API Endpoints

### Core APIs

#### File Upload & Processing
- `POST /v1/data/file-upload` - Upload CSV files with automatic chart recommendations
- Enhanced with client tracking and request correlation

#### Chart Insights
- `POST /v1/chart/insights` - Generate comprehensive AI-powered chart insights
- Supports executive summaries, data insights, hidden patterns, and recommendations
- Multi-LLM provider support (OpenAI, Google Gemini, Mistral, OpenRouter)

#### Authentication
- `POST /v1/auth/google` - Google OAuth authentication
- `POST /v1/auth/refresh-token` - Refresh access tokens
- `POST /v1/auth/debug-login` - Debug authentication (development only)

#### Health & Monitoring
- `GET /v1/health` - Health check with client information
- `GET /v1/chart/insights/health` - Chart insights service health
- `GET /v1/chart/insights/providers` - Available LLM providers

#### Analytics & Events
- `POST /v1/analytics/*` - Event tracking and user analytics
- Enhanced with common headers for better user profiling

### Example Endpoints (for testing)
- `GET /v1/example/public` - Test basic header processing
- `GET /v1/example/headers-summary` - View all processed headers
- `GET /v1/example/authenticated` - Test authenticated endpoints
- `POST /v1/example/analytics` - Test analytics integration

## 📦 Project Structure

```
infocharts_backend/
├── app/
│   ├── api/v1/endpoints/          # API route handlers
│   │   ├── authentication.py     # Auth endpoints with common headers
│   │   ├── analytics.py          # Analytics and event tracking
│   │   ├── chart_insights.py     # AI-powered chart insights
│   │   ├── csv_upload.py         # File upload with header tracking
│   │   ├── health.py             # Health checks with client info
│   │   └── example_common_headers.py  # Example implementations
│   ├── core/                     # Core system components
│   │   ├── context.py            # Request context and header management
│   │   ├── headers.py            # Common headers dependency system
│   │   ├── middleware.py         # Automatic header processing
│   │   ├── auth_dependencies.py  # Standardized authentication
│   │   ├── config.py             # Configuration management
│   │   ├── exceptions.py         # Custom exception handling
│   │   └── logging_config.py     # Structured logging setup
│   ├── models/                   # Data models and schemas
│   ├── services/                 # Business logic and integrations
│   │   ├── chart_insights/       # Modular chart insights system
│   │   ├── analytics/            # Analytics and user tracking
│   │   ├── authentication/       # Auth services and JWT handling
│   │   └── csv_service_v2.py     # CSV processing service
│   ├── utils/                    # Utility functions
│   └── main.py                   # FastAPI application entry point
├── docs/                         # Documentation
├── tests/                        # Test suites
├── sample_data/                  # Sample CSV files for testing
└── requirements.txt              # Python dependencies
```

## 🧪 Testing

### Automated Tests

```bash
# Test the common headers system
python test_common_headers_system.py

# Test all updated APIs
python test_updated_apis.py

# Test client header integration
python test_client_headers.py

# Test server startup
python test_server_startup.py
```

### Manual Testing

```bash
# Test endpoints with curl
curl -H "x-request-id: test-123" \
     -H "x-platform: web" \
     -H "x-client-version: 1.0.0" \
     http://localhost:8000/v1/health

# View all available headers
curl http://localhost:8000/v1/example/headers-summary

# Test file upload (requires authentication)
curl -X POST \
     -H "Authorization: Bearer your-token" \
     -H "x-request-id: upload-test" \
     -F "file=@sample_data/sales_order.csv" \
     http://localhost:8000/v1/data/file-upload
```

## 🔧 Adding New Common Headers

To add a new header (e.g., `x-new-header`):

### 1. Update Context (`app/core/context.py`)
```python
@dataclass
class RequestContext:
    # ... existing fields ...
    new_header: str = ""
```

### 2. Update Headers (`app/core/headers.py`)
```python
async def get_common_headers(
    # ... existing headers ...
    x_new_header: Optional[str] = Header(None, alias="x-new-header")
):
    context = RequestContext(
        # ... existing fields ...
        new_header=sanitize_header_value(x_new_header)
    )
```

### 3. Update Middleware (`app/core/middleware.py`)
```python
context = RequestContext(
    # ... existing fields ...
    new_header=sanitize_header_value(headers.get('x-new-header'))
)
```

That's it! The new header is now available in all endpoints.

## 🐳 Docker Setup (Optional)

```bash
# Build the Docker image
docker build -t infocharts-backend .

# Run the Docker container
docker run -d -p 8000:8000 --env-file .env infocharts-backend

# Access the application
open http://127.0.0.1:8000/docs
```

## 📊 Benefits

### Enhanced Observability
- **🔍 Request Tracking** - Every request has a unique ID for debugging
- **📈 Enhanced Analytics** - Consistent client data across all endpoints
- **🛡️ Better Security** - Client fingerprinting and fraud detection
- **🐛 Easier Debugging** - Request correlation and client context

### Developer Experience
- **🔗 Centralized Management** - Add new headers once, available everywhere
- **🔄 Backward Compatible** - Existing APIs continue to work unchanged
- **📝 Type-Safe** - RequestContext objects with full type hints
- **🧪 Comprehensive Testing** - Full test coverage for all features

### Production Ready
- **⚡ Performance** - Optimized header processing with minimal overhead
- **🔒 Security** - Automatic header sanitization and validation
- **📊 Monitoring** - Built-in health checks and service status
- **🔧 Configurable** - Environment-based configuration for all settings

## 🚀 Next Steps

### Frontend Integration
1. Update your frontend to send standard headers
2. Use the provided integration examples
3. Test with the example endpoints
4. Monitor enhanced analytics data

### Advanced Features
1. Implement role-based access control
2. Add custom business-specific headers
3. Enhance analytics with custom event tracking
4. Set up monitoring and alerting

## 📚 Documentation

- **Interactive API Docs**: http://127.0.0.1:8000/docs
- **Common Headers Guide**: Complete system documentation
- **UI Integration Examples**: React, Vue, Angular, React Native
- **Testing Scripts**: Comprehensive test coverage

## 📝 License

This project is open source and available under the MIT License.


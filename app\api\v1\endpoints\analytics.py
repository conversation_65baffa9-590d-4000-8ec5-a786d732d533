from fastapi import <PERSON>Router, Depends, HTTPException, status, Request
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional
from app.models.analytics import (
    EventTrackingRequest,
    AnalyticsResponse,
    EventType,
    FileUploadEventRequest,
    ChartEventRequest,
    PageViewEventRequest
)
from app.services.analytics.event_tracking_service import EventTrackingService
from app.services.analytics.user_profile_service import UserProfileService
from app.services.authentication.token_service import TokenService
from app.core.logging_config import configure_logging

logger = configure_logging()

router = APIRouter()
security = HTTPBearer()

# Initialize services
event_tracking_service = EventTrackingService()
user_profile_service = UserProfileService()


# CORS preflight handlers
@router.options("/analytics/events")
async def preflight_track_event():
    return JSONResponse(status_code=200)


@router.options("/analytics/events/file-upload")
async def preflight_track_file_upload():
    return JSONResponse(status_code=200)


@router.options("/analytics/events/chart")
async def preflight_track_chart():
    return JSONResponse(status_code=200)


@router.options("/analytics/events/page-view")
async def preflight_track_page_view():
    return JSONResponse(status_code=200)


@router.options("/analytics/user-profile")
async def preflight_user_profile():
    return JSONResponse(status_code=200)


@router.options("/analytics/health")
async def preflight_health():
    return JSONResponse(status_code=200)


def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Extract user email from JWT token"""
    try:
        if not credentials:
            logger.warning("No credentials provided")
            raise HTTPException(status_code=401, detail="Authorization header missing")

        token = credentials.credentials
        if not token:
            logger.warning("No token in credentials")
            raise HTTPException(status_code=401, detail="Token missing")

        logger.debug(f"Verifying token: {token[:20]}...")
        token_data = TokenService.verify_access_token(token)
        logger.debug(f"Token verified for user: {token_data.sub}")
        return token_data.sub
    except HTTPException as e:
        logger.warning(f"Authentication failed: {e.status_code} - {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Unexpected error during token verification: {str(e)}")
        raise HTTPException(status_code=401, detail="Invalid or expired access token")


def get_current_user_optional(credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))):
    """Extract user email from JWT token (optional for health checks)"""
    if not credentials:
        return None

    token = credentials.credentials
    try:
        token_data = TokenService.verify_access_token(token)
        return token_data.sub
    except HTTPException:
        return None
    except Exception as e:
        logger.error(f"Unexpected error during token verification: {str(e)}")
        return None


@router.post("/analytics/events", response_model=AnalyticsResponse)
async def track_event(
    request: Request,
    event_request: EventTrackingRequest,
    current_user: str = Depends(get_current_user)
):
    """
    Track user events from the UI
    
    This endpoint allows the frontend to send various user interaction events
    for analytics tracking. Events are stored asynchronously in Google Sheets.
    """
    try:
        logger.info(f"Tracking event: {event_request.event_type} for user: {current_user}")
        
        # Extract request information
        request_info = event_tracking_service.extract_request_info(request)
        
        # Use provided values or fall back to extracted values
        user_agent = event_request.user_agent or request_info.get('user_agent')
        ip_address = event_request.ip_address or request_info.get('ip_address')
        page_url = event_request.page_url or request_info.get('page_url')
        referrer = event_request.referrer or request_info.get('referrer')
        
        # Track the event asynchronously
        await event_tracking_service.track_event_async(
            email=current_user,
            event_type=event_request.event_type,
            metadata=event_request.metadata,
            user_agent=user_agent,
            ip_address=ip_address,
            session_id=event_request.session_id,
            page_url=page_url,
            referrer=referrer
        )
        
        return AnalyticsResponse(
            success=True,
            message=f"Event {event_request.event_type} tracked successfully"
        )
        
    except Exception as e:
        logger.error(f"Error tracking event: {str(e)}")
        return AnalyticsResponse(
            success=False,
            message=f"Failed to track event: {str(e)}"
        )


@router.post("/analytics/events/file-upload", response_model=AnalyticsResponse)
async def track_file_upload_event(
    request: Request,
    event_request: FileUploadEventRequest,
    current_user: str = Depends(get_current_user)
):
    """
    Track file upload events with specific metadata

    This endpoint is called when a user uploads a CSV/Excel file.
    """
    try:
        logger.info(f"Tracking file upload event for user: {current_user}")

        # Extract request information
        request_info = event_tracking_service.extract_request_info(request)

        # Use provided values or fall back to extracted values
        user_agent = event_request.user_agent or request_info.get('user_agent')
        ip_address = event_request.ip_address or request_info.get('ip_address')

        # Track the file upload event
        event_id = await event_tracking_service.track_file_upload_event(
            email=current_user,
            file_name=event_request.file_name,
            file_size=event_request.file_size,
            file_type=event_request.file_type,
            user_agent=user_agent,
            ip_address=ip_address,
            session_id=event_request.session_id
        )

        return AnalyticsResponse(
            success=True,
            message="File upload event tracked successfully",
            event_id=event_id
        )

    except Exception as e:
        logger.error(f"Error tracking file upload event: {str(e)}")
        return AnalyticsResponse(
            success=False,
            message=f"Failed to track file upload event: {str(e)}"
        )


@router.post("/analytics/events/chart", response_model=AnalyticsResponse)
async def track_chart_event(
    request: Request,
    event_request: ChartEventRequest,
    current_user: str = Depends(get_current_user)
):
    """
    Track chart-related events (save, download, view, update)

    This endpoint is called for various chart interactions.
    """
    try:
        # Validate that the event type is chart-related
        chart_event_types = [
            EventType.CHART_COPY,
            EventType.CHART_DOWNLOAD,
            EventType.CHART_RENDER,
            EventType.CHART_UPDATE,
            EventType.ADDITIONAL_CHARTS_VIEW,
            EventType.CHART_INSIGHTS
        ]

        if event_request.event_type not in chart_event_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid chart event type: {event_request.event_type}"
            )

        logger.info(f"Tracking chart event: {event_request.event_type} for user: {current_user}")

        # Extract request information
        request_info = event_tracking_service.extract_request_info(request)

        # Use provided values or fall back to extracted values
        user_agent = event_request.user_agent or request_info.get('user_agent')
        ip_address = event_request.ip_address or request_info.get('ip_address')

        # Track the chart event
        event_id = await event_tracking_service.track_chart_event(
            email=current_user,
            event_type=event_request.event_type,
            chart_type=event_request.chart_type,
            chart_id=event_request.chart_id,
            user_agent=user_agent,
            ip_address=ip_address,
            session_id=event_request.session_id,
            page_url=request_info.get('page_url')
        )

        return AnalyticsResponse(
            success=True,
            message=f"Chart event {event_request.event_type} tracked successfully",
            event_id=event_id
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error tracking chart event: {str(e)}")
        return AnalyticsResponse(
            success=False,
            message=f"Failed to track chart event: {str(e)}"
        )


@router.post("/analytics/events/page-view", response_model=AnalyticsResponse)
async def track_page_view_event(
    request: Request,
    event_request: PageViewEventRequest,
    current_user: str = Depends(get_current_user)
):
    """
    Track page view events

    This endpoint is called when a user navigates to different pages.
    """
    try:
        logger.info(f"Tracking page view event for user: {current_user}")

        # Extract request information
        request_info = event_tracking_service.extract_request_info(request)

        # Use provided values or fall back to extracted values
        user_agent = event_request.user_agent or request_info.get('user_agent')
        ip_address = event_request.ip_address or request_info.get('ip_address')
        referrer = event_request.referrer or request_info.get('referrer')

        # Track the page view event
        event_id = await event_tracking_service.track_page_view_event(
            email=current_user,
            page_url=event_request.page_url,
            page_title=event_request.page_title,
            user_agent=user_agent,
            ip_address=ip_address,
            session_id=event_request.session_id,
            referrer=referrer
        )

        return AnalyticsResponse(
            success=True,
            message="Page view event tracked successfully",
            event_id=event_id
        )

    except Exception as e:
        logger.error(f"Error tracking page view event: {str(e)}")
        return AnalyticsResponse(
            success=False,
            message=f"Failed to track page view event: {str(e)}"
        )


@router.get("/analytics/user-profile")
async def get_user_profile(request: Request, current_user: str = Depends(get_current_user)):
    """
    Get user profile analytics data

    Returns the user's profile information stored in Google Sheets.
    """
    try:
        logger.info(f"Retrieving user profile for: {current_user}")
        logger.debug(f"Request headers: {dict(request.headers)}")

        # Get user profile from Google Sheets
        user_profile = await user_profile_service.get_user_profile(current_user)

        if user_profile:
            return JSONResponse(content={
                "success": True,
                "data": user_profile
            })
        else:
            return JSONResponse(content={
                "success": False,
                "message": "User profile not found"
            })

    except HTTPException as e:
        logger.error(f"HTTP Exception retrieving user profile: {e.status_code} - {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Error retrieving user profile: {str(e)}")
        return JSONResponse(content={
            "success": False,
            "message": f"Failed to retrieve user profile: {str(e)}"
        })


@router.get("/analytics/health")
async def analytics_health_check():
    """
    Check the health of analytics services

    Returns the status of Google Sheets integration and analytics services.
    """
    try:
        sheets_available = event_tracking_service.sheets_service.is_available()

        return JSONResponse(content={
            "success": True,
            "services": {
                "google_sheets": {
                    "available": sheets_available,
                    "status": "connected" if sheets_available else "disconnected"
                },
                "analytics_enabled": True  # Based on settings.ENABLE_ANALYTICS
            }
        })

    except Exception as e:
        logger.error(f"Error checking analytics health: {str(e)}")
        return JSONResponse(content={
            "success": False,
            "message": f"Analytics health check failed: {str(e)}"
        })


@router.get("/analytics/debug/auth")
async def debug_auth(request: Request, current_user: str = Depends(get_current_user)):
    """
    Debug endpoint to test authentication

    This endpoint helps debug authentication issues.
    """
    try:
        logger.info(f"Debug auth endpoint accessed by user: {current_user}")

        return JSONResponse(content={
            "success": True,
            "user": current_user,
            "message": "Authentication successful",
            "headers": dict(request.headers)
        })

    except HTTPException as e:
        logger.error(f"Authentication failed in debug endpoint: {e.status_code} - {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Unexpected error in debug auth endpoint: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"Unexpected error: {str(e)}"
            }
        )

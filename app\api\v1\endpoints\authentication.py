from fastapi import API<PERSON>outer, Depends, HTTPException, status, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Optional
from app.services.authentication.auth_service import AuthService
from app.services.authentication.token_service import TokenService
from app.services.analytics.user_profile_service import UserProfileService
from app.services.analytics.event_tracking_service import EventTrackingService
from app.models.analytics import EventType
from app.core.config import settings
from app.core.headers import CommonHeaders, get_headers_for_analytics
from app.core.context import RequestContext
from app.core.logging_config import configure_logging

logger = configure_logging()

router = APIRouter()

# Initialize analytics services
user_profile_service = UserProfileService()
event_tracking_service = EventTrackingService()

class GoogleLoginRequest(BaseModel):
    token: str
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    location: Optional[str] = None
    session_id: Optional[str] = None

class RefreshTokenRequest(BaseModel):
    refresh_token: str

@router.options("/auth/google")
async def preflight_auth_google():
    return JSONResponse(status_code=200)


@router.post("/auth/google")
async def google_login(
    request: Request,
    payload: GoogleLoginRequest,
    context: RequestContext = CommonHeaders
):
    """
    Google OAuth login endpoint with common headers integration.

    Now automatically extracts client information from standard headers
    while maintaining backward compatibility with payload fields.
    """
    logger.info(f"Google login attempt [{context.request_id}] from {context.ip_address}")

    # Validate Google token
    google_data = AuthService.verify_google_token(payload.token)

    email = google_data.get("email")
    if not email:
        logger.warning(f"Google token missing email [{context.request_id}]")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Google token missing email")

    # Use common headers with fallback to payload and legacy request extraction
    request_info = user_profile_service.extract_user_info_from_request(request)
    user_agent = payload.user_agent or context.user_agent or request_info.get('user_agent')
    ip_address = payload.ip_address or context.ip_address or request_info.get('ip_address')
    location = payload.location or request_info.get('location')

    # Get analytics-friendly headers
    analytics_headers = get_headers_for_analytics(context)

    logger.debug(f"Login for {email} - platform: {context.platform}, version: {context.client_version}")

    # Track user profile asynchronously (non-blocking)
    await user_profile_service.track_user_login_async(
        email=email,
        google_user_data=google_data,
        user_agent=user_agent,
        ip_address=ip_address,
        location=location
    )

    # Track login event asynchronously (non-blocking)
    await event_tracking_service.track_event_async(
        email=email,
        event_type=EventType.USER_LOGIN,
        metadata={'login_method': 'google_oauth'},
        user_agent=user_agent,
        ip_address=ip_address,
        session_id=payload.session_id
    )

    # Create access and refresh tokens
    tokens = AuthService.create_user_tokens(email)
    return JSONResponse(content=tokens)


@router.post("/auth/refresh-token")
def refresh_access_token(payload: RefreshTokenRequest):
    # Verify refresh token validity
    token_data = TokenService.verify_refresh_token(payload.refresh_token)

    # Create new access token
    access_token = TokenService.create_access_token({"sub": token_data.sub})

    return JSONResponse(content={
        "access_token": access_token,
        "token_type": "bearer"
    })

@router.post("/auth/debug-login")
def debug_login(payload: dict):
    """
    Debug endpoint for testing authentication without Google OAuth.
    Send: {"email": "<EMAIL>"}
    WARNING: This should only be used for development/debugging!
    """
    email = payload.get("email", "<EMAIL>")

    # Create access and refresh tokens
    tokens = AuthService.create_user_tokens(email)
    return JSONResponse(content=tokens)

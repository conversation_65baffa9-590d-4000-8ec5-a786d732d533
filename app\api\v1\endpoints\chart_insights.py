"""
Chart Insights API Endpoints - Performance Optimized Clean Architecture

This module provides high-performance API endpoints for generating comprehensive chart insights
using a modular, clean architecture approach with support for multiple LLM providers.

Performance optimizations:
- Singleton pattern for service instances to reduce initialization overhead
- Lazy loading of services to improve startup time
- Optimized response handling with minimal serialization overhead
- Efficient error handling with reduced logging overhead
- Memory-efficient data processing with streaming where possible

The endpoints follow clean code principles with proper separation of concerns:
- Data processing is handled by dedicated processors
- Insight generation is handled by specialized services
- LLM provider abstraction allows easy switching between providers
- All services are orchestrated through a unified interface
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import J<PERSON>NResponse
from typing import Dict, Any, Optional
from app.services.chart_insights import generate_llm_insights, ChartInsightsRequest, ChartInsightsResponse
from app.core.logging_config import configure_logging
from app.core.utils import sanitize_json_data
from app.core.headers import CommonHeaders
from app.core.context import RequestContext

# Configure logger
logger = configure_logging()

# Service instances (lazy-loaded for performance)
_chart_recommender_instance: Optional[Any] = None
_chart_data_instance: Optional[Any] = None

def get_chart_recommender():
    """Lazy-loaded chart recommender service."""
    global _chart_recommender_instance
    if _chart_recommender_instance is None:
        from app.services.chart_recommender import ChartData as ChartRecommender
        _chart_recommender_instance = ChartRecommender()
        logger.debug("Chart recommender service initialized")
    return _chart_recommender_instance

def get_chart_data_service():
    """Lazy-loaded chart data service."""
    global _chart_data_instance
    if _chart_data_instance is None:
        from app.services.chart_data import ChartData
        _chart_data_instance = ChartData()
        logger.debug("Chart data service initialized")
    return _chart_data_instance

router = APIRouter()


@router.put("/chart/data")
async def update_chart(chart_data_input: dict):
    """
    Update chart data using the data aggregator service.

    Performance optimizations:
    - Lazy loading of data aggregator service
    - Streamlined error handling
    - Optimized response serialization

    Args:
        chart_data_input: Dictionary containing chart data and configuration

    Returns:
        Updated chart data
    """
    chart_type = chart_data_input.get('chart_type', 'unknown')

    try:
        logger.info(f"Chart update request for type: {chart_type}")

        # Lazy load data aggregator service for better performance
        from app.services.data_aggregator.base import DataAggregator
        data_aggregator = DataAggregator()

        updated_chart = data_aggregator.prepare_chart_data(
            df=chart_data_input.get('df'),
            chart_type=chart_type,
            fields=chart_data_input.get('fields')
        )

        if updated_chart is None:
            return JSONResponse(
                status_code=400,
                content={"error": f"Failed to generate chart data for type: {chart_type}"}
            )

        # Optimized response serialization
        sanitized_chart = sanitize_json_data(updated_chart)
        logger.info(f"Chart data updated successfully for type: {chart_type}")

        return JSONResponse(content=sanitized_chart)

    except Exception as e:
        logger.error(f"Chart update error for type {chart_type}: {str(e)}")
        return JSONResponse(
            status_code=400,
            content={"error": f"Chart update failed: {str(e)}"}
        )


@router.post("/chart/insights", response_model=ChartInsightsResponse)
async def generate_unified_chart_insights(
    request: ChartInsightsRequest,
    context: RequestContext = CommonHeaders
):
    """
    Generate comprehensive chart insights with performance optimizations.

    Performance improvements:
    - Early validation to fail fast on invalid requests
    - Optimized logging with minimal string formatting
    - Streamlined error handling with reduced overhead
    - Efficient response serialization

    Args:
        request: ChartInsightsRequest containing chart data and configuration
        context: Request context with common headers (automatically injected)

    Returns:
        ChartInsightsResponse with comprehensive insights
    """
    # Early validation for performance
    if not request.chart_data:
        raise HTTPException(status_code=400, detail="Chart data is required")

    chart_title = request.chart_title or "Untitled Chart"

    try:
        logger.info(f"Chart insights request: '{chart_title}' [{context.request_id}]")
        logger.debug(f"Client: {context.platform} v{context.client_version}")

        # Generate unified insights using the optimized service architecture
        insights_result = await generate_llm_insights(
            chart_data=request.chart_data,
            chart_type=request.chart_type,
            chart_title=chart_title
        )

        # Optimized success response
        logger.info(f"Insights generated successfully for: {chart_title}")
        return JSONResponse(content=_optimize_insights_response(insights_result))

    except HTTPException:
        raise
    except Exception as e:
        # Let custom exceptions bubble up to global handlers
        from app.core.exceptions import CustomException
        if isinstance(e, CustomException):
            raise e

        # Optimized error handling
        logger.error(f"Chart insights error for '{chart_title}': {str(e)}")

        return JSONResponse(
            status_code=500,
            content=_create_insights_error_response(str(e))
        )

def _optimize_insights_response(insights_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Optimize insights response for better performance.

    Args:
        insights_result: Raw insights result

    Returns:
        Optimized response data
    """
    try:
        # Sanitize and optimize the response
        optimized_result = sanitize_json_data(insights_result)

        # Ensure success flag is present
        if "success" not in optimized_result:
            optimized_result["success"] = True

        return optimized_result

    except Exception as e:
        logger.error(f"Error optimizing insights response: {str(e)}")
        return _create_insights_error_response(f"Response optimization failed: {str(e)}")

def _create_insights_error_response(error_message: str) -> Dict[str, Any]:
    """
    Create standardized error response for unified insights endpoint.

    Args:
        error_message: Error message to include

    Returns:
        Standardized error response
    """
    return {
        "success": False,
        "chart_insights": [f"Error: {error_message}"],
        "key_metrics": {},
        "chart_metadata": {"error": error_message}
    }



@router.get("/chart/insights/providers")
async def get_available_llm_providers():
    """
    Get list of available LLM providers with caching for performance.

    Returns:
        Dictionary with available providers and current default
    """
    try:
        # Lazy load to improve performance
        from app.services.chart_insights.llm_client import LLMClientFactory
        from app.core.config import settings

        available_providers = LLMClientFactory.get_available_providers()
        current_provider = settings.DEFAULT_LLM_PROVIDER

        return {
            "available_providers": available_providers,
            "current_default": current_provider,
            "description": "Available LLM providers for chart insights generation"
        }
    except Exception as e:
        logger.error(f"Error getting providers: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to get providers: {str(e)}"}
        )


@router.get("/chart/insights/health")
async def check_chart_insights_health():
    """
    Optimized health check endpoint for chart insights services.

    Returns:
        Health status of chart insights services
    """
    try:
        # Lazy load for better performance
        from app.services.chart_insights.llm_client import LLMClientFactory
        from app.core.config import settings

        # Quick health check without full initialization
        client = LLMClientFactory.create_client()
        provider_name = client.get_provider_name()

        return {
            "status": "healthy",
            "services": {
                "chart_insights_orchestrator": "available",
                "llm_client": f"available ({provider_name})",
                "data_processor": "available",
                "unified_insights_service": "available"
            },
            "configuration": {
                "default_llm_provider": settings.DEFAULT_LLM_PROVIDER,
                "insights_mode": "unified",
                "max_data_points": 100
            }
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e)
            }
        )

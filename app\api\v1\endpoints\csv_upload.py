from typing import Optional, Dict, Any
from fastapi import APIRouter, Upload<PERSON><PERSON>, File, Header, Security, HTTPException
from fastapi.responses import JSONResponse
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from app.services.csv_service_v2 import CsvServiceV2
from app.services.file_validator import FileValidator
from app.services.authentication.token_service import TokenService
from app.core.exceptions import DataProcessingError, FileUploadError, FileSizeExceededError, InvalidFileTypeError
from app.core.logging_config import configure_logging
from app.core.utils import sanitize_json_data
from app.core.context import set_context
from app.core.auth_dependencies import AuthenticatedUser, RequireAuth
from app.core.headers import CommonHeaders
from app.core.context import RequestContext

# Configure logger
logger = configure_logging()

# Initialize router
router = APIRouter()

# Initialize services with singleton pattern for performance
_csv_service_instance: Optional[CsvServiceV2] = None
_file_validator_instance: Optional[FileValidator] = None

def get_csv_service() -> CsvServiceV2:
    """
    Get CSV service instance using singleton pattern for better performance.
    Avoids recreating service instances on every request.
    """
    global _csv_service_instance
    if _csv_service_instance is None:
        _csv_service_instance = CsvServiceV2()
        logger.debug("CSV service instance created")
    return _csv_service_instance

def get_file_validator() -> FileValidator:
    """
    Get file validator instance using singleton pattern for better performance.
    Avoids recreating validator instances on every request.
    """
    global _file_validator_instance
    if _file_validator_instance is None:
        _file_validator_instance = FileValidator()
        logger.debug("File validator instance created")
    return _file_validator_instance

def _optimize_response_data(result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Optimize response data for better performance and JSON serialization.

    Args:
        result: Raw result from CSV service

    Returns:
        Optimized and sanitized result
    """
    try:
        # Pre-sanitize the data for JSON serialization
        sanitized_result = sanitize_json_data(result)

        # Remove any legacy fields that are no longer needed (performance optimization)
        if "analytics" in sanitized_result:
            del sanitized_result["analytics"]
        if "correlations" in sanitized_result:
            del sanitized_result["correlations"]
        if "data_quality" in sanitized_result:
            del sanitized_result["data_quality"]

        return sanitized_result

    except Exception as e:
        logger.error(f"Error optimizing response data: {str(e)}")
        # Fallback to minimal response structure
        return {
            "charts": result.get("charts", []),
            "error": f"Response optimization failed: {str(e)}"
        }

security = HTTPBearer()

def get_current_user(credentials: HTTPAuthorizationCredentials = Security(security)):
    token = credentials.credentials
    logger.debug(f"Verifying access token for user authentication")
    try:
        token_data = TokenService.verify_access_token(token)
        logger.debug(f"User authenticated successfully: {token_data.sub}")
        return token_data.sub
    except HTTPException:
        # Re-raise HTTPException from TokenService
        raise
    except Exception as e:
        # Log unexpected errors and raise HTTPException
        logger.error(f"Unexpected error during token verification: {str(e)}")
        raise HTTPException(status_code=401, detail="Invalid or expired access token")

@router.post("/data/file-upload")
async def upload_csv(
    file: UploadFile = File(...),
    user: AuthenticatedUser = RequireAuth,
    context: RequestContext = CommonHeaders,
    # Legacy headers for backward compatibility
    x_interaction_id: str = Header(None, alias="X-Interaction-Id"),
    x_session_id: str = Header(None, alias="X-Session-Id"),
    x_device_id: str = Header(None, alias="X-Device-Id")
):
    """
    Handles CSV file upload with comprehensive validation, processes the data, and returns chart recommendations.

    Performance optimizations:
    - Singleton service instances for reduced initialization overhead
    - Early file validation to fail fast on invalid files
    - Streamlined error handling for faster response times
    - Optimized JSON serialization with pre-validation
    - Reduced memory footprint through efficient data handling

    File validation includes:
    - File size validation (configurable via MAX_FILE_SIZE_MB)
    - File type validation (configurable via ALLOWED_FILE_TYPES)
    - Basic security validation to prevent malicious uploads
    """
    # Get service instances (singleton pattern for performance)
    csv_service = get_csv_service()
    file_validator = get_file_validator()

    # Use new context system (automatically set by middleware) or fall back to legacy headers
    request_id = context.request_id or x_interaction_id

    # Set legacy context for backward compatibility (only if needed)
    if x_interaction_id or x_session_id or x_device_id:
        set_context(interaction_id=x_interaction_id, session_id=x_session_id, device_id=x_device_id)

    logger.info(f"File upload request from {user.email} - request_id={request_id}, filename={file.filename}")
    logger.debug(f"Client: {context.platform} v{context.client_version} from {context.ip_address}")

    try:
        # Step 1: Validate file (size, type, security) - fail fast approach
        await file_validator.validate_file(file)
        logger.info(f"File validation successful: {file.filename}")

        # Step 2: Process CSV file with optimized service
        result = await csv_service.handle_csv_upload(file)
        logger.info("Chart data successfully generated")

        # Step 3: Optimized JSON serialization with pre-validation
        sanitized_result = _optimize_response_data(result)

        return JSONResponse(
            content=sanitized_result,
            status_code=200
        )

    except FileSizeExceededError as e:
        logger.error(f"File Size Exceeded: {str(e)}")
        return _create_error_response(str(e), 413)  # 413 Payload Too Large

    except InvalidFileTypeError as e:
        logger.error(f"Invalid File Type: {str(e)}")
        return _create_error_response(str(e), 415)  # 415 Unsupported Media Type

    except FileUploadError as e:
        logger.error(f"File Upload Error: {str(e)}")
        return _create_error_response(str(e), 400)  # 400 Bad Request

    except DataProcessingError as e:
        logger.error(f"Data Processing Error: {str(e)}")
        return _create_error_response(str(e), 422)  # 422 Unprocessable Entity

    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return _create_error_response(f"An unexpected error occurred: {str(e)}", 500)

def _create_error_response(error_message: str, status_code: int) -> JSONResponse:
    """
    Create optimized error response with consistent structure.

    Args:
        error_message: Error message to include
        status_code: HTTP status code

    Returns:
        JSONResponse with error details
    """
    error_content = {
        "charts": [],
        "error": error_message,
        "success": False
    }
    return JSONResponse(
        status_code=status_code,
        content=error_content  # No need to sanitize simple error structure
    )


@router.get("/data/file-upload/validation-info")
async def get_file_validation_info(
    user: AuthenticatedUser = RequireAuth
):
    """
    Get file validation configuration information.

    This endpoint provides information about file upload limits and allowed types
    for client-side validation and user information.

    Returns:
        Dictionary with file validation settings
    """
    try:
        file_validator = get_file_validator()
        validation_info = file_validator.get_validation_info()

        logger.info(f"File validation info requested by {user.email}")

        return JSONResponse(
            content={
                "success": True,
                "validation_settings": validation_info,
                "message": "File validation configuration retrieved successfully"
            },
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting file validation info: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Failed to get validation info: {str(e)}"
            }
        )



"""
Example API endpoint demonstrating the new common header system.

This file shows how to use the centralized header management system
across different types of API endpoints.
"""

from fastapi import APIRouter, Depends
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import Optional
from app.core.headers import CommonHeaders, get_headers_for_analytics, get_headers_for_logging
from app.core.auth_dependencies import (
    AuthenticatedUser, 
    RequireAuth, 
    OptionalAuth, 
    RequireAdmin,
    AnalyticsAuth
)
from app.core.context import Request<PERSON>ontext, get_request_context
from app.core.logging_config import configure_logging

logger = configure_logging()
router = APIRouter()

@router.get("/example/public")
async def public_endpoint_with_headers(
    context: RequestContext = CommonHeaders
):
    """
    Example public endpoint that uses common headers.
    
    This endpoint demonstrates how to access common headers
    without requiring authentication.
    """
    
    logger.info(f"Public endpoint accessed [{context.request_id}]")
    
    return JSONResponse(content={
        "message": "Public endpoint with common headers",
        "request_context": {
            "request_id": context.request_id,
            "session_id": context.session_id,
            "device_id": context.device_id,
            "user_agent": context.user_agent,
            "ip_address": context.ip_address,
            "client_version": context.client_version,
            "platform": context.platform,
            "timezone": context.timezone,
            "language": context.language
        }
    })

@router.get("/example/authenticated")
async def authenticated_endpoint(
    user: AuthenticatedUser = RequireAuth,
    context: RequestContext = CommonHeaders
):
    """
    Example authenticated endpoint using the new auth system.
    
    This endpoint demonstrates how to combine authentication
    with common header extraction.
    """
    
    logger.info(f"Authenticated endpoint accessed by {user.email} [{context.request_id}]")
    
    return JSONResponse(content={
        "message": "Authenticated endpoint with common headers",
        "user": {
            "email": user.email,
            "context": user.to_dict()
        },
        "request_context": context.to_dict()
    })

@router.get("/example/optional-auth")
async def optional_auth_endpoint(
    user: Optional[AuthenticatedUser] = OptionalAuth,
    context: RequestContext = CommonHeaders
):
    """
    Example endpoint with optional authentication.
    
    This endpoint works with or without authentication,
    demonstrating flexible auth handling.
    """
    
    if user:
        logger.info(f"Optional auth endpoint accessed by {user.email} [{context.request_id}]")
        message = f"Hello {user.email}! You are authenticated."
        user_info = user.to_dict()
    else:
        logger.info(f"Optional auth endpoint accessed anonymously [{context.request_id}]")
        message = "Hello anonymous user!"
        user_info = None
    
    return JSONResponse(content={
        "message": message,
        "user": user_info,
        "request_context": context.to_dict()
    })

@router.get("/example/admin")
async def admin_endpoint(
    user: AuthenticatedUser = RequireAdmin,
    context: RequestContext = CommonHeaders
):
    """
    Example admin-only endpoint.
    
    This endpoint requires admin privileges (currently all authenticated users).
    """
    
    logger.info(f"Admin endpoint accessed by {user.email} [{context.request_id}]")
    
    return JSONResponse(content={
        "message": "Admin endpoint accessed successfully",
        "user": user.to_dict(),
        "request_context": context.to_dict(),
        "admin_data": {
            "sensitive_info": "This is admin-only data",
            "system_status": "All systems operational"
        }
    })

@router.post("/example/analytics")
async def analytics_endpoint(
    event_data: dict,
    user_email: Optional[str] = AnalyticsAuth,
    context: RequestContext = CommonHeaders
):
    """
    Example analytics endpoint using the analytics auth dependency.
    
    This endpoint demonstrates how to handle analytics data
    with optional user identification.
    """
    
    # Convert context to analytics-friendly format
    analytics_headers = get_headers_for_analytics(context)
    
    # Convert context to logging-friendly format
    logging_headers = get_headers_for_logging(context)
    
    logger.info(f"Analytics event received [{context.request_id}]", extra=logging_headers)
    
    # Simulate analytics processing
    analytics_data = {
        "event": event_data,
        "user_email": user_email,
        "headers": analytics_headers,
        "timestamp": context.request_id,  # Using request_id as timestamp placeholder
        "processed": True
    }
    
    return JSONResponse(content={
        "message": "Analytics event processed",
        "analytics_data": analytics_data,
        "user_authenticated": user_email is not None
    })

@router.get("/example/context-demo")
async def context_demo():
    """
    Example endpoint showing how to access context without dependency injection.
    
    This demonstrates accessing the context that was set by middleware.
    """
    
    # Get context that was set by the middleware
    context = get_request_context()
    
    logger.info(f"Context demo accessed [{context.request_id}]")
    
    return JSONResponse(content={
        "message": "Context accessed via get_request_context()",
        "context": context.to_dict(),
        "note": "This context was set by CommonHeadersMiddleware"
    })

@router.get("/example/headers-summary")
async def headers_summary(
    context: RequestContext = CommonHeaders
):
    """
    Example endpoint that provides a summary of all available header information.
    
    This is useful for debugging and understanding what headers are available.
    """
    
    logger.info(f"Headers summary requested [{context.request_id}]")
    
    return JSONResponse(content={
        "message": "Common headers summary",
        "available_headers": {
            "tracking": {
                "request_id": context.request_id,
                "correlation_id": context.correlation_id,
                "session_id": context.session_id,
                "device_id": context.device_id
            },
            "client_info": {
                "user_agent": context.user_agent,
                "client_version": context.client_version,
                "platform": context.platform,
                "language": context.language,
                "timezone": context.timezone
            },
            "network_info": {
                "ip_address": context.ip_address,
                "referrer": context.referrer
            }
        },
        "usage_examples": {
            "analytics": "Use get_headers_for_analytics(context) for analytics data",
            "logging": "Use get_headers_for_logging(context) for structured logging",
            "authentication": "Use RequireAuth, OptionalAuth, or RequireAdmin dependencies",
            "context_access": "Use get_request_context() to access context anywhere"
        }
    })

# Health check endpoint for the example module
@router.get("/example/health")
async def example_health():
    """Health check for the example endpoints."""
    return JSONResponse(content={
        "status": "healthy",
        "module": "example_common_headers",
        "features": [
            "common_headers",
            "authentication_dependencies", 
            "context_management",
            "analytics_integration"
        ]
    })

from fastapi import APIRouter, Query
from app.core.config import Settings
from typing import Optional
from app.core.headers import CommonHeaders
from app.core.context import RequestContext
from app.core.logging_config import configure_logging

logger = configure_logging()

router = APIRouter()
settings = Settings()

@router.get("/health")
def health_check(context: RequestContext = CommonHeaders):
    """
    Health check endpoint with common headers integration.

    Now includes request tracking and client information.
    """
    logger.debug(f"Health check [{context.request_id}] from {context.ip_address}")

    return {
        "status": "healthy",
        "request_id": context.request_id,
        "client_info": {
            "platform": context.platform,
            "client_version": context.client_version,
            "user_agent": context.user_agent[:50] + "..." if len(context.user_agent) > 50 else context.user_agent
        }
    }

@router.options("/health")
def health_options():
    return {"status": "ok"}

@router.get("/cors-test")
def cors_test():
    return {"message": "CORS is working", "status": "success"}

@router.post("/debug-token")
def debug_token_creation():
    """
    Debug endpoint to create fresh tokens for testing.
    This helps verify that JWT functionality is working correctly.
    """
    from app.services.authentication.auth_service import AuthService
    from app.core.config import settings

    try:
        # Create fresh tokens
        tokens = AuthService.create_user_tokens("<EMAIL>")

        return {
            "status": "success",
            "message": "Fresh tokens created successfully",
            "jwt_config": {
                "algorithm": settings.JWT_ALGORITHM,
                "access_token_expire_minutes": settings.ACCESS_TOKEN_EXPIRE_MINUTES,
                "refresh_token_expire_days": settings.REFRESH_TOKEN_EXPIRE_DAYS,
                "secret_key_configured": bool(settings.JWT_SECRET_KEY),
                "refresh_secret_key_configured": bool(settings.JWT_REFRESH_SECRET_KEY)
            },
            "tokens": tokens
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e),
            "jwt_config": {
                "algorithm": settings.JWT_ALGORITHM,
                "secret_key_configured": bool(settings.JWT_SECRET_KEY),
                "refresh_secret_key_configured": bool(settings.JWT_REFRESH_SECRET_KEY)
            }
        }

@router.post("/verify-token")
def verify_token_endpoint(token_data: dict):
    """
    Debug endpoint to verify tokens.
    Send: {"access_token": "your_token_here"} or {"refresh_token": "your_token_here"}
    """
    from app.services.authentication.token_service import TokenService

    try:
        if "access_token" in token_data:
            token = token_data["access_token"]
            result = TokenService.verify_access_token(token)
            return {
                "status": "success",
                "token_type": "access",
                "subject": result.sub,
                "expires": result.exp.isoformat(),
                "message": "Access token is valid"
            }
        elif "refresh_token" in token_data:
            token = token_data["refresh_token"]
            result = TokenService.verify_refresh_token(token)
            return {
                "status": "success",
                "token_type": "refresh",
                "subject": result.sub,
                "expires": result.exp.isoformat(),
                "message": "Refresh token is valid"
            }
        else:
            return {
                "status": "error",
                "message": "Please provide either 'access_token' or 'refresh_token' in the request body"
            }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e),
            "token_type": "access" if "access_token" in token_data else "refresh"
        }

@router.get("/llm-status")
def llm_status(provider: Optional[str] = Query(None, description="LLM provider to check (mistral or gemini)")):
    """
    Check the status of the LLM service.

    Args:
        provider: Optional provider to check (mistral or gemini)
                 If not provided, uses the default provider from settings

    Returns:
        Status of the LLM service
    """
    try:
        # Initialize LLM client with specified or default provider
        llm_client = LLMClient(provider=provider)

        # Get model info
        model_info = llm_client.get_model_info()

        # Validate LLM
        success, message = llm_client.validate_llm()

        return {
            "status": "ok" if success else "error",
            "provider": model_info["provider"],
            "model": model_info["model"],
            "message": message,
            "default_provider": settings.DEFAULT_LLM_PROVIDER
        }
    except Exception as e:
        return {
            "status": "error",
            "provider": provider or settings.DEFAULT_LLM_PROVIDER,
            "message": str(e),
            "default_provider": settings.DEFAULT_LLM_PROVIDER
        }

"""
Common Authentication Dependencies

This module provides standardized authentication dependencies that work
with the common header system across all APIs.
"""

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional
from app.services.authentication.token_service import TokenService
from app.core.context import RequestContext, get_request_context
from app.core.headers import CommonHeaders
from app.core.logging_config import configure_logging

logger = configure_logging()

# HTTP Bearer security scheme
security = HTTPBearer()
optional_security = HTTPBearer(auto_error=False)

class AuthenticatedUser:
    """Represents an authenticated user with context information"""
    
    def __init__(self, email: str, context: RequestContext):
        self.email = email
        self.context = context
    
    @property
    def user_id(self) -> str:
        """Alias for email (user identifier)"""
        return self.email
    
    def to_dict(self) -> dict:
        """Convert to dictionary for logging/analytics"""
        return {
            "email": self.email,
            "request_id": self.context.request_id,
            "session_id": self.context.session_id,
            "device_id": self.context.device_id,
            "ip_address": self.context.ip_address,
            "user_agent": self.context.user_agent,
            "client_version": self.context.client_version,
            "platform": self.context.platform
        }

def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    context: RequestContext = CommonHeaders
) -> AuthenticatedUser:
    """
    Dependency to get the current authenticated user with full context.
    
    This dependency:
    1. Validates the JWT token
    2. Extracts user information
    3. Combines with request context
    4. Returns an AuthenticatedUser object
    
    Args:
        credentials: JWT token from Authorization header
        context: Request context with common headers
        
    Returns:
        AuthenticatedUser: Authenticated user with context
        
    Raises:
        HTTPException: If authentication fails
    """
    
    token = credentials.credentials
    
    try:
        # Verify the access token
        token_data = TokenService.verify_access_token(token)
        
        # Create authenticated user with context
        user = AuthenticatedUser(email=token_data.sub, context=context)
        
        logger.debug(
            f"User authenticated successfully: {user.email} "
            f"[{context.request_id}] from {context.ip_address}"
        )
        
        return user
        
    except HTTPException as e:
        # Log authentication failure with context
        logger.warning(
            f"Authentication failed: {e.detail} "
            f"[{context.request_id}] from {context.ip_address}"
        )
        raise
        
    except Exception as e:
        # Log unexpected authentication errors
        logger.error(
            f"Unexpected authentication error: {str(e)} "
            f"[{context.request_id}] from {context.ip_address}"
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired access token"
        )

def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(optional_security),
    context: RequestContext = CommonHeaders
) -> Optional[AuthenticatedUser]:
    """
    Optional authentication dependency for endpoints that work with or without auth.
    
    Args:
        credentials: Optional JWT token from Authorization header
        context: Request context with common headers
        
    Returns:
        Optional[AuthenticatedUser]: Authenticated user if token provided and valid, None otherwise
    """
    
    if not credentials:
        logger.debug(f"No authentication provided [{context.request_id}]")
        return None
    
    try:
        # Try to authenticate
        return get_current_user(credentials, context)
        
    except HTTPException:
        # Authentication failed, but this is optional
        logger.debug(
            f"Optional authentication failed [{context.request_id}] "
            f"from {context.ip_address}"
        )
        return None
        
    except Exception as e:
        # Log unexpected errors but don't fail the request
        logger.warning(
            f"Unexpected error in optional authentication: {str(e)} "
            f"[{context.request_id}]"
        )
        return None

def require_admin_user(
    user: AuthenticatedUser = Depends(get_current_user)
) -> AuthenticatedUser:
    """
    Dependency for endpoints that require admin privileges.
    
    Note: This is a placeholder for future admin role implementation.
    Currently, all authenticated users are considered admins.
    
    Args:
        user: Authenticated user
        
    Returns:
        AuthenticatedUser: Admin user
        
    Raises:
        HTTPException: If user is not an admin
    """
    
    # TODO: Implement proper role-based access control
    # For now, all authenticated users are considered admins
    
    logger.debug(f"Admin access granted to {user.email} [{user.context.request_id}]")
    return user

def get_user_for_analytics(
    user: Optional[AuthenticatedUser] = Depends(get_current_user_optional)
) -> Optional[str]:
    """
    Dependency specifically for analytics endpoints that need user email.
    
    Args:
        user: Optional authenticated user
        
    Returns:
        Optional[str]: User email if authenticated, None otherwise
    """
    
    return user.email if user else None

# Convenience aliases for common use cases
RequireAuth = Depends(get_current_user)
OptionalAuth = Depends(get_current_user_optional)
RequireAdmin = Depends(require_admin_user)
AnalyticsAuth = Depends(get_user_for_analytics)

def create_auth_dependency(required: bool = True, admin: bool = False):
    """
    Factory function to create custom authentication dependencies.
    
    Args:
        required: Whether authentication is required
        admin: Whether admin privileges are required
        
    Returns:
        Dependency function
    """
    
    if admin:
        return Depends(require_admin_user)
    elif required:
        return Depends(get_current_user)
    else:
        return Depends(get_current_user_optional)

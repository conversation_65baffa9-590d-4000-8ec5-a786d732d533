from pydantic_settings import BaseSettings
from pydantic import field_validator
from dotenv import load_dotenv
import os
from typing import Optional, Any, List

# Load environment variables from the .env file inside the config directory
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '../../.env'))

class Settings(BaseSettings):

    # CORS Settings
    ALLOWED_ORIGINS: str = None

    # Authentication settings 
    JWT_SECRET_KEY: Optional[str] = None
    JWT_REFRESH_SECRET_KEY: Optional[str] = None
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # LLM Settings
    DEFAULT_LLM_PROVIDER: str = "mistral"  # Options: "mistral", "openai", "gemini", "openrouter"
    MISTRAL_API_KEY: Optional[str] = None
    MISTRAL_MODEL: str = "mistral-large-latest"
    OPENROUTER_API_KEY: Optional[str] = None
    OPENROUTER_MODEL: str = "mistralai/devstral-small:free"
    USE_LLM: bool = False  # Default to rule-based approach

    # Chart Recommendation Settings
    CHART_RECOMMENDATION_TYPE: str = "rule_based"  # Options: "rule_based", "llm"

    # Chart Recommendation Limits - Individual Recommender Limits
    MAX_CHART_RECOMMENDATIONS: int = 100  # Overall maximum number of chart recommendations
    MAX_BASIC_CHART_RECOMMENDATIONS: int = 20  # Maximum basic charts (pie, bar, etc.)
    MAX_NUMERICAL_CHART_RECOMMENDATIONS: int = 10  # Maximum numerical charts (histogram, scatter, etc.)
    MAX_TIME_SERIES_CHART_RECOMMENDATIONS: int = 10  # Maximum time series charts
    MAX_STACKED_CHART_RECOMMENDATIONS: int = 10  # Maximum stacked charts
    MAX_MULTI_CHART_RECOMMENDATIONS: int = 10  # Maximum multi charts (multi_line, multi_area, combo)
    MAX_MULTI_GROUP_CHART_RECOMMENDATIONS: int = 10  # Maximum multi-group charts (grouped_bar)
    MAX_BOX_PLOT_CHART_RECOMMENDATIONS: int = 10  # Maximum box plot charts (box, violin)
    MAX_HEATMAP_CHART_RECOMMENDATIONS: int = 10  # Maximum heatmap charts
    MAX_HIERARCHICAL_CHART_RECOMMENDATIONS: int = 10  # Maximum hierarchical charts (treemap, sunburst, icicle)

    # Google OAuth settings
    GOOGLE_CLIENT_ID: Optional[str] = None
    GOOGLE_CLIENT_SECRET: Optional[str] = None


    # Google Sheets Analytics settings
    GOOGLE_SHEETS_CREDENTIALS_FILE: Optional[str] = None
    GOOGLE_SHEETS_USER_PROFILE_SHEET_ID: Optional[str] = None
    GOOGLE_SHEETS_EVENTS_SHEET_ID: Optional[str] = None
    ENABLE_ANALYTICS: bool = True  # Enable/disable analytics tracking

    # File Upload Settings
    MAX_FILE_SIZE_MB: int = 10  # Maximum file size in MB
    ALLOWED_FILE_TYPES: str = "csv,xlsx,xls"  # Comma-separated list of allowed file extensions

    # Logging Configuration
    LOG_LEVEL: str = "INFO"  # Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
    VERBOSE_LOGGING: bool = False  # Legacy verbose logging flag

    @property
    def allowed_file_types_list(self) -> List[str]:
        """Get allowed file types as a list."""
        return [ext.strip().lower() for ext in self.ALLOWED_FILE_TYPES.split(',')]

    @property
    def max_file_size_bytes(self) -> int:
        """Get maximum file size in bytes."""
        return self.MAX_FILE_SIZE_MB * 1024 * 1024

    @field_validator('USE_LLM', 'ENABLE_ANALYTICS', 'VERBOSE_LOGGING', mode='before')
    @classmethod
    def parse_boolean(cls, v: Any) -> bool:
        """
        Parse boolean values from environment variables, handling common formats.
        Supports: true/false, True/False, 1/0, yes/no (case insensitive)
        Also strips comments from the value.
        """
        if isinstance(v, bool):
            return v
        if isinstance(v, str):
            # Strip comments (anything after #)
            v = v.split('#')[0].strip()
            # Handle common boolean representations
            v_lower = v.lower()
            if v_lower in ('true', '1', 'yes', 'on'):
                return True
            elif v_lower in ('false', '0', 'no', 'off'):
                return False
            else:
                raise ValueError(f"Invalid boolean value: {v}")
        if isinstance(v, int):
            return bool(v)
        raise ValueError(f"Cannot convert {type(v)} to boolean: {v}")

    class Config:
        env_file = os.path.join(os.path.dirname(__file__), '../../.env')

# Initialize the Settings instance
settings = Settings()


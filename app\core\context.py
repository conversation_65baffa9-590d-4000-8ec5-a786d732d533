from contextvars import ContextVar
from typing import Optional, Dict
from dataclasses import dataclass

# Create context variables for standard headers
request_id_ctx_var = ContextVar[str]("request_id", default="")
session_id_ctx_var = ContextVar[str]("session_id", default="")
device_id_ctx_var = ContextVar[str]("device_id", default="")
user_agent_ctx_var = ContextVar[str]("user_agent", default="")
ip_address_ctx_var = ContextVar[str]("ip_address", default="")
referrer_ctx_var = ContextVar[str]("referrer", default="")
correlation_id_ctx_var = ContextVar[str]("correlation_id", default="")
client_version_ctx_var = ContextVar[str]("client_version", default="")
platform_ctx_var = ContextVar[str]("platform", default="")
timezone_ctx_var = ContextVar[str]("timezone", default="")
language_ctx_var = ContextVar[str]("language", default="")

@dataclass
class RequestContext:
    """Standard request context containing common header parameters"""
    request_id: str = ""
    session_id: str = ""
    device_id: str = ""
    user_agent: str = ""
    ip_address: str = ""
    referrer: str = ""
    correlation_id: str = ""
    client_version: str = ""
    platform: str = ""
    timezone: str = ""
    language: str = ""

    def to_dict(self) -> Dict[str, str]:
        """Convert context to dictionary for logging/analytics"""
        return {
            "request_id": self.request_id,
            "session_id": self.session_id,
            "device_id": self.device_id,
            "user_agent": self.user_agent,
            "ip_address": self.ip_address,
            "referrer": self.referrer,
            "correlation_id": self.correlation_id,
            "client_version": self.client_version,
            "platform": self.platform,
            "timezone": self.timezone,
            "language": self.language
        }

# Getter functions for individual context variables
def get_request_id() -> str:
    return request_id_ctx_var.get()

def get_session_id() -> str:
    return session_id_ctx_var.get()

def get_device_id() -> str:
    return device_id_ctx_var.get()

def get_user_agent() -> str:
    return user_agent_ctx_var.get()

def get_ip_address() -> str:
    return ip_address_ctx_var.get()

def get_referrer() -> str:
    return referrer_ctx_var.get()

def get_correlation_id() -> str:
    return correlation_id_ctx_var.get()

def get_client_version() -> str:
    return client_version_ctx_var.get()

def get_platform() -> str:
    return platform_ctx_var.get()

def get_timezone() -> str:
    return timezone_ctx_var.get()

def get_language() -> str:
    return language_ctx_var.get()

def get_request_context() -> RequestContext:
    """Get the complete request context"""
    return RequestContext(
        request_id=get_request_id(),
        session_id=get_session_id(),
        device_id=get_device_id(),
        user_agent=get_user_agent(),
        ip_address=get_ip_address(),
        referrer=get_referrer(),
        correlation_id=get_correlation_id(),
        client_version=get_client_version(),
        platform=get_platform(),
        timezone=get_timezone(),
        language=get_language()
    )

def set_context(interaction_id: Optional[str] = None,
                session_id: Optional[str] = None,
                device_id: Optional[str] = None) -> None:
    """Legacy function for backward compatibility"""
    if interaction_id:
        request_id_ctx_var.set(interaction_id)
    if session_id:
        session_id_ctx_var.set(session_id)
    if device_id:
        device_id_ctx_var.set(device_id)

def set_request_context(context: RequestContext) -> None:
    """Set the complete request context"""
    if context.request_id:
        request_id_ctx_var.set(context.request_id)
    if context.session_id:
        session_id_ctx_var.set(context.session_id)
    if context.device_id:
        device_id_ctx_var.set(context.device_id)
    if context.user_agent:
        user_agent_ctx_var.set(context.user_agent)
    if context.ip_address:
        ip_address_ctx_var.set(context.ip_address)
    if context.referrer:
        referrer_ctx_var.set(context.referrer)
    if context.correlation_id:
        correlation_id_ctx_var.set(context.correlation_id)
    if context.client_version:
        client_version_ctx_var.set(context.client_version)
    if context.platform:
        platform_ctx_var.set(context.platform)
    if context.timezone:
        timezone_ctx_var.set(context.timezone)
    if context.language:
        language_ctx_var.set(context.language)

# app/core/exceptions.py

class CustomException(Exception):
    """Base class for custom exceptions with predefined error codes and types."""
    def __init__(self, message: str, code: int, error_type: str = "BUSINESS"):
        super().__init__(message)
        self.code = code
        self.error_type = error_type

class DataProcessingError(CustomException):
    """Exception raised for errors in data processing."""
    def __init__(self, message="Error processing data"):
        super().__init__(message, code=2001, error_type="BUSINESS")

class FileUploadError(CustomException):
    """Exception raised when file upload fails."""
    def __init__(self, message="Error uploading file"):
        super().__init__(message, code=2002, error_type="BUSINESS")

class FileSizeExceededError(CustomException):
    """Exception raised when uploaded file exceeds size limit."""
    def __init__(self, message="File size exceeds maximum allowed limit", max_size_mb=None, actual_size_mb=None):
        if max_size_mb and actual_size_mb:
            message = f"File size ({actual_size_mb:.1f}MB) exceeds maximum allowed limit ({max_size_mb}MB)"
        super().__init__(message, code=2004, error_type="BUSINESS")

class InvalidFileTypeError(CustomException):
    """Exception raised when uploaded file type is not allowed."""
    def __init__(self, message="File type not allowed", file_type=None, allowed_types=None):
        if file_type and allowed_types:
            message = f"File type '{file_type}' is not allowed. Allowed types: {', '.join(allowed_types)}"
        super().__init__(message, code=2005, error_type="BUSINESS")

class SecurityError(CustomException):
    """Exception raised for security-related violations."""
    def __init__(self, message="Unauthorized access or security violation detected"):
        super().__init__(message, code=9000, error_type="SECURITY")

class InvalidInputError(CustomException):
    """Exception raised when input data does not match expected format."""
    def __init__(self, message="Invalid input data provided"):
        super().__init__(message, code=2003, error_type="BUSINESS")

class LLMRequestError(CustomException):
    """Exception raised when there is an error communicating with the LLM API."""
    def __init__(self, message="Failed to fetch response from LLM API"):
        super().__init__(message, code=3000, error_type="BUSINESS")

class LLMAPIError(Exception):
    """Exception raised when there is an issue with the LLM API."""

    def __init__(self, message="Error communicating with LLM API"):
        self.message = message
        super().__init__(self.message)

class TechnicalError(Exception):
    """Unhandled system errors classified as technical errors."""
    def __init__(self, message="An unexpected error occurred. Please contact support."):
        super().__init__(message)
        self.code = 1000
        self.error_type = "TECHNICAL"

# Chart Insights specific exceptions
class ChartInsightsError(CustomException):
    """Base exception for chart insights related errors."""
    def __init__(self, message="Error generating chart insights"):
        super().__init__(message, code=4000, error_type="BUSINESS")

class InsufficientDataError(CustomException):
    """Exception raised when there is insufficient data for analysis."""
    def __init__(self, message="Insufficient data for chart analysis", chart_type=None):
        if chart_type:
            message = f"Insufficient data for {chart_type} chart analysis: {message}"
        super().__init__(message, code=4001, error_type="BUSINESS")

class InvalidChartDataError(CustomException):
    """Exception raised when chart data format is invalid."""
    def __init__(self, message="Invalid chart data format"):
        super().__init__(message, code=4002, error_type="BUSINESS")

class UnsupportedChartTypeError(CustomException):
    """Exception raised when chart type is not supported."""
    def __init__(self, message="Unsupported chart type"):
        super().__init__(message, code=4003, error_type="BUSINESS")

class ChartInsightsLLMError(CustomException):
    """Exception raised when LLM fails to generate insights."""
    def __init__(self, message="Failed to generate insights using LLM"):
        super().__init__(message, code=4004, error_type="BUSINESS")

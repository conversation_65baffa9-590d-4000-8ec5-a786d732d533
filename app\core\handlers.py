# app/core/handlers.py

from fastapi import Request
from fastapi.responses import JSONResponse
from app.core.exceptions import (
    CustomException, DataProcessingError, FileUploadError, SecurityError, TechnicalError
)

# Generic handler for unhandled exceptions (TECHNICAL ERRORS)
async def general_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content={
            "error_code": 1000,
            "error_type": "TECHNICAL",
            "error_message": "An unexpected error occurred. Please contact support."
        }
    )

# Custom handler for business logic errors
async def custom_exception_handler(request: Request, exc: CustomException):
    return J<PERSON>NResponse(
        status_code=400,
        content={
            "error_code": exc.code,
            "error_type": exc.error_type,
            "error_message": str(exc)
        }
    )

# Security error handler
async def security_exception_handler(request: Request, exc: SecurityError):
    return JSONResponse(
        status_code=403,
        content={
            "error_code": 9000,
            "error_type": "SECURITY",
            "error_message": str(exc)
        }
    )

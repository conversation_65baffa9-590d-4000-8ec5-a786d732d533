"""
Common Header Management System

This module provides a centralized way to handle standard HTTP headers across all APIs.
It includes dependency injection for common headers and automatic context setting.
"""

from fastapi import Header, Request, Depends
from typing import Optional, Dict, Any
from app.core.context import RequestContext, set_request_context
from app.core.logging_config import configure_logging
import uuid
import re

logger = configure_logging()

# Standard header names mapping (client header -> internal name)
STANDARD_HEADERS = {
    # Request tracking
    "x-request-id": "request_id",
    "x-correlation-id": "correlation_id",
    "x-session-id": "session_id",
    "x-device-id": "device_id",
    
    # Client information
    "user-agent": "user_agent",
    "x-client-version": "client_version",
    "x-platform": "platform",
    "x-timezone": "timezone",
    "accept-language": "language",
    
    # Network information
    "x-forwarded-for": "ip_address",
    "x-real-ip": "ip_address",
    "referer": "referrer",
    "referrer": "referrer"
}

def extract_ip_address(request: Request) -> str:
    """Extract IP address from request headers with proxy support"""
    # Check for forwarded IP first (proxy/load balancer)
    forwarded_for = request.headers.get('x-forwarded-for')
    if forwarded_for:
        # Take the first IP in the chain (original client)
        return forwarded_for.split(',')[0].strip()
    
    # Check for real IP header
    real_ip = request.headers.get('x-real-ip')
    if real_ip:
        return real_ip.strip()
    
    # Fall back to direct client IP
    if request.client:
        return request.client.host
    
    return ""

def extract_language(accept_language: Optional[str]) -> str:
    """Extract primary language from Accept-Language header"""
    if not accept_language:
        return ""
    
    # Parse Accept-Language header (e.g., "en-US,en;q=0.9,es;q=0.8")
    languages = accept_language.split(',')
    if languages:
        # Take the first language and extract the primary part
        primary_lang = languages[0].split(';')[0].strip()
        # Extract just the language code (e.g., "en" from "en-US")
        return primary_lang.split('-')[0].lower()
    
    return ""

def generate_request_id() -> str:
    """Generate a unique request ID if not provided"""
    return str(uuid.uuid4())

def sanitize_header_value(value: Optional[str], max_length: int = 255) -> str:
    """Sanitize header value for security and logging"""
    if not value:
        return ""
    
    # Remove control characters and limit length
    sanitized = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', str(value))
    return sanitized[:max_length].strip()

async def get_common_headers(
    request: Request,
    x_request_id: Optional[str] = Header(None, alias="x-request-id"),
    x_correlation_id: Optional[str] = Header(None, alias="x-correlation-id"),
    x_session_id: Optional[str] = Header(None, alias="x-session-id"),
    x_device_id: Optional[str] = Header(None, alias="x-device-id"),
    x_client_version: Optional[str] = Header(None, alias="x-client-version"),
    x_platform: Optional[str] = Header(None, alias="x-platform"),
    x_timezone: Optional[str] = Header(None, alias="x-timezone"),
    user_agent: Optional[str] = Header(None, alias="user-agent"),
    accept_language: Optional[str] = Header(None, alias="accept-language"),
    referer: Optional[str] = Header(None, alias="referer")
) -> RequestContext:
    """
    Dependency to extract and validate common headers from requests.
    
    This function automatically extracts standard headers and creates a RequestContext
    that can be used across all API endpoints.
    
    Returns:
        RequestContext: Populated with extracted header values
    """
    
    # Extract IP address using custom logic
    ip_address = extract_ip_address(request)
    
    # Generate request ID if not provided
    request_id = x_request_id or generate_request_id()
    
    # Extract and sanitize all header values
    context = RequestContext(
        request_id=sanitize_header_value(request_id),
        correlation_id=sanitize_header_value(x_correlation_id or request_id),
        session_id=sanitize_header_value(x_session_id),
        device_id=sanitize_header_value(x_device_id),
        user_agent=sanitize_header_value(user_agent, 500),  # Allow longer user agent
        client_version=sanitize_header_value(x_client_version),
        platform=sanitize_header_value(x_platform),
        timezone=sanitize_header_value(x_timezone),
        language=extract_language(accept_language),
        ip_address=sanitize_header_value(ip_address),
        referrer=sanitize_header_value(referer, 500)  # Allow longer referrer
    )
    
    # Set context for the current request
    set_request_context(context)
    
    # Log the context for debugging (in development)
    logger.debug(f"Request context set: {context.to_dict()}")
    
    return context

def get_headers_for_analytics(context: RequestContext) -> Dict[str, Any]:
    """
    Convert request context to analytics-friendly format.
    
    Args:
        context: RequestContext object
        
    Returns:
        Dictionary suitable for analytics tracking
    """
    return {
        'user_agent': context.user_agent,
        'ip_address': context.ip_address,
        'referrer': context.referrer,
        'session_id': context.session_id,
        'device_id': context.device_id,
        'client_version': context.client_version,
        'platform': context.platform,
        'timezone': context.timezone,
        'language': context.language
    }

def get_headers_for_logging(context: RequestContext) -> Dict[str, str]:
    """
    Convert request context to logging-friendly format.
    
    Args:
        context: RequestContext object
        
    Returns:
        Dictionary suitable for structured logging
    """
    return {
        'request_id': context.request_id,
        'correlation_id': context.correlation_id,
        'session_id': context.session_id,
        'user_agent': context.user_agent[:100] if context.user_agent else "",  # Truncate for logs
        'ip_address': context.ip_address,
        'client_version': context.client_version,
        'platform': context.platform
    }

# Convenience dependency for APIs that need common headers
CommonHeaders = Depends(get_common_headers)

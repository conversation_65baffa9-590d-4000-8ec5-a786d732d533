# app/core/logging_config.py
import logging
import os
import inspect
from contextvars import <PERSON>textV<PERSON>
from app.core.context import get_request_id

# Environment variables to control logging
VERBOSE_LOGGING = os.environ.get('VERBOSE_LOGGING', 'false').lower() == 'true'
LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO').upper()

# Map string log levels to logging constants
LOG_LEVEL_MAP = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

class ContextFilter(logging.Filter):
    def filter(self, record):
        # Add interaction_id from context
        record.interaction_id = get_request_id() or "no-interaction-id"

        # Add filename and function name for better debugging
        if hasattr(record, 'pathname'):
            record.filename = os.path.basename(record.pathname)
        else:
            record.filename = "unknown"

        if hasattr(record, 'funcName'):
            record.function_name = record.funcName
        else:
            record.function_name = "unknown"

        return True

def configure_logging(module_name=None):
    """
    Configure logging with appropriate levels and enhanced formatting.

    Args:
        module_name: Optional module name to get a logger for a specific module

    Returns:
        A configured logger instance
    """
    # Get the logger for the calling module
    logger_name = module_name if module_name else __name__
    logger = logging.getLogger(logger_name)

    # Avoid duplicate handlers
    if logger.handlers:
        return logger

    # Set up the basic configuration with enhanced format
    log_format = (
        '%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(function_name)s() - '
        '%(message)s | interaction_id=%(interaction_id)s'
    )

    # Determine log level based on configuration
    if LOG_LEVEL in LOG_LEVEL_MAP:
        log_level = LOG_LEVEL_MAP[LOG_LEVEL]
    else:
        # Fallback to VERBOSE_LOGGING behavior if LOG_LEVEL is invalid
        log_level = logging.INFO if VERBOSE_LOGGING else logging.WARNING

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)

    # Create formatter and add it to handler
    formatter = logging.Formatter(log_format)
    console_handler.setFormatter(formatter)

    # Add the context filter to include interaction_id and other context
    context_filter = ContextFilter()
    console_handler.addFilter(context_filter)

    # Add handler to logger
    logger.addHandler(console_handler)
    logger.setLevel(log_level)

    # Set specific log levels for different modules based on LOG_LEVEL
    if log_level == logging.DEBUG:
        # Enable debug logging for chart recommendations when LOG_LEVEL=DEBUG
        logging.getLogger('app.services.chart_recommender').setLevel(logging.DEBUG)
        logging.getLogger('app.services.chart_recommendations').setLevel(logging.DEBUG)
        # Keep other modules at INFO level to avoid too much noise
        logging.getLogger('app.services.data_processor').setLevel(logging.INFO)
        logging.getLogger('app.services.data_validator').setLevel(logging.INFO)
        logging.getLogger('app.services.data_analytics').setLevel(logging.INFO)
        logging.getLogger('app.services.data_insights').setLevel(logging.INFO)
    elif log_level >= logging.WARNING:
        # Keep data processing logs at WARNING level to reduce verbosity
        logging.getLogger('app.services.data_processor').setLevel(logging.WARNING)
        logging.getLogger('app.services.data_validator').setLevel(logging.WARNING)
        logging.getLogger('app.services.data_analytics').setLevel(logging.WARNING)
        logging.getLogger('app.services.data_insights').setLevel(logging.WARNING)
        logging.getLogger('app.services.chart_recommender').setLevel(logging.WARNING)
        logging.getLogger('app.services.chart_recommendations').setLevel(logging.WARNING)

    # Keep important application logs at appropriate level
    logging.getLogger('app.main').setLevel(min(log_level, logging.INFO))
    logging.getLogger('app.api').setLevel(min(log_level, logging.INFO))
    logging.getLogger('app.services.csv_service_v2').setLevel(min(log_level, logging.INFO))

    return logger

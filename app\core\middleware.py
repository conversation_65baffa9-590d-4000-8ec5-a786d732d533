"""
Common Middleware for Header Processing

This middleware automatically processes common headers for all requests
and sets up the request context before the request reaches the endpoint.
"""

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import <PERSON><PERSON>App
from app.core.context import RequestContext, set_request_context
from app.core.headers import extract_ip_address, extract_language, generate_request_id, sanitize_header_value
from app.core.logging_config import configure_logging
import time
from typing import Callable

logger = configure_logging()

class CommonHeadersMiddleware(BaseHTTPMiddleware):
    """
    Middleware to automatically extract and set common headers in request context.
    
    This middleware runs before all requests and:
    1. Extracts standard headers from the request
    2. Sets up the RequestContext with sanitized values
    3. Adds request timing and logging
    4. Ensures consistent header handling across all endpoints
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request headers and set context before calling the endpoint"""
        
        start_time = time.time()
        
        try:
            # Extract common headers from request
            context = self._extract_request_context(request)
            
            # Set the context for this request
            set_request_context(context)
            
            # Log request start (debug level to avoid spam)
            logger.debug(
                f"Request started: {request.method} {request.url.path} "
                f"[{context.request_id}] from {context.ip_address}"
            )
            
            # Process the request
            response = await call_next(request)
            
            # Calculate request duration
            duration = time.time() - start_time
            
            # Add common response headers
            response.headers["x-request-id"] = context.request_id
            response.headers["x-correlation-id"] = context.correlation_id
            
            # Log request completion
            logger.info(
                f"Request completed: {request.method} {request.url.path} "
                f"[{context.request_id}] {response.status_code} in {duration:.3f}s"
            )
            
            return response
            
        except Exception as e:
            # Log error with context if available
            duration = time.time() - start_time
            logger.error(
                f"Request failed: {request.method} {request.url.path} "
                f"in {duration:.3f}s - {str(e)}"
            )
            raise
    
    def _extract_request_context(self, request: Request) -> RequestContext:
        """Extract request context from headers"""
        
        headers = request.headers
        
        # Extract IP address
        ip_address = extract_ip_address(request)
        
        # Generate request ID if not provided
        request_id = headers.get('x-request-id') or generate_request_id()
        
        # Extract and sanitize all header values
        context = RequestContext(
            request_id=sanitize_header_value(request_id),
            correlation_id=sanitize_header_value(
                headers.get('x-correlation-id') or request_id
            ),
            session_id=sanitize_header_value(headers.get('x-session-id')),
            device_id=sanitize_header_value(headers.get('x-device-id')),
            user_agent=sanitize_header_value(headers.get('user-agent'), 500),
            client_version=sanitize_header_value(headers.get('x-client-version')),
            platform=sanitize_header_value(headers.get('x-platform')),
            timezone=sanitize_header_value(headers.get('x-timezone')),
            language=extract_language(headers.get('accept-language')),
            ip_address=sanitize_header_value(ip_address),
            referrer=sanitize_header_value(
                headers.get('referer') or headers.get('referrer'), 500
            )
        )
        
        return context

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Enhanced request logging middleware that uses the common headers context.
    
    This middleware provides structured logging with request context information.
    """
    
    def __init__(self, app: ASGIApp, log_body: bool = False):
        super().__init__(app)
        self.log_body = log_body
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Log request and response details with context"""
        
        start_time = time.time()
        
        # Get request context (should be set by CommonHeadersMiddleware)
        from app.core.context import get_request_context
        context = get_request_context()
        
        # Log request details
        request_info = {
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "headers": dict(request.headers) if logger.isEnabledFor(10) else {},  # Debug level
            "client": f"{context.ip_address}:{request.client.port}" if request.client else "unknown",
            "user_agent": context.user_agent,
            "request_id": context.request_id
        }
        
        logger.info(f"Incoming request: {request_info}")
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log response details
            response_info = {
                "status_code": response.status_code,
                "duration_ms": round(duration * 1000, 2),
                "request_id": context.request_id,
                "response_headers": dict(response.headers) if logger.isEnabledFor(10) else {}
            }
            
            logger.info(f"Response sent: {response_info}")
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                f"Request failed after {duration:.3f}s: {str(e)} "
                f"[{context.request_id}]"
            )
            raise

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add security headers to all responses.
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add security headers to response"""
        
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Add cache control for API responses
        if request.url.path.startswith("/v1/"):
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
        
        return response

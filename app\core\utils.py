import json
import numpy as np
import pandas as pd
from typing import Any, Dict, List, Union

def sanitize_json_data(data: Any) -> Any:
    """Recursively sanitize data to ensure it's JSON serializable.
    Handles special float values (NaN, inf, -inf) and NumPy/Pandas types.
    """
    # Handle None
    if data is None:
        return None

    # Handle dictionaries
    if isinstance(data, dict):
        return {k: sanitize_json_data(v) for k, v in data.items()}

    # Handle lists and tuples
    elif isinstance(data, (list, tuple)):
        return [sanitize_json_data(item) for item in data]

    # Handle pandas DataFrame
    elif isinstance(data, pd.DataFrame):
        return sanitize_json_data(data.to_dict('records'))

    # Handle pandas Series
    elif isinstance(data, pd.Series):
        return sanitize_json_data(data.tolist())

    # Handle pandas Index
    elif isinstance(data, pd.Index):
        return sanitize_json_data(data.tolist())

    # Handle numpy arrays
    elif isinstance(data, np.ndarray):
        return sanitize_json_data(data.tolist())

    # Handle numpy scalar types
    elif isinstance(data, (np.integer, np.int64, np.int32, np.int16, np.int8)):
        # Check for NaN values even in integer types (edge case handling)
        try:
            if pd.isna(data) or (hasattr(data, 'dtype') and pd.isna(pd.Series([data]).iloc[0])):
                return 0
            return int(data)
        except (ValueError, OverflowError):
            # Handle cases where conversion fails (e.g., NaN disguised as int)
            return 0
    elif isinstance(data, (np.floating, np.float64, np.float32, np.float16)):
        if np.isnan(data) or np.isinf(data):
            return 0  # Replace with 0 or another appropriate value
        # Round to 2 decimal places if it contains decimals
        float_val = float(data)
        return round(float_val, 2) if float_val != round(float_val) else float_val
    elif isinstance(data, np.bool_):
        return bool(data)

    # Handle pandas dtypes
    elif pd.api.types.is_dtype_equal(getattr(data, 'dtype', None), 'object'):
        return str(data)
    elif hasattr(data, 'dtype'):
        # Handle any other dtype objects
        if 'datetime' in str(data.dtype):
            # Handle datetime dtypes
            return str(data)
        elif 'object' in str(data.dtype):
            # Handle object dtypes
            return str(data)
        else:
            # Try to convert to a basic Python type
            try:
                return data.item() if hasattr(data, 'item') else str(data)
            except (ValueError, TypeError):
                return str(data)
    # Handle the specific ObjectDType class
    elif str(type(data)).endswith("ObjectDType'>") or 'ObjectDType' in str(type(data)):
        return str(data)

    # Handle basic Python types
    elif isinstance(data, (str, int, bool)):
        return data
    elif isinstance(data, float):
        # Handle special float values first
        if np.isnan(data) or np.isinf(data):
            return 0
        # Round to 2 decimal places if it contains decimals
        return round(data, 2) if data != round(data) else data

    # Default: convert to string
    return str(data)

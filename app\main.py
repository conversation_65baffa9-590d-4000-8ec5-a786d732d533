# app/main.py
from fastapi import FastAP<PERSON>
from app.api.v1.endpoints import csv_upload, chart_insights, authentication, health, analytics, example_common_headers
from app.core.logging_config import configure_logging
from app.utils.exception_handler import register_exception_handlers
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings
from app.core.middleware import CommonHeadersMiddleware, SecurityHeadersMiddleware

# Configure logging
logger = configure_logging()

# Create the FastAPI app instance
app = FastAPI()

@app.get("/")
async def root():
    return {"message": "Hello Railway!"}


# Allow CORS for your frontend
allowed_origins = [origin.strip() for origin in settings.ALLOWED_ORIGINS.split(",") if origin.strip()]

# Add custom middleware (order matters - they execute in reverse order)
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(CommonHeadersMiddleware)

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,  # Add both localhost variations
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# Register custom exception handlers
register_exception_handlers(app)

# Include API routers
app.include_router(health.router, prefix="/v1", tags=["Health"])
app.include_router(authentication.router, prefix="/v1", tags=["Authentication"])
app.include_router(csv_upload.router, prefix="/v1", tags=["Data"])
app.include_router(chart_insights.router, prefix="/v1", tags=["Chart"])
app.include_router(analytics.router, prefix="/v1", tags=["Analytics"])
app.include_router(example_common_headers.router, prefix="/v1", tags=["Examples"])

@app.on_event("startup")
async def startup():
    logger.info(f"Starting up the FastAPI app with allowed origins: {allowed_origins}")

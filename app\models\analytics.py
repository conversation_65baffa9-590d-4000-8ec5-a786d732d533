from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum


class EventType(str, Enum):
    """Enumeration of supported event types"""
    USER_LOGIN = "USER_LOGIN"
    FILE_UPLOAD = "FILE_UPLOAD"
    CHART_GENERATION = "CHART_GENERATION"
    CHART_RENDER = "CHART_RENDER"
    CHART_COPY = "CHART_COPY"
    CHART_DOWNLOAD = "CHART_DOWNLOAD"
    CHART_VIEW = "CHART_VIEW"
    ADDITIONAL_CHARTS_VIEW = "ADDITIONAL_CHARTS_VIEW"
    CHART_INSIGHTS="CHART_INSIGHTS"
    EXECUTIVE_SUMMARY_VIEW = "EXECUTIVE_SUMMARY_VIEW"
    CHART_UPDATE = "CHART_UPDATE"
    PAGE_VIEW = "PAGE_VIEW"
    NAVIGATION = "NAVIGATION"
    ERROR_OCCURRED = "ERROR_OCCURRED"
    SESSION_START = "SESSION_START"
    SESSION_END = "SESSION_END"



class UserProfileData(BaseModel):
    """Model for user profile data to be stored in Google Sheets"""
    email: str = Field(..., description="User's email address")
    first_login_date: datetime = Field(..., description="Date of first login")
    last_login_date: datetime = Field(..., description="Date of last login")
    login_count: int = Field(default=1, description="Total number of logins")
    user_agent: Optional[str] = Field(None, description="User's browser/device info")
    ip_address: Optional[str] = Field(None, description="User's IP address")
    location: Optional[str] = Field(None, description="User's location (if available)")
    name: Optional[str] = Field(None, description="User's full name from Google")
    picture: Optional[str] = Field(None, description="User's profile picture URL")


class EventData(BaseModel):
    """Model for event data to be stored in Google Sheets"""
    event_id: str = Field(..., description="Unique event identifier")
    email: str = Field(..., description="User's email address")
    event_type: EventType = Field(..., description="Type of event")
    timestamp: datetime = Field(..., description="When the event occurred")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional event-specific data")
    user_agent: Optional[str] = Field(None, description="User's browser/device info")
    ip_address: Optional[str] = Field(None, description="User's IP address")
    session_id: Optional[str] = Field(None, description="User's session identifier")
    page_url: Optional[str] = Field(None, description="URL where event occurred")
    referrer: Optional[str] = Field(None, description="Referrer URL")


class UserLoginRequest(BaseModel):
    """Request model for user login analytics"""
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    location: Optional[str] = None


class EventTrackingRequest(BaseModel):
    """Request model for event tracking from UI"""
    event_type: EventType = Field(..., description="Type of event to track")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional event-specific data")
    user_agent: Optional[str] = Field(None, description="User's browser/device info")
    ip_address: Optional[str] = Field(None, description="User's IP address")
    session_id: Optional[str] = Field(None, description="User's session identifier")
    page_url: Optional[str] = Field(None, description="URL where event occurred")
    referrer: Optional[str] = Field(None, description="Referrer URL")


class FileUploadEventRequest(BaseModel):
    """Request model for file upload event tracking"""
    file_name: str = Field(..., description="Name of the uploaded file")
    file_size: int = Field(..., description="Size of the uploaded file in bytes")
    file_type: str = Field(..., description="Type/extension of the uploaded file")
    session_id: Optional[str] = Field(None, description="User's session identifier")
    user_agent: Optional[str] = Field(None, description="User's browser/device info")
    ip_address: Optional[str] = Field(None, description="User's IP address")


class ChartEventRequest(BaseModel):
    """Request model for chart event tracking"""
    event_type: EventType = Field(..., description="Type of chart event")
    chart_type: str = Field(..., description="Type of chart (bar, pie, line, etc.)")
    chart_id: Optional[str] = Field(None, description="Unique identifier for the chart")
    session_id: Optional[str] = Field(None, description="User's session identifier")
    user_agent: Optional[str] = Field(None, description="User's browser/device info")
    ip_address: Optional[str] = Field(None, description="User's IP address")


class PageViewEventRequest(BaseModel):
    """Request model for page view event tracking"""
    page_url: str = Field(..., description="URL of the page viewed")
    page_title: Optional[str] = Field(None, description="Title of the page viewed")
    session_id: Optional[str] = Field(None, description="User's session identifier")
    user_agent: Optional[str] = Field(None, description="User's browser/device info")
    ip_address: Optional[str] = Field(None, description="User's IP address")
    referrer: Optional[str] = Field(None, description="Referrer URL")


class AnalyticsResponse(BaseModel):
    """Response model for analytics operations"""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    event_id: Optional[str] = Field(None, description="Event ID if applicable")

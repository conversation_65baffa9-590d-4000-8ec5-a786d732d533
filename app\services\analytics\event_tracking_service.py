import asyncio
import uuid
from datetime import datetime
from typing import Optional, Dict, Any
from app.services.analytics.google_sheets_service import GoogleSheetsService
from app.models.analytics import EventData, EventType
from app.core.config import settings
from app.core.logging_config import configure_logging

logger = configure_logging()


class EventTrackingService:
    """Service for tracking user events and analytics"""
    
    def __init__(self):
        self.sheets_service = GoogleSheetsService()
    
    async def track_event(
        self,
        email: str,
        event_type: EventType,
        metadata: Optional[Dict[str, Any]] = None,
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None,
        session_id: Optional[str] = None,
        page_url: Optional[str] = None,
        referrer: Optional[str] = None
    ) -> Optional[str]:
        """
        Track a user event
        
        Args:
            email: User's email address
            event_type: Type of event to track
            metadata: Additional event-specific data
            user_agent: User's browser/device info
            ip_address: User's IP address
            session_id: User's session identifier
            page_url: URL where event occurred
            referrer: Referrer URL
            
        Returns:
            str: Event ID if successful, None otherwise
        """
        try:
            if not settings.ENABLE_ANALYTICS:
                logger.debug("Analytics tracking is disabled")
                return None
            
            if not self.sheets_service.is_available():
                logger.warning("Google Sheets service not available for event tracking")
                return None
            
            # Generate unique event ID
            event_id = str(uuid.uuid4())
            
            # Prepare event data
            event_data = {
                'event_id': event_id,
                'email': email,
                'event_type': event_type.value,
                'timestamp': datetime.now().isoformat(),
                'metadata': metadata or {},
                'user_agent': user_agent,
                'ip_address': ip_address,
                'session_id': session_id,
                'page_url': page_url,
                'referrer': referrer
            }
            
            # Save to Google Sheets
            success = await self.sheets_service.save_event(event_data)
            
            if success:
                logger.info(f"Event tracked: {event_type.value} for {email} (ID: {event_id})")
                return event_id
            else:
                logger.error(f"Failed to track event: {event_type.value} for {email}")
                return None
            
        except Exception as e:
            logger.error(f"Error tracking event {event_type.value} for {email}: {str(e)}")
            return None
    
    async def track_event_async(
        self,
        email: str,
        event_type: EventType,
        metadata: Optional[Dict[str, Any]] = None,
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None,
        session_id: Optional[str] = None,
        page_url: Optional[str] = None,
        referrer: Optional[str] = None
    ) -> None:
        """
        Track event asynchronously without blocking the main request
        
        Args:
            email: User's email address
            event_type: Type of event to track
            metadata: Additional event-specific data
            user_agent: User's browser/device info
            ip_address: User's IP address
            session_id: User's session identifier
            page_url: URL where event occurred
            referrer: Referrer URL
        """
        try:
            # Create a background task to track the event
            asyncio.create_task(
                self.track_event(
                    email=email,
                    event_type=event_type,
                    metadata=metadata,
                    user_agent=user_agent,
                    ip_address=ip_address,
                    session_id=session_id,
                    page_url=page_url,
                    referrer=referrer
                )
            )
            logger.debug(f"Background event tracking task created: {event_type.value} for {email}")
            
        except Exception as e:
            logger.error(f"Error creating background event tracking task: {str(e)}")
    
    async def track_login_event(
        self,
        email: str,
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Track user login event
        
        Args:
            email: User's email address
            user_agent: User's browser/device info
            ip_address: User's IP address
            session_id: User's session identifier
            
        Returns:
            str: Event ID if successful, None otherwise
        """
        return await self.track_event(
            email=email,
            event_type=EventType.USER_LOGIN,
            metadata={'login_method': 'google_oauth'},
            user_agent=user_agent,
            ip_address=ip_address,
            session_id=session_id
        )
    
    async def track_file_upload_event(
        self,
        email: str,
        file_name: str,
        file_size: int,
        file_type: str,
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Track file upload event
        
        Args:
            email: User's email address
            file_name: Name of uploaded file
            file_size: Size of uploaded file in bytes
            file_type: Type/extension of uploaded file
            user_agent: User's browser/device info
            ip_address: User's IP address
            session_id: User's session identifier
            
        Returns:
            str: Event ID if successful, None otherwise
        """
        metadata = {
            'file_name': file_name,
            'file_size': file_size,
            'file_type': file_type
        }
        
        return await self.track_event(
            email=email,
            event_type=EventType.FILE_UPLOAD,
            metadata=metadata,
            user_agent=user_agent,
            ip_address=ip_address,
            session_id=session_id
        )
    
    async def track_chart_event(
        self,
        email: str,
        event_type: EventType,
        chart_type: str,
        chart_id: Optional[str] = None,
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None,
        session_id: Optional[str] = None,
        page_url: Optional[str] = None
    ) -> Optional[str]:
        """
        Track chart-related events (save, download, view, update)
        
        Args:
            email: User's email address
            event_type: Type of chart event
            chart_type: Type of chart (bar, pie, line, etc.)
            chart_id: Unique identifier for the chart
            user_agent: User's browser/device info
            ip_address: User's IP address
            session_id: User's session identifier
            page_url: URL where event occurred
            
        Returns:
            str: Event ID if successful, None otherwise
        """
        metadata = {
            'chart_type': chart_type,
            'chart_id': chart_id
        }
        
        return await self.track_event(
            email=email,
            event_type=event_type,
            metadata=metadata,
            user_agent=user_agent,
            ip_address=ip_address,
            session_id=session_id,
            page_url=page_url
        )
    
    async def track_page_view_event(
        self,
        email: str,
        page_url: str,
        page_title: Optional[str] = None,
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None,
        session_id: Optional[str] = None,
        referrer: Optional[str] = None
    ) -> Optional[str]:
        """
        Track page view event
        
        Args:
            email: User's email address
            page_url: URL of the page viewed
            page_title: Title of the page viewed
            user_agent: User's browser/device info
            ip_address: User's IP address
            session_id: User's session identifier
            referrer: Referrer URL
            
        Returns:
            str: Event ID if successful, None otherwise
        """
        metadata = {
            'page_title': page_title
        }
        
        return await self.track_event(
            email=email,
            event_type=EventType.PAGE_VIEW,
            metadata=metadata,
            user_agent=user_agent,
            ip_address=ip_address,
            session_id=session_id,
            page_url=page_url,
            referrer=referrer
        )
    
    def extract_request_info(self, request) -> Dict[str, Optional[str]]:
        """
        Extract request information for event tracking
        
        Args:
            request: FastAPI Request object
            
        Returns:
            Dict containing extracted request information
        """
        try:
            # Extract user agent
            user_agent = request.headers.get('user-agent')
            
            # Extract IP address (considering proxy headers)
            ip_address = (
                request.headers.get('x-forwarded-for', '').split(',')[0].strip() or
                request.headers.get('x-real-ip') or
                request.client.host if request.client else None
            )
            
            # Extract referrer
            referrer = request.headers.get('referer')
            
            # Extract page URL
            page_url = str(request.url)
            
            return {
                'user_agent': user_agent,
                'ip_address': ip_address,
                'referrer': referrer,
                'page_url': page_url
            }
            
        except Exception as e:
            logger.error(f"Error extracting request info: {str(e)}")
            return {
                'user_agent': None,
                'ip_address': None,
                'referrer': None,
                'page_url': None
            }

import gspread
from google.oauth2.service_account import Credentials
from typing import List, Dict, Any, Optional
import json
import os
from datetime import datetime
from app.core.config import settings
from app.core.logging_config import configure_logging

logger = configure_logging()


class GoogleSheetsService:
    """Service for interacting with Google Sheets for analytics data storage"""

    _instance = None
    _initialized = False

    def __new__(cls):
        """Implement singleton pattern to ensure only one instance exists"""
        if cls._instance is None:
            cls._instance = super(GoogleSheetsService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        # Only initialize once
        if not self._initialized:
            self.client = None
            self.user_profile_sheet = None
            self.events_sheet = None
            self._initialize_client()
            GoogleSheetsService._initialized = True
            logger.info("GoogleSheetsService singleton instance created and initialized")
    
    def _initialize_client(self):
        """Initialize Google Sheets client with service account credentials"""
        try:
            if not settings.GOOGLE_SHEETS_CREDENTIALS_FILE:
                logger.warning("Google Sheets credentials file not configured")
                return
            
            if not os.path.exists(settings.GOOGLE_SHEETS_CREDENTIALS_FILE):
                logger.warning(f"Google Sheets credentials file not found: {settings.GOOGLE_SHEETS_CREDENTIALS_FILE}")
                return
            
            # Define the scope for Google Sheets API
            scope = [
                'https://spreadsheets.google.com/feeds',
                'https://www.googleapis.com/auth/drive'
            ]
            
            # Load credentials from service account file
            credentials = Credentials.from_service_account_file(
                settings.GOOGLE_SHEETS_CREDENTIALS_FILE,
                scopes=scope
            )
            
            # Initialize the client
            self.client = gspread.authorize(credentials)
            logger.info("Google Sheets client initialized successfully")
            
            # Initialize worksheets
            self._initialize_worksheets()
            
        except Exception as e:
            logger.error(f"Failed to initialize Google Sheets client: {str(e)}")
            self.client = None
    
    def _initialize_worksheets(self):
        """Initialize user profile and events worksheets"""
        try:
            if not self.client:
                logger.debug("Client not available, skipping worksheet initialization")
                return

            # Initialize user profile sheet (only if not already initialized)
            if settings.GOOGLE_SHEETS_USER_PROFILE_SHEET_ID and not self.user_profile_sheet:
                try:
                    self.user_profile_sheet = self.client.open_by_key(
                        settings.GOOGLE_SHEETS_USER_PROFILE_SHEET_ID
                    ).sheet1
                    self._setup_user_profile_headers()
                    logger.info("User profile sheet initialized")
                except Exception as e:
                    logger.error(f"Failed to initialize user profile sheet: {str(e)}")
            elif self.user_profile_sheet:
                logger.debug("User profile sheet already initialized, skipping")

            # Initialize events sheet (only if not already initialized)
            if settings.GOOGLE_SHEETS_EVENTS_SHEET_ID and not self.events_sheet:
                try:
                    self.events_sheet = self.client.open_by_key(
                        settings.GOOGLE_SHEETS_EVENTS_SHEET_ID
                    ).sheet1
                    self._setup_events_headers()
                    logger.info("Events sheet initialized")
                except Exception as e:
                    logger.error(f"Failed to initialize events sheet: {str(e)}")
            elif self.events_sheet:
                logger.debug("Events sheet already initialized, skipping")

        except Exception as e:
            logger.error(f"Failed to initialize worksheets: {str(e)}")
    
    def _setup_user_profile_headers(self):
        """Setup headers for user profile sheet if not already present"""
        try:
            if not self.user_profile_sheet:
                logger.debug("User profile sheet not available, skipping header setup")
                return

            headers = [
                'email', 'first_login_date', 'last_login_date', 'login_count',
                'user_agent', 'ip_address', 'location', 'name', 'picture', 'updated_at'
            ]

            # Check if headers already exist
            existing_headers = self.user_profile_sheet.row_values(1)
            if not existing_headers:
                self.user_profile_sheet.insert_row(headers, 1)
                logger.info("User profile sheet headers setup completed")
            elif existing_headers != headers:
                logger.debug("User profile sheet headers exist but differ, updating...")
                self.user_profile_sheet.update('A1:J1', [headers])
                logger.info("User profile sheet headers updated")
            else:
                logger.debug("User profile sheet headers already correctly set up")

        except Exception as e:
            logger.error(f"Failed to setup user profile headers: {str(e)}")
    
    def _setup_events_headers(self):
        """Setup headers for events sheet if not already present"""
        try:
            if not self.events_sheet:
                logger.debug("Events sheet not available, skipping header setup")
                return

            headers = [
                'event_id', 'email', 'event_type', 'timestamp', 'metadata',
                'user_agent', 'ip_address', 'session_id', 'page_url', 'referrer'
            ]

            # Check if headers already exist
            existing_headers = self.events_sheet.row_values(1)
            if not existing_headers:
                self.events_sheet.insert_row(headers, 1)
                logger.info("Events sheet headers setup completed")
            elif existing_headers != headers:
                logger.debug("Events sheet headers exist but differ, updating...")
                self.events_sheet.update('A1:J1', [headers])
                logger.info("Events sheet headers updated")
            else:
                logger.debug("Events sheet headers already correctly set up")

        except Exception as e:
            logger.error(f"Failed to setup events headers: {str(e)}")
    
    def is_available(self) -> bool:
        """Check if Google Sheets service is available and configured"""
        return self.client is not None
    
    async def save_user_profile(self, user_data: Dict[str, Any]) -> bool:
        """
        Save or update user profile data in Google Sheets
        
        Args:
            user_data: Dictionary containing user profile data
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.user_profile_sheet:
                logger.warning("User profile sheet not available")
                return False
            
            email = user_data.get('email')
            if not email:
                logger.error("Email is required for user profile")
                return False
            
            # Check if user already exists
            existing_row = self._find_user_row(email)
            
            # Prepare row data
            row_data = [
                user_data.get('email', ''),
                user_data.get('first_login_date', ''),
                user_data.get('last_login_date', ''),
                user_data.get('login_count', 1),
                user_data.get('user_agent', ''),
                user_data.get('ip_address', ''),
                user_data.get('location', ''),
                user_data.get('name', ''),
                user_data.get('picture', ''),
                datetime.now().isoformat()
            ]
            
            if existing_row:
                # Update existing row
                self.user_profile_sheet.update(f'A{existing_row}:J{existing_row}', [row_data])
                logger.info(f"Updated user profile for {email}")
            else:
                # Add new row
                self.user_profile_sheet.append_row(row_data)
                logger.info(f"Created new user profile for {email}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to save user profile: {str(e)}")
            return False
    
    async def save_event(self, event_data: Dict[str, Any]) -> bool:
        """
        Save event data in Google Sheets
        
        Args:
            event_data: Dictionary containing event data
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.events_sheet:
                logger.warning("Events sheet not available")
                return False
            
            # Prepare row data
            metadata_str = json.dumps(event_data.get('metadata', {})) if event_data.get('metadata') else ''
            
            row_data = [
                event_data.get('event_id', ''),
                event_data.get('email', ''),
                event_data.get('event_type', ''),
                event_data.get('timestamp', ''),
                metadata_str,
                event_data.get('user_agent', ''),
                event_data.get('ip_address', ''),
                event_data.get('session_id', ''),
                event_data.get('page_url', ''),
                event_data.get('referrer', '')
            ]
            
            # Add new row
            self.events_sheet.append_row(row_data)
            logger.info(f"Saved event: {event_data.get('event_type')} for {event_data.get('email')}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to save event: {str(e)}")
            return False
    
    def _find_user_row(self, email: str) -> Optional[int]:
        """
        Find the row number for a user by email
        
        Args:
            email: User's email address
            
        Returns:
            int: Row number if found, None otherwise
        """
        try:
            if not self.user_profile_sheet:
                return None
            
            # Get all email values (column A)
            email_column = self.user_profile_sheet.col_values(1)
            
            # Find the email (skip header row)
            for i, cell_email in enumerate(email_column[1:], start=2):
                if cell_email == email:
                    return i
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to find user row: {str(e)}")
            return None
    
    async def get_user_login_count(self, email: str) -> int:
        """
        Get the current login count for a user
        
        Args:
            email: User's email address
            
        Returns:
            int: Current login count, 0 if user not found
        """
        try:
            row_num = self._find_user_row(email)
            if not row_num:
                return 0
            
            # Get login count from column D (4th column)
            login_count = self.user_profile_sheet.cell(row_num, 4).value
            return int(login_count) if login_count else 0
            
        except Exception as e:
            logger.error(f"Failed to get user login count: {str(e)}")
            return 0

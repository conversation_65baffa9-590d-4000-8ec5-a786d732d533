import asyncio
from datetime import datetime
from typing import Optional, Dict, Any
from app.services.analytics.google_sheets_service import GoogleSheetsService
from app.models.analytics import UserProfileData
from app.core.config import settings
from app.core.logging_config import configure_logging

logger = configure_logging()


class UserProfileService:
    """Service for managing user profile analytics"""
    
    def __init__(self):
        self.sheets_service = GoogleSheetsService()
    
    async def track_user_login(
        self,
        email: str,
        google_user_data: Dict[str, Any],
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None,
        location: Optional[str] = None
    ) -> bool:
        """
        Track user login and update profile data
        
        Args:
            email: User's email address
            google_user_data: User data from Google authentication
            user_agent: User's browser/device info
            ip_address: User's IP address
            location: User's location (if available)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not settings.ENABLE_ANALYTICS:
                logger.debug("Analytics tracking is disabled")
                return True
            
            if not self.sheets_service.is_available():
                logger.warning("Google Sheets service not available for user tracking")
                return False
            
            # Get current login count
            current_login_count = await self.sheets_service.get_user_login_count(email)
            is_first_login = current_login_count == 0
            
            # Prepare user profile data
            now = datetime.now()
            user_profile_data = {
                'email': email,
                'first_login_date': now.isoformat() if is_first_login else None,
                'last_login_date': now.isoformat(),
                'login_count': current_login_count + 1,
                'user_agent': user_agent,
                'ip_address': ip_address,
                'location': location,
                'name': google_user_data.get('name'),
                'picture': google_user_data.get('picture')
            }
            
            # If not first login, don't update first_login_date
            if not is_first_login:
                user_profile_data.pop('first_login_date', None)
            
            # Save to Google Sheets asynchronously
            success = await self.sheets_service.save_user_profile(user_profile_data)
            
            if success:
                logger.info(f"User profile updated for {email} (login #{current_login_count + 1})")
            else:
                logger.error(f"Failed to update user profile for {email}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error tracking user login for {email}: {str(e)}")
            return False
    
    async def track_user_login_async(
        self,
        email: str,
        google_user_data: Dict[str, Any],
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None,
        location: Optional[str] = None
    ) -> None:
        """
        Track user login asynchronously without blocking the main request
        
        Args:
            email: User's email address
            google_user_data: User data from Google authentication
            user_agent: User's browser/device info
            ip_address: User's IP address
            location: User's location (if available)
        """
        try:
            # Create a background task to track user login
            asyncio.create_task(
                self.track_user_login(
                    email=email,
                    google_user_data=google_user_data,
                    user_agent=user_agent,
                    ip_address=ip_address,
                    location=location
                )
            )
            logger.debug(f"Background user tracking task created for {email}")
            
        except Exception as e:
            logger.error(f"Error creating background user tracking task for {email}: {str(e)}")
    
    async def get_user_profile(self, email: str) -> Optional[Dict[str, Any]]:
        """
        Get user profile data from Google Sheets
        
        Args:
            email: User's email address
            
        Returns:
            Dict containing user profile data or None if not found
        """
        try:
            if not self.sheets_service.is_available():
                logger.warning("Google Sheets service not available")
                return None
            
            # Find user row
            row_num = self.sheets_service._find_user_row(email)
            if not row_num:
                logger.info(f"User profile not found for {email}")
                return None
            
            # Get user data from the row
            user_row = self.sheets_service.user_profile_sheet.row_values(row_num)
            
            if len(user_row) >= 9:  # Ensure we have all required columns
                user_profile = {
                    'email': user_row[0],
                    'first_login_date': user_row[1],
                    'last_login_date': user_row[2],
                    'login_count': int(user_row[3]) if user_row[3] else 0,
                    'user_agent': user_row[4],
                    'ip_address': user_row[5],
                    'location': user_row[6],
                    'name': user_row[7],
                    'picture': user_row[8]
                }
                return user_profile
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting user profile for {email}: {str(e)}")
            return None
    
    def extract_user_info_from_request(self, request) -> Dict[str, Optional[str]]:
        """
        Extract user information from HTTP request
        
        Args:
            request: FastAPI Request object
            
        Returns:
            Dict containing extracted user information
        """
        try:
            # Extract user agent
            user_agent = request.headers.get('user-agent')
            
            # Extract IP address (considering proxy headers)
            ip_address = (
                request.headers.get('x-forwarded-for', '').split(',')[0].strip() or
                request.headers.get('x-real-ip') or
                request.client.host if request.client else None
            )
            
            # Location would typically be determined by IP geolocation service
            # For now, we'll leave it as None and can be enhanced later
            location = None
            
            return {
                'user_agent': user_agent,
                'ip_address': ip_address,
                'location': location
            }
            
        except Exception as e:
            logger.error(f"Error extracting user info from request: {str(e)}")
            return {
                'user_agent': None,
                'ip_address': None,
                'location': None
            }
    
    async def update_user_profile_field(
        self,
        email: str,
        field: str,
        value: Any
    ) -> bool:
        """
        Update a specific field in user profile
        
        Args:
            email: User's email address
            field: Field name to update
            value: New value for the field
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.sheets_service.is_available():
                logger.warning("Google Sheets service not available")
                return False
            
            # Get current user profile
            current_profile = await self.get_user_profile(email)
            if not current_profile:
                logger.warning(f"User profile not found for {email}")
                return False
            
            # Update the specific field
            current_profile[field] = value
            current_profile['updated_at'] = datetime.now().isoformat()
            
            # Save updated profile
            success = await self.sheets_service.save_user_profile(current_profile)
            
            if success:
                logger.info(f"Updated {field} for user {email}")
            else:
                logger.error(f"Failed to update {field} for user {email}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error updating user profile field {field} for {email}: {str(e)}")
            return False

from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from google.oauth2 import id_token
from google.auth.transport import requests
from app.services.authentication.token_service import TokenService
from app.core.config import settings

class AuthService:
    @staticmethod
    def verify_google_token(google_token: str) -> dict:
        if not settings.GOOGLE_CLIENT_ID:
            raise HTTPException(status_code=status.HTTP_501_NOT_IMPLEMENTED, detail="Google authentication not configured")
        try:
            id_info = id_token.verify_oauth2_token(google_token, requests.Request(), settings.GOOGLE_CLIENT_ID )
            return id_info
        except ValueError:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid Google token")

    @staticmethod
    def create_user_tokens(email: str) -> dict:
        access_token = TokenService.create_access_token({"sub": email})
        refresh_token = TokenService.create_refresh_token({"sub": email})
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }

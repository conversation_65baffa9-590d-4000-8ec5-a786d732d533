from datetime import datetime, timedelta, timezone
from typing import Any, Dict

from jose import JW<PERSON><PERSON>r, jwt
from pydantic import BaseModel
from fastapi import HTTPException, status
from app.core.config import settings
from app.core.logging_config import configure_logging

logger = configure_logging()


class TokenData(BaseModel):
    sub: str
    exp: datetime


class TokenService:
    @staticmethod
    def create_access_token(data: Dict[str, Any], expires_delta: timedelta = timedelta(minutes=60)) -> str:
        if not settings.JWT_SECRET_KEY:
            raise HTTPException(status_code=status.HTTP_501_NOT_IMPLEMENTED, detail="JWT authentication not configured")
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + expires_delta
        to_encode.update({"exp": expire})
        logger.debug(f"Creating access token with expiry: {expire}")
        logger.debug(f"Using JWT_SECRET_KEY (first 10 chars): {settings.JWT_SECRET_KEY[:10]}...")
        return jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)

    @staticmethod
    def create_refresh_token(data: Dict[str, Any], expires_delta: timedelta = timedelta(days=7)) -> str:
        if not settings.JWT_REFRESH_SECRET_KEY:
            raise HTTPException(status_code=status.HTTP_501_NOT_IMPLEMENTED, detail="JWT refresh authentication not configured")
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + expires_delta
        to_encode.update({"exp": expire})
        logger.debug(f"Creating refresh token with expiry: {expire}")
        return jwt.encode(to_encode, settings.JWT_REFRESH_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)

    @staticmethod
    def verify_access_token(token: str) -> TokenData:
        if not settings.JWT_SECRET_KEY:
            logger.error("JWT_SECRET_KEY not configured")
            raise HTTPException(status_code=status.HTTP_501_NOT_IMPLEMENTED, detail="JWT authentication not configured")

        logger.debug(f"Verifying access token using JWT_SECRET_KEY (first 10 chars): {settings.JWT_SECRET_KEY[:10]}...")
        try:
            payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
            sub: str = payload.get("sub")
            if sub is None:
                logger.warning("Token missing subject")
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Token missing subject")

            exp_timestamp = payload.get("exp")
            if exp_timestamp is None:
                logger.warning("Token missing expiration")
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Token missing expiration")

            exp: datetime = datetime.fromtimestamp(exp_timestamp, tz=timezone.utc)
            current_time = datetime.now(timezone.utc)

            if exp < current_time:
                logger.warning(f"Token expired. Expiry: {exp}, Current: {current_time}")
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Token has expired")

            logger.debug(f"Access token verified successfully for user: {sub}")
            return TokenData(sub=sub, exp=exp)
        except JWTError as e:
            logger.error(f"JWT decode error: {str(e)}")
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid or expired token")

    @staticmethod
    def verify_refresh_token(token: str) -> TokenData:
        if not settings.JWT_REFRESH_SECRET_KEY:
            logger.error("JWT_REFRESH_SECRET_KEY not configured")
            raise HTTPException(status_code=status.HTTP_501_NOT_IMPLEMENTED, detail="JWT refresh authentication not configured")

        logger.debug(f"Verifying refresh token using JWT_REFRESH_SECRET_KEY (first 10 chars): {settings.JWT_REFRESH_SECRET_KEY[:10]}...")
        try:
            payload = jwt.decode(token, settings.JWT_REFRESH_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
            sub: str = payload.get("sub")
            if sub is None:
                logger.warning("Refresh token missing subject")
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Token missing subject")

            exp_timestamp = payload.get("exp")
            if exp_timestamp is None:
                logger.warning("Refresh token missing expiration")
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Token missing expiration")

            exp: datetime = datetime.fromtimestamp(exp_timestamp, tz=timezone.utc)
            current_time = datetime.now(timezone.utc)

            if exp < current_time:
                logger.warning(f"Refresh token expired. Expiry: {exp}, Current: {current_time}")
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Refresh token has expired")

            logger.debug(f"Refresh token verified successfully for user: {sub}")
            return TokenData(sub=sub, exp=exp)
        except JWTError as e:
            logger.error(f"JWT refresh decode error: {str(e)}")
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid or expired refresh token")

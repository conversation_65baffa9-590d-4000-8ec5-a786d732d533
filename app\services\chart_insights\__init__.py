"""
Chart Insights Service Package

This package provides modular chart analysis services including:
- Executive Summary Generation
- Data Insights Analysis  
- Hidden Pattern Detection
- Recommendations Generation

The services are designed to be LLM provider agnostic and follow clean architecture principles.
"""

from .orchestrator import generate_llm_insights
from .models import ChartInsightsRequest, ChartInsightsResponse

__all__ = [
    'generate_llm_insights',
    'ChartInsightsRequest', 
    'ChartInsightsResponse'
]

"""
Chart Data Processor

This module handles the preprocessing and preparation of chart data
for analysis by the various insight generation services.
"""

import statistics
from typing import Dict, List, Any, Tuple
from app.core.logging_config import configure_logging
from .models import ProcessedChartData, KeyMetrics

logger = configure_logging()


class ChartDataProcessor:
    """Processes and prepares chart data for analysis."""
    
    def __init__(self, max_data_points: int = 100):
        self.max_data_points = max_data_points
    
    def process_chart_data(self, chart_data: Dict[str, Any]) -> ProcessedChartData:
        """
        Process raw chart data into a standardized format for analysis.
        
        Args:
            chart_data: Raw chart data from the request
            
        Returns:
            ProcessedChartData: Standardized data structure
        """
        try:
            logger.info("Processing chart data for analysis")
            logger.debug(f"Input chart data keys: {list(chart_data.keys())}")
            logger.debug(f"Chart data type: {chart_data.get('type', 'unknown')}")

            # Extract data points based on chart structure
            data_points, chart_type = self._extract_data_points(chart_data)
            
            # Limit data points for performance
            limited_data_points = data_points[:self.max_data_points]
            
            # Extract numeric values and categories
            numeric_values, categories = self._extract_values_and_categories(limited_data_points)
            
            # Create metadata
            metadata = self._create_metadata(chart_data, chart_type)
            
            processed_data = ProcessedChartData(
                data_points=limited_data_points,
                total_points=len(limited_data_points),
                chart_metadata=metadata,
                numeric_values=numeric_values,
                categories=categories,
                chart_type=chart_type
            )
            
            logger.info(f"Successfully processed {len(limited_data_points)} data points for {chart_type} chart")
            logger.debug(f"Extracted {len(numeric_values)} numeric values and {len(categories)} categories")
            return processed_data
            
        except Exception as e:
            logger.error(f"Error processing chart data: {str(e)}")
            # Return empty processed data on error
            return ProcessedChartData(
                data_points=[],
                total_points=0,
                chart_metadata={"error": str(e)},
                numeric_values=[],
                categories=[],
                chart_type=None
            )
    
    def _extract_data_points(self, chart_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], str]:
        """Extract data points from various chart data formats."""
        data_points = []
        chart_type = chart_data.get('type', 'unknown')

        # Handle different chart data structures
        if 'data' in chart_data:
            data = chart_data['data']

            if isinstance(data, list):
                # Check if it's a list of traces (multi-group bar, grouped box plots)
                if data and isinstance(data[0], dict):
                    # Handle multi-trace data (grouped bar charts, grouped box plots)
                    for trace in data:
                        if 'x' in trace and 'y' in trace:
                            # Multi-group bar chart or line chart traces
                            x_data = trace.get('x', [])
                            y_data = trace.get('y', [])
                            trace_name = trace.get('name', 'Series')
                            for x, y in zip(x_data, y_data):
                                data_points.append({
                                    "x": x,
                                    "y": y,
                                    "category": str(x),
                                    "value": y,
                                    "series": trace_name
                                })
                        elif 'y' in trace and trace.get('type') == 'box':
                            # Grouped box plot traces
                            y_data = trace.get('y', [])
                            trace_name = trace.get('name', 'Group')
                            for i, y in enumerate(y_data):
                                data_points.append({
                                    "y": y,
                                    "value": y,
                                    "category": trace_name,
                                    "index": i
                                })
                        elif isinstance(trace, dict):
                            # Other trace formats - add as individual data points
                            data_points.append(trace)
                else:
                    # Standard list of data points
                    data_points = data

            elif isinstance(data, dict):
                # Single trace data (single box plot, single series)
                if 'y' in data and data.get('type') == 'box':
                    # Single box plot
                    y_data = data.get('y', [])
                    for i, y in enumerate(y_data):
                        data_points.append({
                            "y": y,
                            "value": y,
                            "category": "Data",
                            "index": i
                        })
                elif 'x' in data and 'y' in data:
                    # Single series with x,y data (funnel, single line/area)
                    x_data = data.get('x', [])
                    y_data = data.get('y', [])
                    for x, y in zip(x_data, y_data):
                        data_points.append({
                            "x": x,
                            "y": y,
                            "category": str(x),
                            "value": y
                        })
                elif 'labels' in data and 'values' in data:
                    # Hierarchical chart format (treemap, sunburst)
                    labels = data.get('labels', [])
                    values = data.get('values', [])
                    parents = data.get('parents', [])
                    for i, (label, value) in enumerate(zip(labels, values)):
                        parent = parents[i] if i < len(parents) else ""
                        data_points.append({
                            "label": label,
                            "value": value,
                            "category": label,
                            "parent": parent
                        })
                else:
                    # Other single data object
                    data_points = [data]

        elif 'z' in chart_data and isinstance(chart_data['z'], list):
            # Heatmap format - z contains 2D array of values (check this BEFORE x/y check)
            z_data = chart_data.get('z', [])
            x_labels = chart_data.get('x', [])
            y_labels = chart_data.get('y', [])

            # Flatten the 2D array and create data points
            for row_idx, row in enumerate(z_data):
                if isinstance(row, list):
                    for col_idx, value in enumerate(row):
                        if isinstance(value, (int, float)):
                            # Create meaningful labels for heatmap cells
                            x_label = x_labels[col_idx] if col_idx < len(x_labels) else f"Col_{col_idx}"
                            y_label = y_labels[row_idx] if row_idx < len(y_labels) else f"Row_{row_idx}"

                            data_points.append({
                                "x": x_label,
                                "y": y_label,
                                "z": value,
                                "value": value,
                                "category": f"{y_label}-{x_label}",
                                "row": row_idx,
                                "col": col_idx
                            })

        elif 'x' in chart_data and 'y' in chart_data:
            # Plotly-style format (bar, line charts)
            x_data = chart_data.get('x', [])
            y_data = chart_data.get('y', [])
            data_points = [
                {"x": x, "y": y, "category": str(x), "value": y}
                for x, y in zip(x_data, y_data)
            ]

        elif 'labels' in chart_data and 'values' in chart_data:
            # Pie chart format
            labels = chart_data.get('labels', [])
            values = chart_data.get('values', [])
            data_points = [
                {"label": label, "value": value, "category": label}
                for label, value in zip(labels, values)
            ]
            # Heatmap format - z contains 2D array of values
            z_data = chart_data.get('z', [])
            x_labels = chart_data.get('x', [])
            y_labels = chart_data.get('y', [])

            # Flatten the 2D array and create data points
            for row_idx, row in enumerate(z_data):
                if isinstance(row, list):
                    for col_idx, value in enumerate(row):
                        if isinstance(value, (int, float)):
                            # Create meaningful labels for heatmap cells
                            x_label = x_labels[col_idx] if col_idx < len(x_labels) else f"Col_{col_idx}"
                            y_label = y_labels[row_idx] if row_idx < len(y_labels) else f"Row_{row_idx}"

                            data_points.append({
                                "x": x_label,
                                "y": y_label,
                                "z": value,
                                "value": value,
                                "category": f"{y_label}-{x_label}",
                                "row": row_idx,
                                "col": col_idx
                            })

        elif chart_type.lower() in ['scatter', 'bubble'] and 'x' in chart_data and 'y' in chart_data:
            # Scatter/Bubble chart format - handle arrays of coordinates
            x_data = chart_data.get('x', [])
            y_data = chart_data.get('y', [])
            size_data = chart_data.get('size', [])  # For bubble charts

            for i, (x_val, y_val) in enumerate(zip(x_data, y_data)):
                point = {
                    "x": x_val,
                    "y": y_val,
                    "value": y_val,  # Use y as primary value
                    "category": f"Point_{i+1}"
                }

                # Add size for bubble charts
                if size_data and i < len(size_data):
                    point["size"] = size_data[i]

                data_points.append(point)

        else:
            # Try to extract any list-like data
            for _, value in chart_data.items():
                if isinstance(value, list) and len(value) > 0:
                    if isinstance(value[0], dict):
                        data_points = value
                    else:
                        data_points = [{"index": i, "value": v} for i, v in enumerate(value)]
                    break
        
        logger.debug(f"Extracted {len(data_points)} data points from {chart_type} chart")
        if data_points:
            logger.debug(f"Sample data point: {data_points[0]}")
        else:
            logger.warning(f"No data points extracted from {chart_type} chart data structure")

        return data_points, chart_type
    
    def _extract_values_and_categories(self, data_points: List[Dict[str, Any]]) -> Tuple[List[float], List[str]]:
        """Extract numeric values and categories from data points."""
        numeric_values = []
        categories = []

        for point in data_points:
            if isinstance(point, dict):
                # Extract numeric values - prioritize 'value', 'z', 'y' for primary values
                primary_value_found = False
                for key in ['value', 'z', 'y', 'size']:
                    if key in point and isinstance(point[key], (int, float)):
                        numeric_values.append(float(point[key]))
                        primary_value_found = True
                        break

                # If no primary value found, extract any numeric value
                if not primary_value_found:
                    for key, value in point.items():
                        if isinstance(value, (int, float)) and key.lower() not in ['id', 'index', 'row', 'col']:
                            numeric_values.append(float(value))
                            break

                # Extract categories - prioritize meaningful category names
                category_found = False
                for key in ['category', 'label', 'name']:
                    if key in point:
                        categories.append(str(point[key]))
                        category_found = True
                        break

                # For heatmap data, create meaningful categories from x/y coordinates
                if not category_found and 'x' in point and 'y' in point:
                    categories.append(f"{point['y']}-{point['x']}")
                elif not category_found and 'x' in point:
                    categories.append(str(point['x']))

            elif isinstance(point, (int, float)):
                numeric_values.append(float(point))

        return numeric_values, list(set(categories))  # Remove duplicates from categories
    
    def _create_metadata(self, chart_data: Dict[str, Any], chart_type: str) -> Dict[str, Any]:
        """Create metadata dictionary from chart data."""
        return {
            "title": chart_data.get('title', 'Chart'),
            "type": chart_type,
            "layout": chart_data.get('layout', {}),
            "original_keys": list(chart_data.keys())
        }
    
    def calculate_key_metrics(self, processed_data: ProcessedChartData) -> KeyMetrics:
        """
        Calculate key statistical metrics from processed chart data.
        
        Args:
            processed_data: Processed chart data
            
        Returns:
            KeyMetrics: Statistical metrics
        """
        try:
            numeric_values = processed_data.numeric_values
            
            metrics = KeyMetrics(
                total_data_points=processed_data.total_points,
                chart_type=processed_data.chart_type,
                unique_categories=len(processed_data.categories)
            )
            
            if numeric_values:
                metrics.min_value = min(numeric_values)
                metrics.max_value = max(numeric_values)
                metrics.mean_value = statistics.mean(numeric_values)
                metrics.median_value = statistics.median(numeric_values)
                metrics.value_range = max(numeric_values) - min(numeric_values)
                metrics.total_sum = sum(numeric_values)
                
                if len(numeric_values) > 1:
                    metrics.std_deviation = statistics.stdev(numeric_values)
                else:
                    metrics.std_deviation = 0.0
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating key metrics: {str(e)}")
            return KeyMetrics(
                total_data_points=0,
                chart_type=processed_data.chart_type
            )

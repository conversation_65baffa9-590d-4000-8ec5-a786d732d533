"""
LLM Client Abstraction Layer

This module provides a unified interface for different LLM providers
(Mistral, OpenAI, Google Gemini, OpenRouter, etc.) to ensure the chart
insights services can switch between providers with minimal changes.
"""

import json
from typing import Optional
from abc import ABC, abstractmethod
from app.core.config import settings
from app.core.logging_config import configure_logging
from .models import LLMResponse

logger = configure_logging()


class BaseLLMClient(ABC):
    """Abstract base class for LLM clients."""

    @abstractmethod
    async def generate_response(self, system_prompt: str) -> LLMResponse:
        """Generate a response from the LLM."""
        pass

    @abstractmethod
    async def generate_unified_response(self, system_prompt: str) -> LLMResponse:
        """Generate a unified response from the LLM (returns raw response for JSON parsing)."""
        pass

    @abstractmethod
    def get_provider_name(self) -> str:
        """Get the name of the LLM provider."""
        pass

    def _clean_json_response(self, response: str) -> str:
        """Clean JSON response by removing markdown code blocks."""
        response = response.strip()
        if response.startswith('```json'):
            response = response[7:]
        if response.startswith('```'):
            response = response[3:]
        if response.endswith('```'):
            response = response[:-3]
        return response.strip()

    def _parse_response_content(self, response: str) -> list:
        """Parse the cleaned response and extract content list."""
        try:
            parsed = json.loads(response)

            # Try different possible keys for the content
            for key in ['summary_points', 'data_insights', 'hidden_patterns', 'recommendations']:
                if key in parsed:
                    return parsed[key]

            # If no specific key found, try to extract any list
            for value in parsed.values():
                if isinstance(value, list):
                    return value

            return ["Unable to parse LLM response format"]

        except json.JSONDecodeError:
            logger.error(f"Failed to parse JSON response: {response[:200]}...")
            return ["Error parsing LLM response"]


class MistralLLMClient(BaseLLMClient):
    """Mistral AI client implementation."""

    def __init__(self):
        self.provider_name = "mistral"

    async def generate_response(self, system_prompt: str) -> LLMResponse:
        """Generate response using Mistral AI."""
        try:
            from app.services.llm_service.mistral_client import get_chat_response

            logger.info(f"Generating response using {self.provider_name}")
            raw_response = get_chat_response(system_prompt)

            # Clean and parse the response
            cleaned_response = self._clean_json_response(raw_response)
            parsed_content = self._parse_response_content(cleaned_response)

            return LLMResponse(
                success=True,
                content=parsed_content,
                provider_used=self.provider_name
            )

        except Exception as e:
            logger.error(f"Error generating response with {self.provider_name}: {str(e)}")
            return LLMResponse(
                success=False,
                content=[],
                error_message=str(e),
                provider_used=self.provider_name
            )

    async def generate_unified_response(self, system_prompt: str) -> LLMResponse:
        """Generate unified response using Mistral AI (returns raw response for JSON parsing)."""
        try:
            from app.services.llm_service.mistral_client import get_chat_response

            logger.info(f"Generating unified response using {self.provider_name}")
            raw_response = get_chat_response(system_prompt)

            # For unified responses, return the raw response as a single-item list
            # The unified service will handle JSON parsing
            return LLMResponse(
                success=True,
                content=[raw_response],  # Return raw response as single-item list
                provider_used=self.provider_name
            )

        except Exception as e:
            logger.error(f"Error generating unified response with {self.provider_name}: {str(e)}")
            return LLMResponse(
                success=False,
                content=[],
                error_message=str(e),
                provider_used=self.provider_name
            )

    def get_provider_name(self) -> str:
        return self.provider_name


class OpenAILLMClient(BaseLLMClient):
    """OpenAI client implementation (placeholder for future implementation)."""

    def __init__(self):
        self.provider_name = "openai"

    async def generate_response(self, system_prompt: str) -> LLMResponse:
        """Generate response using OpenAI."""
        # TODO: Implement OpenAI client
        return LLMResponse(
            success=False,
            content=["OpenAI client not implemented yet"],
            error_message="OpenAI client not implemented",
            provider_used=self.provider_name
        )

    async def generate_unified_response(self, system_prompt: str) -> LLMResponse:
        """Generate unified response using OpenAI."""
        # TODO: Implement OpenAI client
        return LLMResponse(
            success=False,
            content=["OpenAI client not implemented yet"],
            error_message="OpenAI client not implemented",
            provider_used=self.provider_name
        )

    def get_provider_name(self) -> str:
        return self.provider_name


class GeminiLLMClient(BaseLLMClient):
    """Google Gemini client implementation (placeholder for future implementation)."""

    def __init__(self):
        self.provider_name = "gemini"

    async def generate_response(self, system_prompt: str) -> LLMResponse:
        """Generate response using Google Gemini."""
        # TODO: Implement Gemini client
        return LLMResponse(
            success=False,
            content=["Gemini client not implemented yet"],
            error_message="Gemini client not implemented",
            provider_used=self.provider_name
        )

    async def generate_unified_response(self, system_prompt: str) -> LLMResponse:
        """Generate unified response using Google Gemini."""
        # TODO: Implement Gemini client
        return LLMResponse(
            success=False,
            content=["Gemini client not implemented yet"],
            error_message="Gemini client not implemented",
            provider_used=self.provider_name
        )

    def get_provider_name(self) -> str:
        return self.provider_name


class OpenRouterLLMClient(BaseLLMClient):
    """OpenRouter client implementation using the existing OpenRouter service."""

    def __init__(self):
        self.provider_name = "openrouter"

    async def generate_response(self, system_prompt: str) -> LLMResponse:
        """Generate response using OpenRouter."""
        try:
            from app.services.llm_service.openrouter_client import get_chat_completion

            logger.info(f"Generating response using {self.provider_name}")
            raw_response = get_chat_completion(system_prompt)

            # Clean and parse the response
            cleaned_response = self._clean_json_response(raw_response)
            parsed_content = self._parse_response_content(cleaned_response)

            return LLMResponse(
                success=True,
                content=parsed_content,
                provider_used=self.provider_name
            )

        except Exception as e:
            logger.error(f"Error generating response with {self.provider_name}: {str(e)}")
            return LLMResponse(
                success=False,
                content=[],
                error_message=str(e),
                provider_used=self.provider_name
            )

    async def generate_unified_response(self, system_prompt: str) -> LLMResponse:
        """Generate unified response using OpenRouter (returns raw response for JSON parsing)."""
        try:
            from app.services.llm_service.openrouter_client import get_chat_completion

            logger.info(f"Generating unified response using {self.provider_name}")
            raw_response = get_chat_completion(system_prompt)

            # For unified responses, return the raw response as a single-item list
            # The unified service will handle JSON parsing
            return LLMResponse(
                success=True,
                content=[raw_response],  # Return raw response as single-item list
                provider_used=self.provider_name
            )

        except Exception as e:
            logger.error(f"Error generating unified response with {self.provider_name}: {str(e)}")
            return LLMResponse(
                success=False,
                content=[],
                error_message=str(e),
                provider_used=self.provider_name
            )

    def get_provider_name(self) -> str:
        return self.provider_name


class LLMClientFactory:
    """Factory class for creating LLM clients based on configuration."""
    
    _clients = {
        "mistral": MistralLLMClient,
        "openai": OpenAILLMClient,
        "gemini": GeminiLLMClient,
        "openrouter": OpenRouterLLMClient
    }
    
    @classmethod
    def create_client(self, provider: Optional[str] = None) -> BaseLLMClient:
        """
        Create an LLM client based on the provider.
        
        Args:
            provider: LLM provider name. If None, uses DEFAULT_LLM_PROVIDER from settings.
            
        Returns:
            BaseLLMClient: Configured LLM client instance
        """
        if provider is None:
            provider = settings.DEFAULT_LLM_PROVIDER.lower()
        
        provider = provider.lower()
        
        if provider not in self._clients:
            logger.warning(f"Unknown LLM provider: {provider}. Falling back to mistral.")
            provider = "mistral"
        
        client_class = self._clients[provider]
        logger.info(f"Creating LLM client for provider: {provider}")
        return client_class()
    
    @classmethod
    def get_available_providers(self) -> list:
        """Get list of available LLM providers."""
        return list(self._clients.keys())

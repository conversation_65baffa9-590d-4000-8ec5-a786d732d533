"""
Data models for Chart Insights Service

This module contains all the data models, request/response schemas,
and data structures used by the chart insights services.
"""

from typing import Dict, List, Any, Optional
from pydantic import BaseModel


class ChartData(BaseModel):
    """Chart data structure for analysis."""
    type: Optional[str] = None
    title: Optional[str] = None
    x: Optional[List[Any]] = None
    y: Optional[List[Any]] = None
    labels: Optional[List[str]] = None
    values: Optional[List[Any]] = None
    data: Optional[List[Dict[str, Any]]] = None
    layout: Optional[Dict[str, Any]] = None


class ChartInsightsRequest(BaseModel):
    """Request model for unified chart insights generation."""
    chart_data: Dict[str, Any]
    chart_type: Optional[str] = None
    chart_title: Optional[str] = "Chart Analysis"


class ChartInsightsResponse(BaseModel):
    """Response model for unified chart insights."""
    success: bool
    chart_insights: List[str]
    key_metrics: Dict[str, Any]
    chart_metadata: Dict[str, Any]


class ProcessedChartData(BaseModel):
    """Processed chart data ready for analysis."""
    data_points: List[Dict[str, Any]]
    total_points: int
    chart_metadata: Dict[str, Any]
    numeric_values: List[float]
    categories: List[str]
    chart_type: Optional[str] = None


class InsightGenerationContext(BaseModel):
    """Context object for insight generation."""
    processed_data: ProcessedChartData
    chart_title: str
    chart_type: Optional[str]
    llm_provider: str
    max_data_points: int = 100


class LLMResponse(BaseModel):
    """Standardized LLM response model."""
    success: bool
    content: List[str]
    error_message: Optional[str] = None
    provider_used: Optional[str] = None


class KeyMetrics(BaseModel):
    """Statistical metrics extracted from chart data."""
    total_data_points: int
    chart_type: Optional[str]
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    mean_value: Optional[float] = None
    median_value: Optional[float] = None
    std_deviation: Optional[float] = None
    value_range: Optional[float] = None
    total_sum: Optional[float] = None
    unique_categories: Optional[int] = None

"""
Chart Insights Orchestrator

This module orchestrates all chart insight services to generate comprehensive
analysis including executive summary, data insights, hidden patterns, and
recommendations. It provides a unified interface for the API layer.
"""

from datetime import datetime, timezone
from typing import Dict, Any, Optional
from app.core.config import settings
from app.core.logging_config import configure_logging
from app.core.exceptions import InsufficientDataError
from .models import (
    ChartInsightsRequest, 
    ChartInsightsResponse, 
    InsightGenerationContext,
    ProcessedChartData
)
from .data_processor import ChartDataProcessor
from .unified_insights_service import UnifiedInsightsService

logger = configure_logging()


class ChartInsightsOrchestrator:
    """
    Orchestrates all chart insight services to provide comprehensive analysis.
    
    This class coordinates the execution of different insight generation services
    and combines their results into a unified response.
    """
    
    def __init__(self, llm_provider: Optional[str] = None):
        """
        Initialize the orchestrator with unified insights service.

        Args:
            llm_provider: LLM provider to use. If None, uses DEFAULT_LLM_PROVIDER from settings.
        """
        self.llm_provider = llm_provider or settings.DEFAULT_LLM_PROVIDER
        self.data_processor = ChartDataProcessor()

        # Initialize unified insights service (only approach now)
        self.unified_insights_service = UnifiedInsightsService(self.llm_provider)

        logger.info(f"ChartInsightsOrchestrator initialized with LLM provider: {self.llm_provider}, mode: unified")
    
    async def generate_comprehensive_insights(self, request: ChartInsightsRequest) -> ChartInsightsResponse:
        """
        Generate unified chart insights using the streamlined approach.

        Args:
            request: Chart insights request containing data and configuration

        Returns:
            ChartInsightsResponse: Unified insights response
        """
        try:
            logger.info(f"Starting unified insights generation for: {request.chart_title}")

            # Step 1: Process and prepare chart data
            processed_data = self.data_processor.process_chart_data(request.chart_data)

            # Step 2: Create insight generation context
            context = InsightGenerationContext(
                processed_data=processed_data,
                chart_title=request.chart_title,
                chart_type=request.chart_type,
                llm_provider=self.llm_provider
            )

            # Step 3: Generate unified insights
            insights_results = await self.unified_insights_service.generate_unified_insights(context)

            # Step 4: Calculate key metrics
            key_metrics = self.data_processor.calculate_key_metrics(processed_data)

            # Step 5: Create comprehensive response
            response = ChartInsightsResponse(
                success=True,
                chart_insights=insights_results.get("chart_insights", []),
                key_metrics=key_metrics.model_dump(),
                chart_metadata={
                    "chart_type": request.chart_type,
                    "chart_title": request.chart_title,
                    "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
                    "llm_provider_used": self.llm_provider,
                    "insights_mode": "unified",
                    "total_data_points": processed_data.total_points,
                    "insights_count": len(insights_results.get("chart_insights", []))
                }
            )

            logger.info(f"Successfully generated {len(insights_results.get('chart_insights', []))} unified insights for: {request.chart_title}")
            return response

        except Exception as e:
            logger.error(f"Error in unified insights generation: {str(e)}")
            # Let the exception bubble up to be handled by global exception handlers
            raise e
    

    
    def _create_error_response(self, error_message: str) -> ChartInsightsResponse:
        """Create an error response with the given message."""
        return ChartInsightsResponse(
            success=False,
            chart_insights=[f"Error: {error_message}"],
            key_metrics={"error": error_message},
            chart_metadata={
                "error": error_message,
                "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
                "llm_provider_used": self.llm_provider
            }
        )
    
    def get_llm_provider(self) -> str:
        """Get the current LLM provider being used."""
        return self.llm_provider
    
    def get_available_services(self) -> list:
        """Get list of available insight services."""
        return ["unified_chart_insights"]


# Main function to be used by the API layer
async def generate_llm_insights(
    chart_data: Dict[str, Any],
    chart_type: Optional[str] = None,
    chart_title: str = "Chart Analysis",
    llm_provider: Optional[str] = None
) -> Dict[str, Any]:
    """
    Main function to generate unified chart insights.

    This is the primary interface function that should be used by the API layer.
    It creates the orchestrator and generates unified insights.

    Args:
        chart_data: Dictionary containing chart data
        chart_type: Type of chart (bar, line, pie, etc.)
        chart_title: Title of the chart
        llm_provider: LLM provider to use (optional)

    Returns:
        Dict[str, Any]: Unified insights response as dictionary
    """
    try:
        # Create request object
        request = ChartInsightsRequest(
            chart_data=chart_data,
            chart_type=chart_type,
            chart_title=chart_title
        )

        # Create orchestrator and generate insights
        orchestrator = ChartInsightsOrchestrator(llm_provider)
        response = await orchestrator.generate_comprehensive_insights(request)

        # Convert response to dictionary for API compatibility
        return response.model_dump()

    except Exception as e:
        logger.error(f"Error in generate_llm_insights: {str(e)}")
        # Let the exception bubble up to be handled by global exception handlers
        raise e

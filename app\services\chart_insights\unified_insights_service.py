"""
Unified Insights Service

This service combines all chart insights (executive summary, data insights, 
hidden patterns, and recommendations) into a single LLM API call for improved 
efficiency and cost optimization.
"""

import json
from datetime import datetime, timezone
from typing import List, Dict, Any
from app.core.logging_config import configure_logging
from app.core.exceptions import (
    InsufficientDataError, InvalidChartDataError,
    UnsupportedChartTypeError, ChartInsightsLLMError
)
from .models import InsightGenerationContext, LLMResponse
from .llm_client import LLMClientFactory

logger = configure_logging()


class UnifiedInsightsService:
    """Service for generating all chart insights in a single LLM call."""
    
    def __init__(self, llm_provider: str = None):
        self.llm_client = LLMClientFactory.create_client(llm_provider)
        self._used_fallback_parsing = False
        logger.info(f"UnifiedInsightsService initialized with provider: {self.llm_client.get_provider_name()}")
    
    async def generate_unified_insights(self, context: InsightGenerationContext) -> Dict[str, Any]:
        """
        Generate unified chart insights combining data insights, hidden patterns,
        and recommendations into a single comprehensive list.

        Args:
            context: Context containing processed chart data and metadata

        Returns:
            Dict[str, Any]: Dictionary containing unified chart_insights list
        """
        try:
            logger.info(f"Generating unified insights for chart: {context.chart_title}")

            # Enhanced data validation with chart type context
            if not context.processed_data.data_points:
                logger.warning(f"No data points found in processed data for {context.chart_type} chart")
                raise InsufficientDataError("No valid data points found for analysis", context.chart_type)

            # Check for valid numeric values if chart requires them
            if not context.processed_data.numeric_values and context.chart_type not in ['pie', 'doughnut', 'treemap', 'sunburst']:
                logger.warning(f"No valid numeric values found for {context.chart_type} chart analysis")
                raise InvalidChartDataError(f"No valid numeric values found for {context.chart_type} chart analysis")

            # Check minimum data requirements
            if len(context.processed_data.data_points) < 2:
                logger.warning(f"Insufficient data points for {context.chart_type} chart analysis: {len(context.processed_data.data_points)}")
                raise InsufficientDataError(f"Insufficient data points for analysis (found {len(context.processed_data.data_points)}, need at least 2)", context.chart_type)

            # Create unified system prompt
            system_prompt = self._create_unified_prompt(context)
            
            # Generate response using LLM (use unified method for raw JSON response)
            llm_response = await self.llm_client.generate_unified_response(system_prompt)

            if llm_response.success:
                logger.info("Successfully generated unified insights")
                parsed_result = self._parse_unified_response(llm_response.content)

                # If we had to use fallback parsing, try to get JSON format on next attempt
                if self._used_fallback_parsing:
                    logger.info("Previous response used fallback parsing, attempting JSON retry")
                    retry_result = await self._retry_for_json_response(system_prompt)
                    if retry_result:
                        return retry_result

                return parsed_result
            else:
                logger.error(f"Failed to generate unified insights: {llm_response.error_message}")
                raise ChartInsightsLLMError(f"LLM failed to generate insights: {llm_response.error_message}")

        except (InsufficientDataError, InvalidChartDataError, UnsupportedChartTypeError, ChartInsightsLLMError) as e:
            # Re-raise specific chart insights errors
            raise e
        except Exception as e:
            logger.error(f"Error in unified insights generation: {str(e)}")
            raise ChartInsightsLLMError(f"Error generating unified insights: {str(e)}")
    
    def _create_unified_prompt(self, context: InsightGenerationContext) -> str:
        """Create a comprehensive unified system prompt for all insights."""
        
        data_sample = context.processed_data.data_points[:30]  # Balanced sample size
        total_points = context.processed_data.total_points
        chart_type = context.chart_type or 'Unknown'
        chart_title = context.chart_title
        
        # Include statistical and distribution context
        numeric_values = context.processed_data.numeric_values
        categories = context.processed_data.categories
        
        stats_context = ""
        if numeric_values:
            stats_context = f"""
**Statistical Context:**
- Number of numeric values: {len(numeric_values)}
- Min value: {min(numeric_values):.2f}
- Max value: {max(numeric_values):.2f}
- Range: {max(numeric_values) - min(numeric_values):.2f}
"""
        
        distribution_context = ""
        if categories:
            distribution_context += f"\n- Unique categories: {len(categories)}"
        if numeric_values:
            distribution_context += f"\n- Value distribution: Min={min(numeric_values):.2f}, Max={max(numeric_values):.2f}"
        
        return f"""You are a business intelligence expert. Analyze the chart data and provide comprehensive insights in a single unified list.

**Chart Data:**
- Title: {chart_title}
- Type: {chart_type}
- Data Points: {total_points}{distribution_context}
{stats_context}
- Sample: {json.dumps(data_sample, indent=2)}

**CRITICAL INSTRUCTION: Your response must be ONLY valid JSON. No explanations, no markdown, no additional text.**

**Required JSON Format (copy this structure exactly):**
{{
    "chart_insights": [
        "Statistical observation about the data distribution or patterns with specific numbers",
        "Quantitative analysis or comparative insight between data points",
        "Non-obvious relationship or correlation discovered in the data",
        "Underlying trend or behavioral pattern that may not be immediately visible",
        "Specific actionable recommendation based on the data analysis",
        "Strategic suggestion for improvement or optimization with expected outcome"
    ]
}}

**REQUIREMENTS:**
- Generate exactly 5-6 unique insights
- Distribute insights to cover: data analysis, hidden patterns, and actionable recommendations
- Each insight should be relevant to the chart data and provide value
- Include specific numbers and metrics where possible
- Make insights actionable and business-focused
- Ensure all insights are unique and non-repetitive

**EXAMPLE RESPONSE FORMAT:**
{{
    "chart_insights": [
        "Mean value of 150 with median of 148 suggests normal distribution across categories",
        "Peak performance occurs in middle categories with 40% higher values than outliers",
        "Cyclical pattern emerges every 3 data points suggesting seasonal influence on performance",
        "Values cluster around 100-200 range indicating market saturation threshold",
        "Focus marketing efforts on high-performing segments to maximize ROI by 25%",
        "Implement predictive analytics to capitalize on identified cyclical patterns"
    ]
}}

**RESPOND WITH JSON ONLY - START WITH {{ AND END WITH }}**"""
    
    def _get_business_context(self, chart_type: str) -> str:
        """Get relevant business context based on chart type."""
        context_map = {
            # Basic charts
            "bar": "This appears to be categorical data comparison, often used for performance metrics, sales data, or comparative analysis across different segments.",
            "horizontal_bar": "This appears to be categorical data comparison with horizontal orientation, often used for ranking or comparative analysis when category names are long.",
            "column": "This appears to be categorical data comparison with vertical columns, often used for performance metrics and comparative analysis.",
            "pie": "This appears to be proportion or distribution data, often used for market share, budget allocation, or composition analysis.",
            "doughnut": "This appears to be proportion or distribution data with emphasis on the whole-to-part relationship, often used for budget breakdowns or market share analysis.",
            "semi_circle": "This appears to be proportion data displayed in a semi-circular format, often used for gauge-like metrics or progress indicators.",
            "funnel": "This appears to be process or conversion data, often used for sales funnels, conversion rates, or step-by-step process analysis.",
            "polar_area": "This appears to be multi-dimensional categorical data, often used for comparing multiple metrics across categories.",

            # Time series charts
            "line": "This appears to be time-series or trend data, often used for tracking performance over time, forecasting, or identifying patterns.",
            "area": "This appears to be cumulative or stacked data over time, often used for showing composition changes over time or volume trends.",
            "timeline": "This appears to be chronological event data, often used for project timelines, historical analysis, or sequential process tracking.",

            # Numerical charts
            "scatter": "This appears to be correlation or relationship data, often used for identifying relationships between variables, outlier detection, or regression analysis.",
            "bubble": "This appears to be multi-dimensional relationship data, often used for portfolio analysis, risk-return analysis, or three-variable correlations.",
            "histogram": "This appears to be frequency distribution data, often used for understanding data distribution, quality control, or statistical analysis.",

            # Distribution analysis charts
            "box": "This appears to be statistical distribution data, often used for outlier detection, quartile analysis, or comparing distributions across groups.",
            "violin": "This appears to be detailed distribution data, often used for understanding data density, distribution shape, and statistical properties.",

            # Heatmap charts
            "heatmap": "This appears to be intensity or density data, often used for performance matrices, correlation analysis, or geographic data visualization.",
            "calendar_heatmap": "This appears to be time-based intensity data, often used for activity tracking, seasonal analysis, or temporal pattern identification.",

            # Stacked charts
            "stacked_bar": "This appears to be multi-category composition data, often used for showing part-to-whole relationships across different segments.",
            "stacked_line": "This appears to be multi-series time data with cumulative values, often used for tracking multiple metrics over time with total context.",
            "stacked_area": "This appears to be multi-series cumulative data over time, often used for showing contribution of different components to a total.",

            # Multi-series charts
            "multi_line": "This appears to be comparative time-series data, often used for comparing multiple metrics or entities over time.",
            "multi_area": "This appears to be comparative area data over time, often used for showing multiple trends with volume context.",
            "combo_bar_line": "This appears to be mixed metric data, often used for showing different types of measurements (e.g., volume and rate) on the same chart.",

            # Multi-group charts
            "grouped_bar": "This appears to be multi-category comparative data, often used for comparing multiple metrics across different groups or time periods.",

            # Hierarchical charts
            "treemap": "This appears to be hierarchical proportion data, often used for organizational analysis, budget allocation, or nested category analysis.",
            "sunburst": "This appears to be multi-level hierarchical data, often used for drill-down analysis, organizational structures, or nested categorization.",
            "icicle": "This appears to be hierarchical data with linear layout, often used for process flows, organizational hierarchies, or nested data exploration."
        }

        return context_map.get(chart_type.lower(), "This chart represents business data that requires strategic analysis and actionable insights.")

    async def _retry_for_json_response(self, original_prompt: str) -> Dict[str, List[str]]:
        """Retry with a more aggressive JSON-only prompt."""
        try:
            logger.info("Retrying with JSON-focused prompt")

            # Create a very simple, JSON-focused prompt
            json_prompt = f"""
{original_prompt}

CRITICAL: The previous response was not in JSON format. You MUST respond with ONLY valid JSON.

Example of EXACTLY what your response should look like:
{{"chart_insights":["Data insight: Point 1","Hidden pattern: Point 2","Recommendation: Point 3","Data insight: Point 4","Hidden pattern: Point 5"]}}

Respond with JSON only. No explanations. Start with {{ and end with }}.
"""

            retry_response = await self.llm_client.generate_unified_response(json_prompt)
            if retry_response.success:
                retry_result = self._parse_unified_response(retry_response.content)
                if not self._used_fallback_parsing:
                    logger.info("Retry successful - got JSON response")
                    return retry_result

            logger.info("Retry did not improve JSON parsing")
            return None

        except Exception as e:
            logger.warning(f"Retry attempt failed: {str(e)}")
            return None

    def _parse_unified_response(self, content: List[str]) -> Dict[str, List[str]]:
        """Parse the unified LLM response into structured insights."""
        self._used_fallback_parsing = False  # Reset flag

        try:
            # The content should be a list with one JSON string
            if not content or len(content) == 0:
                logger.error("Empty response from LLM")
                raise ChartInsightsLLMError("Empty response from LLM")

            # Get the response text
            response_text = content[0] if isinstance(content, list) else str(content)
            logger.info(f"Raw LLM response (first 500 chars): {response_text[:500]}...")

            # Clean the response (remove markdown code blocks if present)
            cleaned_response = self._clean_json_response(response_text)
            logger.info(f"Cleaned response (first 500 chars): {cleaned_response[:500]}...")

            # Try to parse the JSON response
            try:
                parsed_response = json.loads(cleaned_response)
                logger.info("Successfully parsed JSON response")
            except json.JSONDecodeError:
                # Try to extract JSON from mixed content
                logger.info("Direct JSON parsing failed, attempting to extract JSON from mixed content")
                extracted_json = self._extract_json_from_text(cleaned_response)
                if extracted_json:
                    parsed_response = json.loads(extracted_json)
                    logger.info("Successfully extracted and parsed JSON from mixed content")
                else:
                    raise  # Re-raise the original JSONDecodeError

            # Validate the expected structure for unified insights
            if "chart_insights" not in parsed_response:
                logger.warning("Missing 'chart_insights' key in unified response")
                parsed_response["chart_insights"] = ["Unable to generate chart insights"]
            elif not isinstance(parsed_response["chart_insights"], list):
                logger.warning("'chart_insights' is not a list, converting")
                parsed_response["chart_insights"] = [str(parsed_response["chart_insights"])]

            # Ensure we have 5-6 insights
            insights = parsed_response["chart_insights"]
            if len(insights) < 5:
                logger.warning(f"Only {len(insights)} insights generated, padding to minimum 5")
                while len(insights) < 5:
                    insights.append(f"Additional insight: Further analysis recommended for comprehensive understanding")
            elif len(insights) > 6:
                logger.warning(f"{len(insights)} insights generated, trimming to maximum 6")
                insights = insights[:6]
                parsed_response["chart_insights"] = insights

            logger.info(f"Successfully parsed unified response with {len(insights)} insights")
            return parsed_response

        except json.JSONDecodeError as e:
            logger.warning(f"JSON parsing failed: {str(e)}")
            logger.warning(f"Raw response that failed JSON parsing: {response_text[:500]}...")
            logger.info("Attempting to parse natural language response as fallback")

            # Fallback: Try to parse natural language response
            self._used_fallback_parsing = True
            fallback_result = self._parse_natural_language_response(response_text)
            if fallback_result:
                logger.info("Successfully parsed natural language response")
                return fallback_result
            else:
                logger.error(f"Both JSON and natural language parsing failed")
                logger.error(f"Response text that failed to parse: {response_text[:1000]}...")
                raise ChartInsightsLLMError("Failed to parse LLM response")
        except json.JSONDecodeError as e:
            # This is already handled above, but just in case
            raise ChartInsightsLLMError(f"JSON parsing failed: {str(e)}")
        except Exception as e:
            logger.error(f"Error parsing unified response: {str(e)}")
            raise ChartInsightsLLMError(f"Error parsing response: {str(e)}")

    def _clean_json_response(self, response: str) -> str:
        """Clean JSON response by removing markdown code blocks."""
        response = response.strip()
        if response.startswith('```json'):
            response = response[7:]
        if response.startswith('```'):
            response = response[3:]
        if response.endswith('```'):
            response = response[:-3]
        return response.strip()

    def _extract_json_from_text(self, text: str) -> str:
        """
        Attempt to extract JSON object from mixed text content.
        Looks for content between { and } brackets.
        """
        try:
            # Find the first { and last } to extract potential JSON
            start_idx = text.find('{')
            end_idx = text.rfind('}')

            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                potential_json = text[start_idx:end_idx + 1]
                logger.info(f"Extracted potential JSON: {potential_json[:200]}...")

                # Try to validate it's proper JSON
                json.loads(potential_json)  # This will raise an exception if invalid
                return potential_json

            return None

        except Exception as e:
            logger.warning(f"Failed to extract JSON from text: {str(e)}")
            return None

    def _parse_natural_language_response(self, response_text: str) -> Dict[str, List[str]]:
        """
        Fallback method to parse natural language response when JSON parsing fails.
        This method attempts to extract unified insights from unstructured text.
        """
        try:
            logger.info("Attempting to parse natural language response for unified insights")

            # Split response into sentences for analysis
            sentences = [s.strip() for s in response_text.split('.') if s.strip()]

            # Create unified insights from sentences
            chart_insights = []

            if len(sentences) >= 5:
                # Take up to 6 sentences and format them as insights
                selected_sentences = sentences[:6]

                for i, sentence in enumerate(selected_sentences):
                    # Add prefixes to categorize insights
                    if i % 3 == 0:
                        prefix = "Data insight: "
                    elif i % 3 == 1:
                        prefix = "Hidden pattern: "
                    else:
                        prefix = "Recommendation: "

                    # Clean and format the sentence
                    cleaned_sentence = sentence.strip()[:200]
                    if cleaned_sentence:
                        chart_insights.append(f"{prefix}{cleaned_sentence}")
            else:
                # If too few sentences, create generic insights
                content_summary = response_text[:300] + "..." if len(response_text) > 300 else response_text

                chart_insights = [
                    f"Data insight: Analysis shows key patterns in the data: {content_summary}",
                    f"Hidden pattern: Underlying trends identified in the dataset",
                    f"Recommendation: Further analysis recommended for comprehensive understanding",
                    f"Data insight: Statistical patterns observed in the chart data",
                    f"Hidden pattern: Correlations detected between data points"
                ]

            # Ensure we have 5-6 insights
            while len(chart_insights) < 5:
                chart_insights.append("Additional insight: Further analysis recommended for comprehensive understanding")

            if len(chart_insights) > 6:
                chart_insights = chart_insights[:6]

            result = {"chart_insights": chart_insights}
            logger.info(f"Successfully parsed natural language response with {len(chart_insights)} insights")
            return result

        except Exception as e:
            logger.error(f"Error parsing natural language response: {str(e)}")
            return None
    
    def _create_insufficient_data_response(self) -> Dict[str, Any]:
        """Create response for insufficient data scenarios."""
        error_message = "No valid data points found for analysis"
        return {
            "success": False,
            "chart_insights": [f"Error: {error_message}"],
            "key_metrics": {"error": error_message},
            "chart_metadata": {
                "error": error_message,
                "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
                "llm_provider_used": self.llm_client.get_provider_name()
            }
        }

    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create error response for unified insights."""
        return {
            "success": False,
            "chart_insights": [f"Error generating insights: {error_message}"],
            "key_metrics": {"error": error_message},
            "chart_metadata": {
                "error": error_message,
                "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
                "llm_provider_used": self.llm_client.get_provider_name()
            }
        }
    
    def get_service_name(self) -> str:
        """Get the service name for logging and identification."""
        return "UnifiedInsightsService"
    
    def get_llm_provider(self) -> str:
        """Get the current LLM provider being used."""
        return self.llm_client.get_provider_name()

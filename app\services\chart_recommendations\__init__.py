"""
Chart Recommendations Package.

This package contains modules for different types of chart recommendations.
Each module is responsible for generating recommendations for a specific type of chart.
"""

from app.services.chart_recommendations.base import ChartRecommendationBase
from app.services.chart_recommendations.hierarchical_charts import HierarchicalChartR<PERSON>ommender
from app.services.chart_recommendations.stacked_charts import StackedChartRecommender
from app.services.chart_recommendations.multi_charts import MultiChartRecommender
from app.services.chart_recommendations.numerical_charts import NumericalChartRecommender
from app.services.chart_recommendations.basic_charts import Basic<PERSON>hartRecommender
from app.services.chart_recommendations.time_series_charts import TimeSeriesChartRecommender
from app.services.chart_recommendations.multi_group_charts import MultiGroupChartRecommender
from app.services.chart_recommendations.box_plots import <PERSON><PERSON>lotRecommender
from app.services.chart_recommendations.heatmap_charts import Heatmap<PERSON>hartRecommender

__all__ = [
    'ChartRecommendationBase',
    'HierarchicalChartRecommender',
    'StackedChartRecommender',
    'MultiChartRecommender',
    'NumericalChartRecommender',
    'BasicChartR<PERSON>ommender',
    'TimeSeriesChartRecommender',
    'MultiGroupChartRecommender',
    'BoxPlotRecommender',
    'HeatmapChartRecommender',
]

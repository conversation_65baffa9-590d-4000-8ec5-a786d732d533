"""
Base module for chart recommendations.

This module contains the base class for all chart recommendation modules.
"""

from typing import List, Dict, Optional
from logging import getLogger
from app.core.logging_config import configure_logging

logger = configure_logging()

class ChartRecommendationBase:
    """Base class for chart recommendations."""

    def __init__(self):
        """Initialize the chart recommender."""
        self.CATEGORY_THRESHOLDS = {
            "pie": 8,        # Max categories for readable pie chart
            "doughnut": 8,   # Max categories for readable doughnut chart
            "bar": 15,       # Max categories for readable bar chart
            "heatmap": 20,   # Max categories per axis for heatmap
            "box": 12,       # Max categories for box plot
            "scatter": 10,   # Max categories for scatter plot color coding
            "bubble": 10,    # Max categories for bubble chart color coding
            "line": 8        # Max lines to show in line chart
        }

        self.ADVANCED_THRESHOLDS = {
            "treemap": 100,          # Hierarchical data with many categories
            "grouped_bar": 30,       # Categories grouped by another variable
            "stacked_bar": 25,       # Stacked categories with clear patterns
            "sunburst": 100,         # Hierarchical data visualization
            "icicle": 50,            # Hierarchical data with fewer categories than treemap
            "packed_bubbles": 50,    # Size-based grouping of categories
            "bubble": 20,            # Bubble chart with size encoding
            "table": 1000            # Paginated tabular view
        }

    def get_recommendations(self, metadata: Dict) -> List[Dict]:
        """
        Get chart recommendations based on data characteristics.

        Args:
            metadata: The metadata dictionary containing information about the dataset

        Returns:
            List of chart recommendations
        """
        # This method should be implemented by subclasses
        raise NotImplementedError("Subclasses must implement get_recommendations method")

    def _format_datetime_columns(self, date_col: str, time_agg: str) -> str:
        """
        Format datetime columns for display.

        Args:
            date_col: The datetime column name
            time_agg: The time aggregation level (daily, monthly, quarterly, yearly)

        Returns:
            The formatted date format string
        """
        # Use user-friendly date formats
        if time_agg == 'monthly':
            return '%b %Y'  # "Jan 2024"
        elif time_agg == 'quarterly':
            return 'Q%q %Y'  # "Q1 2024" - frontend will need to handle this format
        elif time_agg == 'yearly':
            return '%Y'
        else:  # daily
            return '%d %b %Y'  # "01 Jan 2024"

    def _get_recommended_time_aggregation(self, date_metadata: dict) -> str:
        """Get recommended time aggregation based on date range."""
        try:
            logger.info(f"Date Metadata: {date_metadata}")
            for field, meta in date_metadata.items():
                start_date_str = meta.get('min_date') or meta.get('min') or meta.get('start_date')
                end_date_str = meta.get('max_date') or meta.get('max') or meta.get('end_date')

                if not (start_date_str and end_date_str):
                    logger.debug(f"Missing date range for field {field}")
                    continue

                try:
                    import pandas as pd
                    start_date = pd.to_datetime(start_date_str)
                    end_date = pd.to_datetime(end_date_str)
                    date_diff = (end_date - start_date).days
                    logger.info(f"Date difference for {field}: {date_diff} days")

                    if date_diff > 730:  # More than 2 years
                        logger.info(f"Using yearly aggregation for {field}")
                        return "yearly"
                    elif date_diff > 365:  # More than 1 year
                        logger.info(f"Using quarterly aggregation for {field}")
                        return "quarterly"
                    elif date_diff > 30:  # More than 30 days
                        logger.info(f"Using monthly aggregation for {field}")
                        return "monthly"
                    elif date_diff > 0:
                        logger.info(f"Using daily aggregation for {field}")
                        return "daily"
                    else:
                        logger.info(f"Using hourly aggregation for {field}")
                        return "hourly"
                except (ValueError, TypeError) as e:
                    logger.error(f"Error processing dates for field {field}: {e}")
                    continue

            # If we get here, try to calculate date range from unique_dates
            for field, meta in date_metadata.items():
                unique_dates = meta.get('unique_dates', 0)
                if unique_dates > 100:
                    logger.info(f"Using quarterly aggregation based on {unique_dates} unique dates")
                    return "quarterly"
                elif unique_dates > 30:
                    logger.info(f"Using monthly aggregation based on {unique_dates} unique dates")
                    return "monthly"
                elif unique_dates > 0:
                    logger.info(f"Using daily aggregation based on {unique_dates} unique dates")
                    return "daily"

            logger.info("No valid date range found, defaulting to quarterly aggregation")
            return "quarterly"  # Default to quarterly for better visualization of date ranges
        except Exception as e:
            logger.error(f"Error in _get_recommended_time_aggregation: {e}")
            return "quarterly"  # Safe default

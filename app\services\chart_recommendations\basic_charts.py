"""
Basic chart recommendations module.

This module contains functions for recommending basic charts like pie, doughnut, and bar.
"""

from typing import List, Dict
from app.core.logging_config import configure_logging
from app.services.chart_recommendations.base import ChartRecommendationBase

logger = configure_logging()

class BasicChartRecommender(ChartRecommendationBase):
    """Class for basic chart recommendations."""

    def _calculate_column_suitability_score(self, unique_count: int, avg_text_length: float, null_ratio: float) -> float:
        """
        Calculate a suitability score for a categorical column based on multiple factors.

        Args:
            unique_count: Number of unique values in the column
            avg_text_length: Average text length of values
            null_ratio: Ratio of null values

        Returns:
            Suitability score (higher is better)
        """
        try:
            # Base score based on unique count (prefer 2-10 categories)
            if 2 <= unique_count <= 6:
                count_score = 100
            elif 7 <= unique_count <= 10:
                count_score = 80
            elif 11 <= unique_count <= 20:
                count_score = 60
            elif 21 <= unique_count <= 50:
                count_score = 40
            else:
                count_score = 20

            # Text length score (prefer moderate length for readability)
            if 3 <= avg_text_length <= 15:
                text_score = 20
            elif 16 <= avg_text_length <= 30:
                text_score = 15
            elif avg_text_length < 3:
                text_score = 10  # Very short text might be codes
            else:
                text_score = 5   # Very long text is hard to display

            # Data quality score (penalize high null ratios)
            quality_score = max(0, 20 * (1 - null_ratio))

            total_score = count_score + text_score + quality_score
            return total_score

        except Exception as e:
            logger.warning(f"Error calculating suitability score: {e}")
            return 50  # Default moderate score

    def _get_average_text_length(self, column_name: str, sample_data: List[Dict]) -> float:
        """
        Get average text length for a categorical column.

        Args:
            column_name: Name of the column
            sample_data: Sample data from metadata

        Returns:
            Average text length of the column values
        """
        try:
            for col_info in sample_data:
                if col_info.get("column") == column_name:
                    sample_values = col_info.get("sample", [])
                    if sample_values:
                        total_length = sum(len(str(val)) for val in sample_values)
                        return total_length / len(sample_values)
            return 0.0
        except Exception as e:
            logger.warning(f"Error calculating average text length for {column_name}: {e}")
            return 0.0

    def _generate_chart_title(self, chart_type: str, column_name: str) -> str:
        """
        Generate a descriptive chart title based on chart type and column name.

        Args:
            chart_type: The type of chart being created
            column_name: The name of the column being visualized

        Returns:
            Formatted chart title
        """
        # Clean up column name for display
        display_name = column_name.replace('_', ' ').title()

        # Chart type specific titles
        title_templates = {
            "pie": f"{display_name} Distribution",
            "doughnut": f"{display_name} Breakdown",
            "semi_circle": f"{display_name} Overview",
            "horizontal_bar": f"{display_name} Comparison",
            "bar": f"{display_name} Analysis",
            "column": f"{display_name} Distribution",
            "funnel": f"{display_name} Funnel",
            "polar_area": f"{display_name} Polar View"
        }

        return title_templates.get(chart_type, f"{display_name} Distribution")

    def _generate_unique_combinations(self, numerical_cols: List[str], valid_basic_cat_cols: List) -> List:
        """
        Generate unique combinations of categorical + count and categorical + numerical columns.

        Args:
            numerical_cols: List of prioritized numerical columns
            valid_basic_cat_cols: List of valid categorical columns with their characteristics

        Returns:
            List of unique combinations (cat_col, numeric_field, aggregation, unique_count, avg_text_length)
        """
        combinations = []

        # Create combinations for each valid categorical column
        for cat_col, unique_count, avg_text_length in valid_basic_cat_cols:
            # Add count-based combination (category + count)
            combinations.append((cat_col, "count", "count", unique_count, avg_text_length))

            # Add numerical column combinations (category + numerical with aggregations)
            for num_col in numerical_cols[:5]:  # Limit to top 5 numerical columns
                # Add Sum aggregation
                combinations.append((cat_col, num_col, "sum", unique_count, avg_text_length))

                # Add Average aggregation
                combinations.append((cat_col, num_col, "avg", unique_count, avg_text_length))

        logger.debug(f"Generated {len(combinations)} total combinations from {len(valid_basic_cat_cols)} "
                    f"categorical and {len(numerical_cols)} numerical columns")

        return combinations

    def _select_best_combination(self, combinations: List, used_combinations: set,
                               chart_config: Dict, prefer_long_text: bool = False):
        """
        Select the best unused combination for a specific chart type.

        Args:
            combinations: List of available combinations
            used_combinations: Set of already used combinations
            chart_config: Chart configuration with min/max categories and optimal range
            prefer_long_text: Whether to prefer combinations with longer text

        Returns:
            Best combination tuple or None if no suitable combination found
        """
        min_cat = chart_config["min_categories"]
        max_cat = chart_config["max_categories"]
        optimal_min, optimal_max = chart_config["optimal_range"]

        suitable_combinations = []

        for combo in combinations:
            cat_col, numeric_field, aggregation, unique_count, avg_text_length = combo
            combination_key = (cat_col, numeric_field, aggregation)

            # Skip if combination already used
            if combination_key in used_combinations:
                continue

            # Check if unique count fits chart requirements
            if not (min_cat <= unique_count <= max_cat):
                continue

            # Calculate suitability score
            if prefer_long_text:
                score = avg_text_length  # Higher score for longer text
            else:
                # Prefer optimal category count range
                if optimal_min <= unique_count <= optimal_max:
                    score = 100 - abs(unique_count - (optimal_min + optimal_max) / 2)
                else:
                    score = 50 - abs(unique_count - (optimal_min + optimal_max) / 2)

            # Bonus for count-based combinations (simpler and often more meaningful)
            if aggregation == "count":
                score += 10

            suitable_combinations.append((combo, score))

        # Sort by score (descending) and return the best combination
        if suitable_combinations:
            suitable_combinations.sort(key=lambda x: x[1], reverse=True)
            return suitable_combinations[0][0]  # Return the combination tuple

        return None

    def _generate_enhanced_chart_title(self, chart_type: str, cat_col: str, numeric_field: str, aggregation: str) -> str:
        """
        Generate enhanced chart title for category + numerical combinations.

        Args:
            chart_type: The type of chart being created
            cat_col: Categorical column name
            numeric_field: Numerical field name or "count"
            aggregation: Aggregation type (count, sum, avg)

        Returns:
            Formatted chart title
        """
        # Clean up column names for display
        cat_display = cat_col.replace('_', ' ').title()

        if numeric_field == "count":
            # Count-based charts
            title_templates = {
                "pie": f"{cat_display} Distribution",
                "doughnut": f"{cat_display} Breakdown",
                "semi_circle": f"{cat_display} Overview",
                "horizontal_bar": f"{cat_display} Count",
                "bar": f"{cat_display} Analysis",
                "column": f"{cat_display} Distribution",
                "funnel": f"{cat_display} Funnel",
                "polar_area": f"{cat_display} Polar View"
            }
        else:
            # Numerical aggregation charts
            num_display = numeric_field.replace('_', ' ').title()
            agg_display = {"sum": "Total", "avg": "Average"}.get(aggregation, aggregation.title())

            title_templates = {
                "pie": f"{agg_display} {num_display} by {cat_display}",
                "doughnut": f"{agg_display} {num_display} by {cat_display}",
                "semi_circle": f"{agg_display} {num_display} by {cat_display}",
                "horizontal_bar": f"{agg_display} {num_display} by {cat_display}",
                "bar": f"{agg_display} {num_display} by {cat_display}",
                "column": f"{agg_display} {num_display} by {cat_display}",
                "funnel": f"{agg_display} {num_display} by {cat_display}",
                "polar_area": f"{agg_display} {num_display} by {cat_display}"
            }

        return title_templates.get(chart_type, f"{cat_display} Analysis")

    def get_recommendations(self, metadata: Dict) -> List[Dict]:
        """
        Get basic chart recommendations based on data characteristics with enhanced uniqueness logic.

        Args:
            metadata: The metadata dictionary containing information about the dataset

        Returns:
            List of basic chart recommendations with unique column combinations
        """
        recommendations = []

        try:
            # Safely get columns from metadata with defaults
            categorical_columns = metadata.get("categorical_columns", [])
            numerical_columns = metadata.get("numerical_columns", [])
            unique_counts = metadata.get("unique_counts", {})
            sample_data = metadata.get("sample_data", [])
            column_characteristics = metadata.get("column_characteristics", {})

            logger.debug(f"BasicChartRecommender: Processing {len(categorical_columns)} categorical, {len(numerical_columns)} numerical columns")

            # Enhanced categorical column prioritization with multiple criteria
            prioritized_categorical = []
            if categorical_columns:
                # Get detailed characteristics for each categorical column
                cat_analysis = []
                for col in categorical_columns:
                    unique_count = unique_counts.get(col, 0)
                    if unique_count > 1:  # Filter out columns with only one unique value
                        char_info = column_characteristics.get(col, {})
                        avg_text_length = char_info.get("avg_text_length", 0)
                        null_ratio = char_info.get("null_ratio", 0)

                        # Calculate suitability score based on multiple factors
                        suitability_score = self._calculate_column_suitability_score(
                            unique_count, avg_text_length, null_ratio
                        )

                        cat_analysis.append({
                            "column": col,
                            "unique_count": unique_count,
                            "avg_text_length": avg_text_length,
                            "null_ratio": null_ratio,
                            "suitability_score": suitability_score
                        })

                # Sort by suitability score (descending) then by unique count (ascending)
                cat_analysis.sort(key=lambda x: (-x["suitability_score"], x["unique_count"]))
                prioritized_categorical = [item["column"] for item in cat_analysis]

                logger.debug(f"Categorical column analysis:")
                for item in cat_analysis[:5]:  # Show top 5
                    logger.debug(f"  - {item['column']}: {item['unique_count']} unique, "
                               f"avg_text_len={item['avg_text_length']:.1f}, "
                               f"score={item['suitability_score']:.1f}")

                logger.info(f"Prioritized {len(prioritized_categorical)} categorical columns for basic charts")

            # Prioritize numerical columns for aggregations
            prioritized_numerical = []
            if numerical_columns:
                # Get characteristics for numerical columns
                num_analysis = []
                for col in numerical_columns:
                    char_info = column_characteristics.get(col, {})
                    null_ratio = char_info.get("null_ratio", 0)

                    # Prefer columns with less null values
                    suitability_score = 100 * (1 - null_ratio)

                    num_analysis.append({
                        "column": col,
                        "null_ratio": null_ratio,
                        "suitability_score": suitability_score
                    })

                # Sort by suitability score (descending)
                num_analysis.sort(key=lambda x: -x["suitability_score"])
                prioritized_numerical = [item["column"] for item in num_analysis]

                logger.info(f"Prioritized {len(prioritized_numerical)} numerical columns for basic charts")

            # Get valid categorical columns with at least 2 unique values and their characteristics
            valid_basic_cat_cols = []
            for cat_col in prioritized_categorical:
                unique_count = unique_counts.get(cat_col, 0)
                if isinstance(unique_count, (int, float)) and unique_count >= 2:
                    avg_text_length = self._get_average_text_length(cat_col, sample_data)
                    valid_basic_cat_cols.append((cat_col, unique_count, avg_text_length))

            if not valid_basic_cat_cols:
                logger.debug("No valid categorical columns found for basic charts")
                return recommendations

            logger.debug(f"Valid categorical columns for basic charts:")
            for col, unique_count, avg_text_length in valid_basic_cat_cols[:5]:  # Show top 5
                logger.debug(f"  - {col}: {unique_count} unique, avg_text_len={avg_text_length:.1f}")

            # Define chart types with their optimal characteristics
            chart_types = [
                {
                    "type": "pie",
                    "min_categories": 2,
                    "max_categories": 7,
                    "optimal_range": (3, 6),
                    "priority": 1
                },
                {
                    "type": "doughnut",
                    "min_categories": 3,
                    "max_categories": 8,
                    "optimal_range": (4, 7),
                    "priority": 2
                },
                {
                    "type": "semi_circle",
                    "min_categories": 2,
                    "max_categories": 6,
                    "optimal_range": (3, 5),
                    "priority": 3
                },
                {
                    "type": "horizontal_bar",
                    "min_categories": 2,
                    "max_categories": 8,
                    "optimal_range": (3, 8),
                    "priority": 4,
                    "prefer_long_text": True  # Prefer columns with longer text
                },
                {
                    "type": "bar",
                    "min_categories": 9,
                    "max_categories": 15,
                    "optimal_range": (9, 15),
                    "priority": 5
                },
                {
                    "type": "column",  # Vertical bar chart
                    "min_categories": 2,
                    "max_categories": 12,
                    "optimal_range": (4, 10),
                    "priority": 6
                },
                {
                    "type": "funnel",
                    "min_categories": 3,
                    "max_categories": 8,
                    "optimal_range": (4, 6),
                    "priority": 7
                },
                {
                    "type": "polar_area",
                    "min_categories": 3,
                    "max_categories": 8,
                    "optimal_range": (4, 7),
                    "priority": 8
                }
            ]

            # Generate unique combinations of categorical + count and categorical + numerical
            unique_combinations = self._generate_unique_combinations(
                prioritized_numerical, valid_basic_cat_cols
            )

            logger.debug(f"Generated {len(unique_combinations)} unique combinations for basic charts")
            logger.debug("Sample combinations:")
            for i, combo in enumerate(unique_combinations[:3]):  # Show first 3
                cat_col, numeric_field, aggregation, unique_count, avg_text_length = combo
                logger.debug(f"  [{i+1}] {cat_col} + {numeric_field} ({aggregation}) - {unique_count} categories")

            # Enhanced chart generation with unique combinations
            used_combinations = set()  # Track (category, numeric_field, aggregation) combinations

            for chart_config in chart_types:
                chart_type = chart_config["type"]
                prefer_long_text = chart_config.get("prefer_long_text", False)

                logger.debug(f"Evaluating chart type: {chart_type} (categories: {chart_config['min_categories']}-{chart_config['max_categories']})")

                # Find the best unused combination for this chart type
                selected_combination = self._select_best_combination(
                    unique_combinations, used_combinations, chart_config, prefer_long_text
                )

                if selected_combination:
                    cat_col, numeric_field, aggregation, unique_count, avg_text_length = selected_combination

                    # Mark this combination as used
                    combination_key = (cat_col, numeric_field, aggregation)
                    used_combinations.add(combination_key)

                    # Create descriptive chart title
                    chart_title = self._generate_enhanced_chart_title(chart_type, cat_col, numeric_field, aggregation)

                    recommendation = {
                        "chart_type": chart_type,
                        "fields": {
                            "x": cat_col,
                            "x_type": "categorical",
                            "numeric": numeric_field,
                            "aggregation": aggregation,
                            "chart_title": chart_title
                        }
                    }

                    recommendations.append(recommendation)
                    logger.debug(f"✓ Added {chart_type} chart: {cat_col} + {numeric_field} ({aggregation}) "
                              f"({unique_count} categories, score based on {avg_text_length:.1f} avg text length)")
                else:
                    logger.debug(f"✗ No suitable unused combination found for chart type '{chart_type}'")

            return recommendations

        except Exception as e:
            logger.error(f"Error in BasicChartRecommender.get_recommendations(): {str(e)}", exc_info=True)
            return []

"""
Box plot recommendations module.

This module contains functions for recommending box plots.
"""

from typing import List, Dict, Optional
from logging import getLogger
from app.core.logging_config import configure_logging
from app.services.chart_recommendations.base import ChartRecommendationBase

logger = configure_logging()

class BoxPlotRecommender(ChartRecommendationBase):
    """Class for box plot recommendations."""
    
    def get_recommendations(self, metadata: Dict) -> List[Dict]:
        """
        Get box plot recommendations based on data characteristics.

        Args:
            metadata: The metadata dictionary containing information about the dataset

        Returns:
            List of box plot recommendations
        """
        recommendations = []

        try:
            # Safely get columns from metadata with defaults
            numerical_columns = metadata.get("numerical_columns", [])
            categorical_columns = metadata.get("categorical_columns", [])
            unique_counts = metadata.get("unique_counts", {})
        
            # Prioritize categorical columns by unique count
            prioritized_categorical = []
            if categorical_columns:
                # Get unique counts for each categorical column
                cat_unique_counts = {col: unique_counts.get(col, 0) for col in categorical_columns}

                # Filter out columns with only one unique value
                valid_categorical = [col for col in categorical_columns if cat_unique_counts.get(col, 0) > 1]

                # Sort by unique count (ascending)
                prioritized_categorical = sorted(valid_categorical, key=lambda col: cat_unique_counts.get(col, float('inf')))

            # Box plots for categorical vs numerical (prioritized)
            if prioritized_categorical and numerical_columns:
                # Find valid categorical columns for box plots
                valid_box_cat_cols = []
                for cat_col in prioritized_categorical:
                    unique_count = unique_counts.get(cat_col, 0)
                    # Ensure the categorical column has at least 2 but not too many unique values
                    if unique_count >= 2 and unique_count <= 15:  # Reasonable number of categories for box plot
                        valid_box_cat_cols.append(cat_col)

                # Use different categorical columns for each numerical column when possible
                if valid_box_cat_cols:
                    for i, num_col in enumerate(numerical_columns[:3]):  # Limit to first 3 numerical columns
                        # Select a different categorical column for each numerical column if possible
                        cat_col_index = min(i, len(valid_box_cat_cols) - 1)
                        cat_col = valid_box_cat_cols[cat_col_index]

                        recommendations.append({
                            "chart_type": "box",
                            "fields": {
                                "x": cat_col,
                                "x_type": "categorical",
                                "numeric": num_col,
                                "chart_title": f"{num_col} by {cat_col}"
                            }
                        })
        
            return recommendations

        except Exception as e:
            logger.error(f"Error in BoxPlotRecommender.get_recommendations(): {str(e)}", exc_info=True)
            return []
    
    def _analyze_distribution(
        self, num_col: str, categorical_columns: List[str], unique_counts: Dict
    ) -> List[Dict]:
        """
        Analyze numerical distributions and their relationship with categories.
        
        Args:
            num_col: The numerical column to analyze
            categorical_columns: List of categorical columns
            unique_counts: Dictionary of unique counts for each column
            
        Returns:
            List of box plot recommendations
        """
        recommendations = []
        
        for cat_col in categorical_columns:
            if unique_counts.get(cat_col, 0) <= self.CATEGORY_THRESHOLDS["box"]:
                recommendations.append({
                    "chart_type": "box",
                    "fields": {
                        "x": cat_col,
                        "y": num_col
                    },
                    "options": {
                        "notched": True,
                        "points": "outliers"
                    }
                })
        
        return recommendations

"""
Hierarchical chart recommendations module.

This module contains functions for recommending hierarchical charts like treemap, sunburst, and icicle.
"""

from typing import List, Dict, Optional, Tuple
from logging import getLogger
import pandas as pd
from app.core.logging_config import configure_logging
from app.services.chart_recommendations.base import ChartRecommendationBase

logger = configure_logging()

class HierarchicalChartRecommender(ChartRecommendationBase):
    """Class for hierarchical chart recommendations."""

    def get_recommendations(self, metadata: Dict) -> List[Dict]:
        """
        Get hierarchical chart recommendations based on data characteristics.

        Args:
            metadata: The metadata dictionary containing information about the dataset

        Returns:
            List of hierarchical chart recommendations
        """
        recommendations = []

        try:
            # Safely get columns from metadata with defaults
            categorical_columns = metadata.get("categorical_columns", [])
            numerical_columns = metadata.get("numerical_columns", [])
            datetime_columns = metadata.get("datetime_columns", [])
            unique_counts = metadata.get("unique_counts", {})

            # Prioritize categorical columns by unique count
            prioritized_categorical = []
            if categorical_columns:
                # Get unique counts for each categorical column
                cat_unique_counts = {col: unique_counts.get(col, 0) for col in categorical_columns}

                # Filter out columns with only one unique value
                valid_categorical = [col for col in categorical_columns if cat_unique_counts.get(col, 0) > 1]

                # Sort by unique count (ascending)
                prioritized_categorical = sorted(valid_categorical, key=lambda col: cat_unique_counts.get(col, float('inf')))


            # Step 1: Detect potential hierarchical relationships using data analysis
            # This is more reliable than just using column names
            hierarchical_relationships = []
            # Only proceed if we have at least 2 categorical columns
            if prioritized_categorical and len(prioritized_categorical) >= 2:
                # For hierarchical charts, we need at least one categorical column
                # Focus on detecting hierarchical relationships in the data

                

                # Common hierarchical column patterns to check
                common_hierarchies = [
                    ['department', 'incident_type', 'severity'],
                    ['department', 'severity'],
                    ['department', 'incident_type'],
                    ['incident_type', 'severity'],
                    ['category', 'subcategory'],
                    ['region', 'country', 'city'],
                    ['region', 'state', 'city'],
                    ['year', 'quarter', 'month'],
                    ['group', 'subgroup'],
                    ['division', 'team']
                ]

                # Check if any of our columns match common hierarchical patterns
                # Try to find matching hierarchies from common patterns
                for hierarchy in common_hierarchies:
                    matching_cols = []
                    for pattern in hierarchy:
                        matching = [col for col in prioritized_categorical if pattern in (str(col).lower() if not pd.isna(col) else '')]
                        if matching:
                            matching_cols.append(matching[0])

                    if len(matching_cols) >= 2:
                        hierarchical_relationships.append(matching_cols)
                        logger.info(f"Found potential hierarchy based on common patterns: {' > '.join(matching_cols)}")

            # Step 2: Check for parent-child relationships in column names
            # This is a fallback if data analysis doesn't yield results
            parent_child_pairs = []
            for col1 in prioritized_categorical:
                for col2 in prioritized_categorical:
                    if col1 != col2:
                        # Check if one column name is contained within the other
                        # e.g., "Category" and "SubCategory", "Department" and "Department Manager"
                        # Safely convert column names to lowercase
                        col1_lower = str(col1).lower() if not pd.isna(col1) else ''
                        col2_lower = str(col2).lower() if not pd.isna(col2) else ''

                        if col1_lower in col2_lower or col2_lower in col1_lower:
                            # Determine which is likely the parent
                            if len(col1) < len(col2) and col1_lower in col2_lower:
                                parent, child = col1, col2
                            else:
                                parent, child = col2, col1
                            parent_child_pairs.append((parent, child))

            if parent_child_pairs:
                logger.info(f"Found parent-child relationships based on column names: {parent_child_pairs}")

                # Try to build multi-level hierarchies from parent-child pairs
                # Start with each pair and try to extend it
                for start_parent, start_child in parent_child_pairs:
                    hierarchy_chain = [start_parent, start_child]

                    # Look for chains where the child becomes a parent
                    current_child = start_child
                    while True:
                        next_level = False
                        for p, c in parent_child_pairs:
                            if p == current_child and c not in hierarchy_chain:
                                hierarchy_chain.append(c)
                                current_child = c
                                next_level = True
                                break

                        if not next_level:
                            break

                    if len(hierarchy_chain) >= 2:
                        hierarchical_relationships.append(hierarchy_chain)
                        logger.info(f"Built hierarchy chain from column names: {' > '.join(hierarchy_chain)}")

            # Step 3: If we still don't have hierarchies, create some based on cardinality
            # Columns with fewer unique values are typically higher in the hierarchy
            if not hierarchical_relationships and len(prioritized_categorical) >= 2:
                # Sort by unique count (ascending)
                sorted_cols = sorted([(col, unique_counts.get(col, 0)) for col in prioritized_categorical],
                                    key=lambda x: x[1])

                # Create hierarchies with 2-3 levels based on cardinality
                if len(sorted_cols) >= 3:
                    # Create a 3-level hierarchy with the columns having the fewest unique values at the top
                    three_level = [col for col, _ in sorted_cols[:3]]
                    hierarchical_relationships.append(three_level)
                    logger.info(f"Created 3-level hierarchy based on cardinality: {' > '.join(three_level)}")

                # Also create a 2-level hierarchy with different columns if possible
                if len(sorted_cols) >= 2:
                    two_level = [col for col, _ in sorted_cols[:2]]
                    if two_level not in hierarchical_relationships:
                        hierarchical_relationships.append(two_level)
                        logger.info(f"Created 2-level hierarchy based on cardinality: {' > '.join(two_level)}")

            # Step 4: Generate hierarchical chart recommendations
            value_col = numerical_columns[0] if numerical_columns else None

            # Create different hierarchies for each chart type if possible
            if hierarchical_relationships:
                # Use different hierarchies for each chart type if available
                num_hierarchies = len(hierarchical_relationships)

                # Treemap recommendation (use the longest hierarchy if available)
                treemap_hierarchy = sorted(hierarchical_relationships, key=len, reverse=True)[0]

                # Check if any hierarchy column is a datetime column
                treemap_fields = {
                    "hierarchy": treemap_hierarchy,
                    "numeric": value_col if value_col else "count",
                    "chart_title": f"Treemap of {' > '.join(treemap_hierarchy)}"
                }

                # Add time_agg parameter if any hierarchy column is a datetime column
                for col in treemap_hierarchy:
                    if col in datetime_columns:
                        treemap_fields["time_agg"] = "monthly"  # Default to monthly for datetime columns
                        break

                recommendations.append({
                    "chart_type": "treemap",
                    "fields": treemap_fields
                })
                logger.info(f"Added treemap with hierarchy: {treemap_hierarchy}")

                # Sunburst recommendation (use a different hierarchy if available)
                if num_hierarchies > 1:
                    sunburst_hierarchy = hierarchical_relationships[1]
                else:
                    sunburst_hierarchy = hierarchical_relationships[0]

                # Check if any hierarchy column is a datetime column
                sunburst_fields = {
                    "hierarchy": sunburst_hierarchy,
                    "numeric": value_col if value_col else "count",
                    "chart_title": f"Sunburst of {' > '.join(sunburst_hierarchy)}"
                }

                # Add time_agg parameter if any hierarchy column is a datetime column
                for col in sunburst_hierarchy:
                    if col in datetime_columns:
                        sunburst_fields["time_agg"] = "monthly"  # Default to monthly for datetime columns
                        break

                recommendations.append({
                    "chart_type": "sunburst",
                    "fields": sunburst_fields
                })
                logger.info(f"Added sunburst with hierarchy: {sunburst_hierarchy}")

                # Icicle recommendation (use a different hierarchy if available)
                if num_hierarchies > 2:
                    icicle_hierarchy = hierarchical_relationships[2]
                else:
                    icicle_hierarchy = hierarchical_relationships[0]

                # Check if any hierarchy column is a datetime column
                icicle_fields = {
                    "hierarchy": icicle_hierarchy,
                    "numeric": value_col if value_col else "count",
                    "chart_title": f"Icicle of {' > '.join(icicle_hierarchy)}"
                }

                # Add time_agg parameter if any hierarchy column is a datetime column
                for col in icicle_hierarchy:
                    if col in datetime_columns:
                        icicle_fields["time_agg"] = "monthly"  # Default to monthly for datetime columns
                        break

                recommendations.append({
                    "chart_type": "icicle",
                    "fields": icicle_fields
                })
                logger.info(f"Added icicle with hierarchy: {icicle_hierarchy}")

            # If we didn't find any hierarchical relationships, generate single-level charts
            # with different categorical columns for each chart type
            elif prioritized_categorical:
                self._add_single_level_hierarchical_charts(
                    recommendations,
                    prioritized_categorical,
                    unique_counts,
                    numerical_columns
                )

            return recommendations

        except Exception as e:
            logger.error(f"Error in HierarchicalChartRecommender.get_recommendations(): {str(e)}", exc_info=True)
            return []

    def _add_single_level_hierarchical_charts(
        self,
        recommendations: List[Dict],
        prioritized_categorical: List[str],
        unique_counts: Dict,
        numerical_columns: List[str]
    ) -> None:
        """
        Add single-level hierarchical charts when no multi-level hierarchies are found.

        Args:
            recommendations: The list of recommendations to append to
            prioritized_categorical: List of categorical columns prioritized by unique count
            unique_counts: Dictionary of unique counts for each column
            numerical_columns: List of numerical columns
        """
        # Get categorical columns with appropriate unique values for each hierarchical chart type
        treemap_candidates = []
        sunburst_candidates = []
        icicle_candidates = []

        for cat_col in prioritized_categorical:
            unique_count = unique_counts.get(cat_col, 0)

            # Treemap works well with many categories (3-100)
            if unique_count >= 3 and unique_count <= self.ADVANCED_THRESHOLDS["treemap"]:
                treemap_candidates.append((cat_col, unique_count))

            # Sunburst works well with moderate number of categories (3-100)
            if unique_count >= 3 and unique_count <= self.ADVANCED_THRESHOLDS["sunburst"]:
                sunburst_candidates.append((cat_col, unique_count))

            # Icicle works best with fewer categories (3-50)
            if unique_count >= 3 and unique_count <= self.ADVANCED_THRESHOLDS["icicle"]:
                icicle_candidates.append((cat_col, unique_count))

        # Sort candidates by optimal category count for each chart type
        # For treemap: more categories is better (sort descending)
        treemap_candidates.sort(key=lambda x: x[1], reverse=True)

        # For sunburst: moderate number is ideal (sort by distance from 20)
        sunburst_candidates.sort(key=lambda x: abs(x[1] - 20))

        # For icicle: fewer categories is better (sort ascending)
        icicle_candidates.sort(key=lambda x: x[1])

        # Extract just the column names
        treemap_cols = [col for col, _ in treemap_candidates]
        sunburst_cols = [col for col, _ in sunburst_candidates]
        icicle_cols = [col for col, _ in icicle_candidates]

        # Make sure we have valid columns for each chart type
        valid_cat_cols = list(set(treemap_cols + sunburst_cols + icicle_cols))

        if valid_cat_cols:
            value_col = numerical_columns[0] if numerical_columns else None

            # Select the best column for each chart type
            treemap_col = treemap_cols[0] if treemap_cols else valid_cat_cols[0]

            # For sunburst, try to use a different column than treemap
            if len(sunburst_cols) > 1 and sunburst_cols[0] == treemap_col:
                sunburst_col = sunburst_cols[1]
            else:
                sunburst_col = sunburst_cols[0] if sunburst_cols else valid_cat_cols[0]

            # For icicle, try to use a different column than both treemap and sunburst
            if icicle_cols:
                for col in icicle_cols:
                    if col != treemap_col and col != sunburst_col:
                        icicle_col = col
                        break
                else:
                    icicle_col = icicle_cols[0]
            else:
                icicle_col = valid_cat_cols[0]

            logger.info(f"Using different columns for single-level hierarchical charts - Treemap: {treemap_col} ({unique_counts.get(treemap_col, 0)} categories), " +
                       f"Sunburst: {sunburst_col} ({unique_counts.get(sunburst_col, 0)} categories), " +
                       f"Icicle: {icicle_col} ({unique_counts.get(icicle_col, 0)} categories)")

            # Treemap for single level
            treemap_fields = {
                "hierarchy": [treemap_col],
                "numeric": value_col if value_col else "count",
                "chart_title": f"Treemap of {treemap_col}"
            }

            # Add time_agg parameter if the column is a datetime column
            if treemap_col in datetime_columns:
                treemap_fields["time_agg"] = "monthly"  # Default to monthly for datetime columns

            recommendations.append({
                "chart_type": "treemap",
                "fields": treemap_fields
            })

            # Sunburst for single level
            sunburst_fields = {
                "hierarchy": [sunburst_col],
                "numeric": value_col if value_col else "count",
                "chart_title": f"Sunburst of {sunburst_col}"
            }

            # Add time_agg parameter if the column is a datetime column
            if sunburst_col in datetime_columns:
                sunburst_fields["time_agg"] = "monthly"  # Default to monthly for datetime columns

            recommendations.append({
                "chart_type": "sunburst",
                "fields": sunburst_fields
            })

            # Icicle for single level (if supported)
            icicle_fields = {
                "hierarchy": [icicle_col],
                "numeric": value_col if value_col else "count",
                "chart_title": f"Icicle of {icicle_col}"
            }

            # Add time_agg parameter if the column is a datetime column
            if icicle_col in datetime_columns:
                icicle_fields["time_agg"] = "monthly"  # Default to monthly for datetime columns

            recommendations.append({
                "chart_type": "icicle",
                "fields": icicle_fields
            })

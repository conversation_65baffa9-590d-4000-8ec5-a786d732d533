"""
Multi-chart recommendations module.

This module contains functions for recommending multi-type charts like multi-area, multi-line, and combo charts.
"""

from typing import List, Dict, Optional
from logging import getLogger
from app.core.logging_config import configure_logging
from app.services.chart_recommendations.base import ChartRecommendationBase

logger = configure_logging()

class MultiChartRecommender(ChartRecommendationBase):
    """Class for multi-chart recommendations."""
    
    def get_recommendations(self, metadata: Dict) -> List[Dict]:
        """
        Get multi-chart recommendations based on data characteristics.

        Args:
            metadata: The metadata dictionary containing information about the dataset

        Returns:
            List of multi-chart recommendations
        """
        recommendations = []
        used_combinations = set()

        try:
            # Safely get columns from metadata with defaults
            datetime_columns = metadata.get("datetime_columns", [])
            numerical_columns = metadata.get("numerical_columns", [])
            categorical_columns = metadata.get("categorical_columns", [])
            date_metadata = metadata.get("date_metadata", {})
            unique_counts = metadata.get("unique_counts", {})

            logger.debug(f"Multi-chart recommender: {len(datetime_columns)} datetime, {len(numerical_columns)} numerical, {len(categorical_columns)} categorical columns")

            # Multi-line charts for time series - generate ALL combinations
            if datetime_columns and len(numerical_columns) >= 2:
                for date_col in datetime_columns:
                    recommended_agg = self._get_recommended_time_aggregation(date_metadata)
                    date_format = self._format_datetime_columns(date_col, recommended_agg)

                    # Generate different combinations of numerical columns
                    # Create combinations of 2-4 numerical columns
                    for start_idx in range(len(numerical_columns)):
                        for end_idx in range(start_idx + 2, min(start_idx + 5, len(numerical_columns) + 1)):
                            y_fields = numerical_columns[start_idx:end_idx]
                            combination_key = ("multi_line", date_col, tuple(y_fields))

                            if combination_key not in used_combinations:
                                used_combinations.add(combination_key)
                                recommendations.append({
                                    "chart_type": "multi_line",
                                    "fields": {
                                        "x": date_col,
                                        "x_type": "datetime",
                                        "y_fields": y_fields,
                                        "time_agg": recommended_agg,
                                        "date_format": date_format,
                                        "chart_title": f"Multiple Metrics Over Time ({date_col})"
                                    }
                                })
                                logger.debug(f"Added multi_line: {date_col} with {len(y_fields)} numerical columns")

            # Multi-area charts for time series - generate ALL combinations
            if datetime_columns and len(numerical_columns) >= 2:
                for date_col in datetime_columns:
                    recommended_agg = self._get_recommended_time_aggregation(date_metadata)
                    date_format = self._format_datetime_columns(date_col, recommended_agg)

                    # Generate different combinations of numerical columns (2-3 for area charts to avoid clutter)
                    for start_idx in range(len(numerical_columns)):
                        for end_idx in range(start_idx + 2, min(start_idx + 4, len(numerical_columns) + 1)):
                            y_fields = numerical_columns[start_idx:end_idx]
                            combination_key = ("multi_area", date_col, tuple(y_fields))

                            if combination_key not in used_combinations:
                                used_combinations.add(combination_key)
                                recommendations.append({
                                    "chart_type": "multi_area",
                                    "fields": {
                                        "x": date_col,
                                        "x_type": "datetime",
                                        "y_fields": y_fields,
                                        "time_agg": recommended_agg,
                                        "date_format": date_format,
                                        "chart_title": f"Cumulative Trends Over Time ({date_col})"
                                    }
                                })
                                logger.debug(f"Added multi_area: {date_col} with {len(y_fields)} numerical columns")

            # Combo bar-line charts - generate ALL combinations
            if categorical_columns and len(numerical_columns) >= 2:
                # Filter categorical columns with reasonable cardinality
                valid_categorical = [col for col in categorical_columns
                                   if isinstance(unique_counts.get(col, 0), (int, float))
                                   and unique_counts.get(col, 0) >= 2
                                   and unique_counts.get(col, 0) <= 15]

                for cat_col in valid_categorical:
                    # Generate all combinations of 2 numerical columns
                    for i in range(len(numerical_columns)):
                        for j in range(i + 1, len(numerical_columns)):
                            y1_col = numerical_columns[i]
                            y2_col = numerical_columns[j]
                            combination_key = ("combo_bar_line", cat_col, y1_col, y2_col)

                            if combination_key not in used_combinations:
                                used_combinations.add(combination_key)
                                recommendations.append({
                                    "chart_type": "combo_bar_line",
                                    "fields": {
                                        "x": cat_col,
                                        "x_type": "categorical",
                                        "y1": y1_col,  # First numerical column for bars
                                        "y2": y2_col,  # Second numerical column for line
                                        "chart_title": f"{y1_col} and {y2_col} by {cat_col}"
                                    }
                                })
                                logger.debug(f"Added combo_bar_line: {cat_col} with {y1_col} and {y2_col}")

            logger.info(f"Generated {len(recommendations)} multi-chart recommendations from {len(used_combinations)} unique combinations")

            # Log breakdown by chart type for debugging
            chart_type_counts = {}
            for rec in recommendations:
                chart_type = rec.get('chart_type', 'unknown')
                chart_type_counts[chart_type] = chart_type_counts.get(chart_type, 0) + 1

            if chart_type_counts:
                logger.debug(f"Multi-chart breakdown: {chart_type_counts}")

            return recommendations

        except Exception as e:
            logger.error(f"Error in MultiChartRecommender.get_recommendations(): {str(e)}", exc_info=True)
            return []

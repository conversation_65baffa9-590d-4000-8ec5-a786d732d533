"""
Multi-chart recommendations module.

This module contains functions for recommending multi-type charts like multi-area, multi-line, and combo charts.
"""

from typing import List, Dict, Optional
from logging import getLogger
from app.core.logging_config import configure_logging
from app.services.chart_recommendations.base import ChartRecommendationBase

logger = configure_logging()

class MultiChartRecommender(ChartRecommendationBase):
    """Class for multi-chart recommendations."""
    
    def get_recommendations(self, metadata: Dict) -> List[Dict]:
        """
        Get multi-chart recommendations based on data characteristics.

        Args:
            metadata: The metadata dictionary containing information about the dataset

        Returns:
            List of multi-chart recommendations
        """
        recommendations = []

        try:
            # Safely get columns from metadata with defaults
            datetime_columns = metadata.get("datetime_columns", [])
            numerical_columns = metadata.get("numerical_columns", [])
            categorical_columns = metadata.get("categorical_columns", [])
            date_metadata = metadata.get("date_metadata", {})
            unique_counts = metadata.get("unique_counts", {})
        
            # Multi-line chart for time series
            if datetime_columns and len(numerical_columns) >= 2:
                date_col = datetime_columns[0]
                recommended_agg = self._get_recommended_time_aggregation(date_metadata)
                date_format = self._format_datetime_columns(date_col, recommended_agg)

                recommendations.append({
                    "chart_type": "multi_line",
                    "fields": {
                        "x": date_col,
                        "x_type": "datetime",
                        "y_fields": numerical_columns[:3],  # Use up to 3 numerical columns
                        "time_agg": recommended_agg,
                        "date_format": date_format,
                        "chart_title": f"Multiple Metrics Over Time"
                    }
                })

            # Multi-area chart for time series (cumulative values)
            if datetime_columns and len(numerical_columns) >= 2:
                date_col = datetime_columns[0]
                recommended_agg = self._get_recommended_time_aggregation(date_metadata)
                date_format = self._format_datetime_columns(date_col, recommended_agg)

                recommendations.append({
                    "chart_type": "multi_area",
                    "fields": {
                        "x": date_col,
                        "x_type": "datetime",
                        "y_fields": numerical_columns[:2],  # Use up to 2 numerical columns to avoid clutter
                        "time_agg": recommended_agg,
                        "date_format": date_format,
                        "chart_title": f"Cumulative Trends Over Time"
                    }
                })

            # Multi-type charts (combined bar and line)
            if categorical_columns and len(numerical_columns) >= 2:
                cat_col = categorical_columns[0]
                unique_count = unique_counts.get(cat_col, 0)
                if isinstance(unique_count, (int, float)) and unique_count >= 2 and unique_count <= 15:
                    recommendations.append({
                        "chart_type": "combo_bar_line",
                        "fields": {
                            "x": cat_col,
                            "x_type": "categorical",
                            "y1": numerical_columns[0],  # First numerical column for bars
                            "y2": numerical_columns[1],  # Second numerical column for line
                            "chart_title": f"{numerical_columns[0]} and {numerical_columns[1]} by {cat_col}"
                        }
                    })

            return recommendations

        except Exception as e:
            logger.error(f"Error in MultiChartRecommender.get_recommendations(): {str(e)}", exc_info=True)
            return []

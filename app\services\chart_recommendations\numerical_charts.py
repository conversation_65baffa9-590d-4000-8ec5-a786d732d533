"""
Numerical chart recommendations module.

This module contains functions for recommending numerical charts like histogram, scatter, and bubble.
"""

from typing import List, Dict, Optional
from logging import getLogger
from app.core.logging_config import configure_logging
from app.services.chart_recommendations.base import ChartRecommendationBase

logger = configure_logging()

class NumericalChartRecommender(ChartRecommendationBase):
    """Class for numerical chart recommendations."""
    
    def get_recommendations(self, metadata: Dict) -> List[Dict]:
        """
        Get numerical chart recommendations based on data characteristics.

        Args:
            metadata: The metadata dictionary containing information about the dataset

        Returns:
            List of numerical chart recommendations
        """
        recommendations = []

        try:
            # Safely get columns from metadata with defaults
            numerical_columns = metadata.get("numerical_columns", [])
            categorical_columns = metadata.get("categorical_columns", [])
            unique_counts = metadata.get("unique_counts", {})
        
            # Histogram recommendations
            for num_col in numerical_columns:
                # Always recommend histogram for numerical columns
                # The actual validation will happen in the DataAggregator._prepare_histogram_data method
                recommendations.append({
                    "chart_type": "histogram",
                    "fields": {
                        "x": num_col,
                        "x_type": "numerical",
                        "numeric": "count",
                        "chart_title": f"Distribution of {num_col}",
                        "bin": True,  # Add binning for numerical distribution
                        "bin_size": 10
                    }
                })

            # Scatter plot recommendations
            if len(numerical_columns) >= 2:
                recommendations.append({
                    "chart_type": "scatter",
                    "fields": {
                        "x": numerical_columns[0],
                        "x_type": "numerical",
                        "y": numerical_columns[1],
                        "y_type": "numerical",
                        "chart_title": f"Correlation: {numerical_columns[0]} vs {numerical_columns[1]}"
                    }
                })

                # Bubble chart recommendations (if we have at least 3 numerical columns)
                if len(numerical_columns) >= 3:
                    # First bubble chart: basic bubble chart
                    recommendations.append({
                        "chart_type": "bubble",
                        "fields": {
                            "x": numerical_columns[0],
                            "x_type": "numerical",
                            "y": numerical_columns[1],
                            "y_type": "numerical",
                            "size": numerical_columns[2],  # Third numerical column for bubble size
                            "chart_title": f"Bubble Chart: {numerical_columns[0]} vs {numerical_columns[1]} (Size: {numerical_columns[2]})"
                        }
                    })

                    # Second bubble chart: with categorical coloring (if available and different combination)
                    if categorical_columns and len(numerical_columns) >= 4:
                        # Find a categorical column with a reasonable number of categories for coloring
                        color_candidates = []
                        for cat_col in categorical_columns:
                            unique_count = unique_counts.get(cat_col, 0)
                            # Ideal for coloring: between 2 and 10 categories
                            if 2 <= unique_count <= 10:
                                color_candidates.append(cat_col)

                        if color_candidates:
                            color_col = color_candidates[0]  # Use the first suitable categorical column
                            # Use different numerical columns for the second bubble chart
                            recommendations.append({
                                "chart_type": "bubble",
                                "fields": {
                                    "x": numerical_columns[1],  # Different x column
                                    "x_type": "numerical",
                                    "y": numerical_columns[2],  # Different y column
                                    "y_type": "numerical",
                                    "size": numerical_columns[3],  # Different size column
                                    "color": color_col,  # Categorical column for coloring
                                    "color_type": "categorical",
                                    "chart_title": f"Bubble Chart by {color_col}: {numerical_columns[1]} vs {numerical_columns[2]} (Size: {numerical_columns[3]})"
                                }
                            })
                    elif categorical_columns and len(numerical_columns) == 3:
                        # If we only have 3 numerical columns, use categorical coloring with same numerical columns
                        color_candidates = []
                        for cat_col in categorical_columns:
                            unique_count = unique_counts.get(cat_col, 0)
                            if 2 <= unique_count <= 10:
                                color_candidates.append(cat_col)

                        if color_candidates:
                            color_col = color_candidates[0]
                            recommendations.append({
                                "chart_type": "bubble",
                                "fields": {
                                    "x": numerical_columns[0],
                                    "x_type": "numerical",
                                    "y": numerical_columns[1],
                                    "y_type": "numerical",
                                    "size": numerical_columns[2],
                                    "color": color_col,  # Add categorical coloring
                                    "color_type": "categorical",
                                    "chart_title": f"Bubble Chart by {color_col}: {numerical_columns[0]} vs {numerical_columns[1]} (Size: {numerical_columns[2]})"
                                }
                            })

            # Correlation matrix for multiple numerical columns
            if len(numerical_columns) > 2:
                recommendations.append({
                    "chart_type": "scatter_matrix",
                    "fields": {
                        "columns": numerical_columns[:4]  # Limit to prevent visual overload
                    },
                    "options": {
                        "diagonal": "histogram"
                    }
                })

            return recommendations

        except Exception as e:
            logger.error(f"Error in NumericalChartRecommender.get_recommendations(): {str(e)}", exc_info=True)
            return []

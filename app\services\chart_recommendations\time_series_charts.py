"""
Time series chart recommendations module.

This module contains functions for recommending time series charts.
"""

from typing import List, Dict, Optional
import pandas as pd
from logging import getLogger
from app.core.logging_config import configure_logging
from app.services.chart_recommendations.base import ChartRecommendationBase

logger = configure_logging()

class TimeSeriesChartRecommender(ChartRecommendationBase):
    """Class for time series chart recommendations."""

    def get_recommendations(self, metadata: Dict) -> List[Dict]:
        """
        Get time series chart recommendations based on data characteristics.
        All charts are generated in this single function with unique combinations.

        Args:
            metadata: The metadata dictionary containing information about the dataset

        Returns:
            List of time series chart recommendations
        """
        recommendations = []
        used_combinations = set()  # Track unique field combinations

        # Safely get columns from metadata with defaults
        datetime_columns = metadata.get("datetime_columns", [])
        numerical_columns = metadata.get("numerical_columns", [])
        categorical_columns = metadata.get("categorical_columns", [])
        date_metadata = metadata.get("date_metadata", {})
        unique_counts = metadata.get("unique_counts", {})

        # Early exit if no datetime columns
        if not datetime_columns:
            logger.info("No datetime columns found, skipping time series recommendations")
            return recommendations

        # Get recommended time aggregation based on date metadata
        recommended_agg = self._get_recommended_time_aggregation(date_metadata)

        # Process each datetime column
        for date_col in datetime_columns:
            # Get date metadata for this column
            col_metadata = date_metadata.get(date_col, {})
            unique_dates = col_metadata.get('unique_dates', 0)

            logger.info(f"Processing datetime column: {date_col} with {unique_dates} unique dates")

            # Skip if only one unique date (no time series possible)
            if unique_dates < 2:
                logger.info(f"Skipping {date_col}: insufficient unique dates ({unique_dates})")
                continue

            # 1. Generate basic time series charts (line, area) with numerical columns
            if numerical_columns:
                time_series_charts = self._generate_time_series_charts(
                    date_col, numerical_columns, recommended_agg, used_combinations
                )
                recommendations.extend(time_series_charts)

            # 2. Generate calendar heatmap (only if reasonable number of dates)
            if 2 <= unique_dates <= 100:
                calendar_chart = self._generate_calendar_heatmap(date_col, used_combinations)
                if calendar_chart:
                    recommendations.append(calendar_chart)

            # 3. Generate timeline charts with categorical grouping
            if categorical_columns:
                timeline_charts = self._generate_timeline_charts(
                    date_col, categorical_columns,
                    recommended_agg, unique_counts, used_combinations
                )
                recommendations.extend(timeline_charts)

            # 4. Generate time-based heatmaps (datetime x categorical)
            if categorical_columns and numerical_columns:
                heatmap_charts = self._generate_time_heatmaps(
                    date_col, categorical_columns, numerical_columns,
                    recommended_agg, unique_counts, used_combinations
                )
                recommendations.extend(heatmap_charts)

        logger.info(f"Generated {len(recommendations)} time series chart recommendations")
        return recommendations

    def _generate_time_series_charts(
        self, date_col: str, numerical_columns: List[str], time_agg: str, used_combinations: set
    ) -> List[Dict]:
        """Generate basic time series charts (line, area) with unique combinations."""
        recommendations = []
        chart_types = ["line", "area"]

        # Get user-friendly date format
        date_format = self._format_datetime_columns(date_col, time_agg)

        for i, num_col in enumerate(numerical_columns[:2]):  # Limit to first 2 numerical columns
            chart_type = chart_types[i % len(chart_types)]

            # Create unique combination key
            combination_key = (chart_type, date_col, num_col)

            if combination_key not in used_combinations:
                used_combinations.add(combination_key)

                if chart_type == "line":
                    recommendations.append({
                        "chart_type": "line",
                        "fields": {
                            "x": date_col,
                            "x_type": "datetime",
                            "numeric": num_col,
                            "agg": "mean",
                            "time_agg": time_agg,
                            "date_format": date_format,
                            "chart_title": f"{num_col.replace('_', ' ').title()} Over Time",
                            "x_axis_title": "Date",
                            "y_axis_title": num_col.replace('_', ' ').title()
                        },
                        "options": {
                            "time_grouping": {
                                "interval": time_agg,
                                "format": date_format
                            }
                        }
                    })
                else:  # area chart
                    recommendations.append({
                        "chart_type": "area",
                        "fields": {
                            "x": date_col,
                            "x_type": "datetime",
                            "numeric": num_col,
                            "agg": "sum",
                            "time_agg": time_agg,
                            "date_format": date_format,
                            "chart_title": f"Cumulative {num_col.replace('_', ' ').title()}",
                            "x_axis_title": "Date",
                            "y_axis_title": f"Total {num_col.replace('_', ' ').title()}"
                        },
                        "options": {
                            "time_grouping": {
                                "interval": time_agg,
                                "format": date_format
                            }
                        }
                    })

        return recommendations

    def _generate_calendar_heatmap(self, date_col: str, used_combinations: set) -> Optional[Dict]:
        """Generate calendar heatmap chart."""
        combination_key = ("calendar_heatmap", date_col, "count")

        if combination_key not in used_combinations:
            used_combinations.add(combination_key)
            return {
                "chart_type": "calendar_heatmap",
                "fields": {
                    "x": date_col,
                    "x_type": "datetime",
                    "numeric": "count",
                    "time_agg": "daily",  # Calendar heatmap works best with daily data
                    "chart_title": f"Calendar View of {date_col}"
                }
            }
        return None

    def _generate_timeline_charts(
        self, date_col: str, categorical_columns: List[str],
        time_agg: str, unique_counts: Dict, used_combinations: set
    ) -> List[Dict]:
        """Generate timeline charts with categorical grouping."""
        recommendations = []

        # Filter categorical columns to exclude single-value columns
        valid_categorical = [
            col for col in categorical_columns
            if unique_counts.get(col, 0) >= 2
        ]

        if not valid_categorical:
            return recommendations

        # Simple timeline without grouping
        combination_key = ("timeline", date_col, None)
        if combination_key not in used_combinations:
            used_combinations.add(combination_key)
            recommendations.append({
                "chart_type": "timeline",
                "fields": {
                    "x": date_col,
                    "x_type": "datetime",
                    "time_agg": time_agg,
                    "chart_title": f"Timeline of Events by {date_col}"
                }
            })

        # Timeline with categorical grouping (use first valid categorical column)
        if valid_categorical:
            group_col = valid_categorical[0]
            combination_key = ("timeline", date_col, group_col)
            if combination_key not in used_combinations:
                used_combinations.add(combination_key)
                recommendations.append({
                    "chart_type": "timeline",
                    "fields": {
                        "x": date_col,
                        "x_type": "datetime",
                        "group": group_col,
                        "time_agg": time_agg,
                        "chart_title": f"Timeline of Events by {date_col} and {group_col}"
                    }
                })

        return recommendations

    def _generate_time_heatmaps(
        self, date_col: str, categorical_columns: List[str], numerical_columns: List[str],
        time_agg: str, unique_counts: Dict, used_combinations: set
    ) -> List[Dict]:
        """Generate time-based heatmaps (datetime x categorical)."""
        recommendations = []

        # Filter categorical columns to exclude single-value columns
        valid_categorical = [
            col for col in categorical_columns
            if unique_counts.get(col, 0) >= 2
        ]

        if not valid_categorical or not numerical_columns:
            return recommendations

        # Use first valid categorical and numerical column
        cat_col = valid_categorical[0]
        num_col = numerical_columns[0]

        # Check combined cardinality to avoid overly complex heatmaps
        date_unique_count = unique_counts.get(date_col, 0)
        cat_unique_count = unique_counts.get(cat_col, 0)
        combined_cardinality = date_unique_count * cat_unique_count

        # Only create heatmap if cardinality is reasonable
        if combined_cardinality <= 400 and combined_cardinality >= 4:
            combination_key = ("heatmap", date_col, cat_col, num_col)
            if combination_key not in used_combinations:
                used_combinations.add(combination_key)
                recommendations.append({
                    "chart_type": "heatmap",
                    "fields": {
                        "x": date_col,
                        "x_type": "datetime",
                        "y": cat_col,
                        "y_type": "categorical",
                        "numeric": num_col,
                        "time_agg": time_agg,
                        "chart_title": f"{num_col} by {date_col} and {cat_col}"
                    }
                })

        return recommendations

    def _analyze_time_series(
        self, date_col: str, numerical_columns: List[str], date_metadata: Dict
    ) -> List[Dict]:
        """
        Analyze time-based patterns and relationships.
        This method is kept for backward compatibility but functionality is moved to get_recommendations.
        """
        # Get date range from date_metadata
        try:
            # Check if we have the full date_metadata dictionary
            if isinstance(date_metadata, dict) and 'transaction_date' in date_metadata:
                meta = date_metadata['transaction_date']
                start_date = pd.to_datetime(meta['min_date'])
                end_date = pd.to_datetime(meta['max_date'])
                date_range_days = (end_date - start_date).days
                logger.info(f"Calculated date range: {date_range_days} days")

                # Set appropriate time aggregation with user-friendly formats
                if date_range_days > 730:  # More than 2 years
                    time_agg = 'yearly'
                    date_format = '%Y'
                elif date_range_days > 90:  # More than 3 months
                    time_agg = 'quarterly'
                    date_format = 'Q%q %Y'  # "Q1 2024" - frontend will need to handle this format
                elif date_range_days > 30:  # More than 30 days
                    time_agg = 'monthly'
                    date_format = '%b %Y'  # "Jan 2024"
                else:
                    time_agg = 'daily'
                    date_format = '%d %b %Y'  # "01 Jan 2024"
            else:
                # Use the recommended_agg if provided
                time_agg = date_metadata.get('recommended_agg', 'monthly')
                date_format = self._format_datetime_columns(date_col, time_agg)

            # Use the new method to generate charts
            used_combinations = set()
            return self._generate_time_series_charts(date_col, numerical_columns, time_agg, used_combinations)

        except Exception as e:
            logger.error(f"Error in time series analysis: {e}")
            # Default to monthly aggregation as fallback with user-friendly format
            used_combinations = set()
            return self._generate_time_series_charts(date_col, numerical_columns, 'monthly', used_combinations)

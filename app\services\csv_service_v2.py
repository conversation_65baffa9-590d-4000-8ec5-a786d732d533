import pandas as pd
from fastapi import UploadFile
from app.core.exceptions import DataProcessingError
from app.core.logging_config import configure_logging
from app.services.data_processor import DataProcessor
from app.services.chart_recommender import ChartData as ChartRecommender
from app.services.chart_data import ChartData
from app.services.data_loader import DataLoader
from app.services.data_validator import DataValidator

logger = configure_logging('app.services.csv_service_v2')

class CsvServiceV2:
    """
    Enhanced CSV Service for data processing and analysis pipeline.
    Follows data science principles for loading, cleaning, analysis, and visualization.
    """

    def __init__(self):
        self.data_processor = DataProcessor()
        self.chart_recommender = ChartRecommender()
        self.chart_data_service = ChartData()
        self.data_loader = DataLoader()
        self.data_validator = DataValidator()


    async def handle_csv_upload(self, file: UploadFile) -> dict:
        """
        Main entry point for CSV processing pipeline.
        Orchestrates the entire data processing flow.
        """
        try:
            # Step 1: Data Loading - Using the DataLoader service
            df = await self.data_loader.load_csv_data(file)
            logger.info("Data loaded successfully")

            # Step 2: Data Exploration and Cleaning - Using the DataValidator service
            df_cleaned, _ = self.data_validator.clean_and_validate_data(df)
            logger.info("Data validated and cleaned")

            # Step 3: Data Processing - Using the DataProcessor service
            processed_data = self.data_processor.process_data(df_cleaned)

            if processed_data is None or 'metadata' not in processed_data:
                logger.error("Failed to process CSV data")
                raise DataProcessingError("Failed to process CSV data")

            logger.debug( processed_data['metadata'])

            # Step 4: Generate Chart Recommendations
            recommended_charts = self.chart_recommender.get_rule_based_recommendations(processed_data['metadata'])
            logger.debug( recommended_charts)

            # Step 5: Process chart recommendations
            chart_data = self.chart_data_service.process_chart_recommendations(
                df=processed_data['df'],
                recommended_charts=recommended_charts
            )

            result = {
                "charts": chart_data
            }

            logger.info(f"Successfully processed CSV file with {len(df)} rows")
            return result

        except Exception as e:
            logger.error(f"Error in CSV processing pipeline: {str(e)}")
            raise DataProcessingError(str(e))

    # All helper methods have been moved to dedicated services:
    # - Data loading functionality moved to the DataLoader service
    # - Data cleaning and validation moved to the DataValidator service
    # - Chart recommendation moved to the ChartRecommender service
    # - Chart data processing moved to the ChartData service
    # Note: Analytics and correlation analysis removed for improved performance
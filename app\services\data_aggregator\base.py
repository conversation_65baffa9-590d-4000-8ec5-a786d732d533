"""
Base module for data aggregation.

This module contains the base class for data aggregation and common utilities.
"""

import pandas as pd
import numpy as np
import json
from app.core.logging_config import configure_logging
from app.core.utils import sanitize_json_data

# Configure logger for this module
logger = configure_logging('app.services.data_aggregator.base')

class ChartAggregatorBase:
    """Base class for chart data aggregation."""

    def __init__(self):
        """Initialize the chart aggregator."""
        pass

    def _ensure_serializable(self, data):
        """Ensure data is JSON serializable by handling special float values and other non-serializable types."""
        # First round numeric values to 2 decimal places
        rounded_data = self._round_numeric_values(data)
        # Then ensure the data is serializable
        return sanitize_json_data(rounded_data)

    def _format_datetime_columns(self, df: pd.DataFrame, datetime_col: str, time_agg: str,
                              sort_key_col: str = 'sort_key', display_col: str = None) -> pd.DataFrame:
        """
        Format datetime columns with user-friendly formats and add sort keys for chronological ordering.

        Args:
            df: DataFrame containing the datetime column
            datetime_col: Name of the datetime column to format
            time_agg: Time aggregation level ('monthly', 'quarterly', 'yearly', 'daily', 'hourly')
            sort_key_col: Name of the column to store the sort key (default: 'sort_key')
            display_col: Name of the column to store the display value. If None, overwrites the original column.
                         If same as datetime_col, overwrites the original column.

        Returns:
            DataFrame with added sort key and formatted display columns
        """
        # Make a copy to avoid modifying the original DataFrame
        result_df = df.copy()

        # If display_col is None, use the original column name
        if display_col is None:
            display_col = datetime_col

        # Initialize sort key column with a default format (YYYY-MM-DD)
        result_df[sort_key_col] = result_df[datetime_col].dt.strftime('%Y-%m-%d')

        # Apply time aggregation with user-friendly formats
        if time_agg == "monthly":
            # Use format like "Jan 2024" instead of "2024-01"
            result_df[sort_key_col] = result_df[datetime_col].dt.strftime('%Y-%m')  # YYYY-MM for sorting
            result_df[display_col] = result_df[datetime_col].dt.strftime('%b %Y')  # MMM YYYY for display
        elif time_agg == "quarterly":
            # Use standard quarter format (Q1, Q2, Q3, Q4)
            try:
                result_df[sort_key_col] = result_df[datetime_col].dt.strftime('%Y') + '-Q' + result_df[datetime_col].dt.quarter.astype(str)  # YYYY-Q# for sorting
                # Create a new column with Q1, Q2, Q3, Q4 format
                result_df[display_col] = 'Q' + result_df[datetime_col].dt.quarter.astype(str) + ' ' + result_df[datetime_col].dt.year.astype(str)  # "Q1 2023", "Q2 2023", etc.
            except (ValueError, TypeError) as e:
                # Fallback for problematic datetime values
                logger.warning(f"Error in quarterly datetime formatting: {str(e)}")
                result_df[sort_key_col] = result_df[datetime_col].astype(str)
                result_df[display_col] = result_df[datetime_col].astype(str)
        elif time_agg == "yearly":
            result_df[sort_key_col] = result_df[datetime_col].dt.strftime('%Y')  # YYYY for sorting
            result_df[display_col] = result_df[datetime_col].dt.strftime('%Y')  # YYYY for display
        elif time_agg == "hourly":
            result_df[sort_key_col] = result_df[datetime_col].dt.strftime('%Y-%m-%d %H')  # YYYY-MM-DD HH for sorting
            result_df[display_col] = result_df[datetime_col].dt.strftime('%d %b %Y %H:00')  # DD MMM YYYY HH:00 for display
        else:  # daily is default
            result_df[sort_key_col] = result_df[datetime_col].dt.strftime('%Y-%m-%d')  # YYYY-MM-DD for sorting
            result_df[display_col] = result_df[datetime_col].dt.strftime('%d %b %Y')  # DD MMM YYYY for display

        return result_df

    def _apply_time_aggregation(self, series: pd.Series, time_agg: str) -> tuple:
        """
        Apply time aggregation to a datetime series with user-friendly formats
        Returns a tuple of (sort_key, display_value) to maintain chronological order

        Note: This method is kept for backward compatibility. New code should use _format_datetime_columns.
        """
        if not time_agg:
            return (series.dt.strftime('%Y-%m-%d'), series.dt.strftime('%d %b %Y'))  # (YYYY-MM-DD, DD MMM YYYY)

        if time_agg == "monthly":
            return (series.dt.strftime('%Y-%m'), series.dt.strftime('%b %Y'))  # (YYYY-MM, MMM YYYY)
        elif time_agg == "quarterly":
            # Handle potential NaN values in datetime series
            try:
                sort_key = series.dt.strftime('%Y') + '-Q' + series.dt.quarter.astype(str)
                # Use standard quarter format (Q1, Q2, Q3, Q4)
                display = 'Q' + series.dt.quarter.astype(str) + ' ' + series.dt.year.astype(str)
                return (sort_key, display)  # (YYYY-Q#, "Q1 2023", "Q2 2023", etc.)
            except (ValueError, TypeError) as e:
                # Fallback for problematic datetime values
                logger.warning(f"Error in quarterly aggregation: {str(e)}")
                return (series.astype(str), series.astype(str))
        elif time_agg == "yearly":
            return (series.dt.strftime('%Y'), series.dt.strftime('%Y'))  # (YYYY, YYYY)
        elif time_agg == "hourly":
            return (series.dt.strftime('%Y-%m-%d %H'), series.dt.strftime('%d %b %Y %H:00'))  # (YYYY-MM-DD HH, DD MMM YYYY HH:00)
        else:  # daily is default
            return (series.dt.strftime('%Y-%m-%d'), series.dt.strftime('%d %b %Y'))  # (YYYY-MM-DD, DD MMM YYYY)

    def _round_numeric_values(self, data):
        """Round numeric values in the data structure and handle special float values."""
        if isinstance(data, (list, np.ndarray)):
            return [self._round_numeric_values(x) for x in data]
        elif isinstance(data, dict):
            return {k: self._round_numeric_values(v) for k, v in data.items()}
        elif isinstance(data, (float, np.float64, np.float32)):
            # Handle special float values (inf, -inf, NaN) that are not JSON serializable
            if np.isnan(data) or np.isinf(data):
                return 0  # Replace with 0 or another appropriate value
            # Round to 2 decimal places if it contains decimals
            return round(data, 2) if data != round(data) else data
        return data

# Import chart-specific modules - after ChartAggregatorBase is defined to avoid circular imports
from app.services.data_aggregator.basic_charts import BasicChartAggregator
from app.services.data_aggregator.time_series_charts import TimeSeriesChartAggregator
from app.services.data_aggregator.hierarchical_charts import HierarchicalChartAggregator
from app.services.data_aggregator.distribution_charts import DistributionChartAggregator
from app.services.data_aggregator.multi_charts import MultiChartAggregator
from app.services.data_aggregator.stacked_charts import StackedChartAggregator
from app.services.data_aggregator.heatmap_charts import HeatmapChartAggregator
from app.services.data_aggregator.scatter_charts import ScatterChartAggregator
from app.services.data_aggregator.funnel_charts import FunnelChartAggregator
from app.services.data_aggregator.multi_group_charts import MultiGroupChartAggregator
from app.services.data_aggregator.timeline_charts import TimelineChartAggregator

class DataAggregator:
    """Main data aggregator class that orchestrates chart data preparation."""

    def __init__(self):
        """Initialize the data aggregator with chart-specific aggregators."""
        self.basic_chart_aggregator = BasicChartAggregator()
        self.time_series_chart_aggregator = TimeSeriesChartAggregator()
        self.hierarchical_chart_aggregator = HierarchicalChartAggregator()
        self.distribution_chart_aggregator = DistributionChartAggregator()
        self.multi_chart_aggregator = MultiChartAggregator()
        self.stacked_chart_aggregator = StackedChartAggregator()
        self.heatmap_chart_aggregator = HeatmapChartAggregator()
        self.scatter_chart_aggregator = ScatterChartAggregator()
        self.funnel_chart_aggregator = FunnelChartAggregator()
        self.multi_group_chart_aggregator = MultiGroupChartAggregator()
        self.timeline_chart_aggregator = TimelineChartAggregator()

    def _ensure_serializable(self, data):
        """Ensure data is JSON serializable by handling special float values and other non-serializable types."""
        # First apply rounding to numeric values using the ChartAggregatorBase method
        rounded_data = self.basic_chart_aggregator._round_numeric_values(data)
        # Then ensure the data is serializable
        return sanitize_json_data(rounded_data)

    def prepare_chart_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """Main method to prepare chart data based on chart type."""
        try:
            # Validate inputs
            if not isinstance(df, pd.DataFrame) or df.empty:
                logger.warning("Empty or invalid DataFrame provided")
                return None

            if not fields:
                logger.warning("No fields provided")
                return None

            # Ensure fields dict is serializable
            fields = sanitize_json_data(fields)

            # Route to appropriate chart aggregator based on chart type
            if chart_type in ["pie", "doughnut", "semi_circle", "bar", "horizontal_bar", "column", "polar_area"]:
                return self.basic_chart_aggregator.prepare_chart_data(df, chart_type, fields)
            elif chart_type in ["line", "area"]:
                return self.time_series_chart_aggregator.prepare_chart_data(df, chart_type, fields)
            elif chart_type in ["treemap", "sunburst", "icicle"]:
                return self.hierarchical_chart_aggregator.prepare_chart_data(df, chart_type, fields)
            elif chart_type in ["histogram", "box", "violin"]:
                return self.distribution_chart_aggregator.prepare_chart_data(df, chart_type, fields)
            elif chart_type in ["multi_line", "multi_area", "combo_bar_line"]:
                return self.multi_chart_aggregator.prepare_chart_data(df, chart_type, fields)
            elif chart_type in ["stacked_bar", "stacked_line", "stacked_area"]:
                return self.stacked_chart_aggregator.prepare_chart_data(df, chart_type, fields)
            elif chart_type in ["heatmap", "calendar_heatmap"]:
                return self.heatmap_chart_aggregator.prepare_chart_data(df, chart_type, fields)
            elif chart_type in ["scatter", "bubble", "scatter_matrix"]:
                return self.scatter_chart_aggregator.prepare_chart_data(df, chart_type, fields)
            elif chart_type in ["funnel"]:
                return self.funnel_chart_aggregator.prepare_chart_data(df, chart_type, fields)
            elif chart_type in ["grouped_bar"]:
                return self.multi_group_chart_aggregator.prepare_chart_data(df, chart_type, fields)
            elif chart_type in ["timeline"]:
                return self.timeline_chart_aggregator.prepare_chart_data(df, chart_type, fields)
            else:
                logger.warning(f"Unsupported chart type: {chart_type}")
                return None

        except Exception as e:
            logger.error(f"Error preparing {chart_type} data: {str(e)}")
            return None

"""
Basic charts data aggregation module.
"""

import pandas as pd
import numpy as np
from app.core.logging_config import configure_logging
from app.services.data_aggregator.base import ChartAggregatorBase

logger = configure_logging('app.services.data_aggregator.basic_charts')

class BasicChartAggregator(ChartAggregatorBase):
    """Class for basic chart data aggregation."""
    
    def prepare_chart_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """
        Prepare data for basic charts (pie, doughnut, semi_circle, bar, horizontal_bar, column, polar_area).

        Args:
            df: The pandas DataFrame containing the data
            chart_type: The type of chart to prepare data for
            fields: Dictionary of fields to use for the chart

        Returns:
            Dictionary containing the chart data
        """
        if chart_type in ["pie", "doughnut", "semi_circle"]:
            return self._prepare_pie_chart_data(df, chart_type, fields)
        elif chart_type in ["bar", "horizontal_bar", "column"]:
            return self._prepare_bar_chart_data(df, chart_type, fields)
        elif chart_type == "polar_area":
            return self._prepare_polar_area_chart_data(df, fields)
        else:
            logger.warning(f"Unsupported basic chart type: {chart_type}")
            return None
    
    def _prepare_pie_chart_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """Prepares data for pie, doughnut, and semi-circle charts."""
        try:
            x_field = fields.get("x")
            x_type = fields.get("x_type")
            numeric_field = fields.get("numeric")
            
            if not x_field or not x_type:
                logger.warning(f"Skipping {chart_type}: Missing x or x_type")
                return None
            
            if x_type == "categorical":
                grouped = df.groupby(x_field).size().reset_index(name='count')
                if numeric_field and numeric_field != "count":
                    grouped = df.groupby(x_field)[numeric_field].agg('sum').reset_index()

                # Check if all values are the same - skip if they are
                if len(grouped) > 1:  # Make sure there's at least 2 categories
                    value_col = numeric_field if numeric_field and numeric_field != "count" else "count"
                    unique_values = grouped[value_col].nunique()
                    if unique_values == 1:
                        logger.info(f"Skipping {chart_type}: All categories have the same value ({grouped[value_col].iloc[0]})")
                        return None

                chart_title = fields.get("chart_title", f"{x_field.replace('_', ' ').title()} Distribution")
                if chart_type == "pie":
                    chart_title = chart_title.replace("Pie Chart", "").replace("pie chart", "").replace("Pie", "").replace("pie", "").replace(":","").strip()
                elif chart_type == "doughnut":
                    chart_title = chart_title.replace("Doughnut Chart", "").replace("doughnut chart", "").replace("Doughnut", "").replace("doughnut", "").replace(":","").strip()
                chart_data = {
                    "chart_type": chart_type,
                    "library": "plotly",
                    "data": {
                        "values": grouped[numeric_field if numeric_field and numeric_field != "count" else "count"].tolist(),
                        "labels": grouped[x_field].tolist(),
                        "type": "pie",
                        "hole": 0.4 if chart_type == "doughnut" else 0
                    },
                    "layout": {
                        "title": chart_title,
                        "showlegend": True
                    }
                }
                return self._ensure_serializable(chart_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Error preparing {chart_type} data: {str(e)}")
            return None
    
    def _prepare_bar_chart_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """Prepares data for bar charts."""
        try:
            x_field = fields.get("x")
            x_type = fields.get("x_type")
            numeric_field = fields.get("numeric")
            
            if not x_field or not x_type:
                logger.warning("Skipping bar chart: Missing x or x_type")
                return None
            
            if x_type == "categorical":
                grouped = df.groupby(x_field).size().reset_index(name='count')
                if numeric_field and numeric_field != "count":
                    grouped = df.groupby(x_field)[numeric_field].agg('sum').reset_index()

                # Check if all values are the same - skip if they are
                if len(grouped) > 1:  # Make sure there's at least 2 categories
                    value_col = numeric_field if numeric_field and numeric_field != "count" else "count"
                    unique_values = grouped[value_col].nunique()
                    if unique_values == 1:
                        logger.info(f"Skipping bar chart: All categories have the same value ({grouped[value_col].iloc[0]})")
                        return None

            elif x_type == "numerical" and fields.get('bin'):
                # Handle binned numerical data
                bins = min(fields.get('bin_size', 10), 50)
                # Create bins and convert intervals to strings
                df['bins'] = pd.cut(df[x_field], bins=bins)
                # Convert intervals to readable strings and get value counts
                grouped = df['bins'].value_counts().reset_index()
                grouped.columns = [x_field, 'count']
                # Convert intervals to strings in format "start-end"
                grouped[x_field] = grouped[x_field].apply(lambda x: f"{x.left:.2f}-{x.right:.2f}")
                # Sort by the numeric start of each bin
                grouped = grouped.sort_values(by=x_field, key=lambda x: pd.to_numeric(x.str.split('-').str[0]))

                # Check if all bins have the same count
                if len(grouped) > 1 and grouped['count'].nunique() == 1:
                    logger.info(f"Skipping bar chart: All bins have the same count ({grouped['count'].iloc[0]})")
                    return None

            # Determine chart orientation and type
            plotly_type = "bar"
            orientation = None

            if chart_type == "horizontal_bar":
                # For horizontal bar, swap x and y axes
                x_data = grouped[numeric_field if numeric_field and numeric_field != "count" else "count"].tolist()
                y_data = grouped[x_field].tolist()
                orientation = "h"
                x_axis_title = fields.get("y_axis_title", numeric_field if numeric_field else "Count")
                y_axis_title = fields.get("x_axis_title", x_field.replace('_', ' ').title())
            else:
                # For regular bar and column (both are vertical in Plotly)
                x_data = grouped[x_field].tolist()
                y_data = grouped[numeric_field if numeric_field and numeric_field != "count" else "count"].tolist()
                x_axis_title = fields.get("x_axis_title", x_field.replace('_', ' ').title())
                y_axis_title = fields.get("y_axis_title", numeric_field if numeric_field else "Count")

            chart_data = {
                "chart_type": chart_type,
                "library": "plotly",
                "data": {
                    "x": x_data,
                    "y": y_data,
                    "type": plotly_type,
                    **({"orientation": orientation} if orientation else {})
                },
                "layout": {
                    "title": fields.get("chart_title", f"{x_field.replace('_', ' ').title()} Distribution"),
                    "xaxis": {"title": x_axis_title},
                    "yaxis": {"title": y_axis_title}
                }
            }
            return self._ensure_serializable(chart_data)

        except Exception as e:
            logger.error(f"Error preparing bar chart data: {str(e)}")
            return None

    def _prepare_polar_area_chart_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for polar area charts."""
        try:
            x_field = fields.get("x")
            x_type = fields.get("x_type")
            numeric_field = fields.get("numeric")

            if not x_field or not x_type:
                logger.warning("Skipping polar area chart: Missing x or x_type")
                return None

            # Group data by x_field
            if numeric_field and numeric_field != "count":
                grouped = df.groupby(x_field)[numeric_field].sum().reset_index()
            else:
                grouped = df.groupby(x_field).size().reset_index(name='count')
                numeric_field = "count"

            # Limit to reasonable number of categories for polar area
            if len(grouped) > 12:
                grouped = grouped.nlargest(12, numeric_field)
                logger.info(f"Limited polar area chart to top 12 categories")

            chart_title = fields.get("chart_title", f"{x_field.replace('_', ' ').title()} Distribution")
            chart_data = {
                "chart_type": "polar_area",
                "library": "plotly",
                "data": {
                    "r": grouped[numeric_field].tolist(),
                    "theta": grouped[x_field].tolist(),
                    "type": "barpolar"
                },
                "layout": {
                    "title": chart_title,
                    "polar": {
                        "radialaxis": {
                            "visible": True,
                            "title": numeric_field.replace('_', ' ').title() if numeric_field != "count" else "Count"
                        },
                        "angularaxis": {
                            "title": x_field.replace('_', ' ').title()
                        }
                    }
                }
            }
            return self._ensure_serializable(chart_data)

        except Exception as e:
            logger.error(f"Error preparing polar area chart data: {str(e)}")
            return None

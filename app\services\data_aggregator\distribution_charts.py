"""
Distribution charts data aggregation module.
"""

import pandas as pd
import numpy as np
from app.core.logging_config import configure_logging
from app.services.data_aggregator.base import ChartAggregatorBase

logger = configure_logging('app.services.data_aggregator.distribution_charts')

class DistributionChartAggregator(ChartAggregatorBase):
    """Class for distribution chart data aggregation."""
    
    def prepare_chart_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """
        Prepare data for distribution charts (histogram, box, violin).
        
        Args:
            df: The pandas DataFrame containing the data
            chart_type: The type of chart to prepare data for
            fields: Dictionary of fields to use for the chart
            
        Returns:
            Dictionary containing the chart data
        """
        if chart_type == "histogram":
            return self._prepare_histogram_data(df, fields)
        elif chart_type == "box":
            return self._prepare_distribution_data(df, chart_type, fields)
        elif chart_type == "violin":
            return self._prepare_violin_plot_data(df, fields)
        else:
            logger.warning(f"Unsupported distribution chart type: {chart_type}")
            return None
    
    def _prepare_histogram_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for histogram charts with intelligent bin sizing."""
        num_field = fields.get("numeric")
        x_field = fields.get("x")

        if not num_field and not x_field:
            logger.warning(f"Skipping Histogram: no field specified")
            return None

        # Use x_field if num_field is "count" or not specified
        field_to_use = x_field if num_field == "count" else num_field

        if field_to_use not in df.columns:
            logger.warning(f"Skipping Histogram: {field_to_use} not found in DataFrame")
            return None

        try:
            # Ensure numeric type
            df[field_to_use] = pd.to_numeric(df[field_to_use], errors='coerce')

            # Remove NaN values
            valid_data = df[field_to_use].dropna()

            if len(valid_data) == 0:
                logger.warning(f"Skipping Histogram: No valid numeric data in {field_to_use}")
                return None

            # Get min and max values
            min_val = valid_data.min()
            max_val = valid_data.max()
            data_range = max_val - min_val

            # Check for data variation - skip if all values are identical
            if data_range == 0 or valid_data.nunique() == 1:
                logger.info(f"Skipping histogram: No variation in data - all values are identical ({min_val})")
                return None

            # Determine optimal number of bins based on data characteristics
            if data_range == 0:
                bin_size = 4  # Minimum bins for single-value data
            else:
                # Calculate initial bin size using Freedman-Diaconis rule
                iqr = np.percentile(valid_data, 75) - np.percentile(valid_data, 25)
                if iqr == 0:
                    # Fall back to Sturges' rule if IQR is 0
                    bin_size = int(np.ceil(np.log2(len(valid_data))) + 1)
                else:
                    bin_width = 2 * iqr / (len(valid_data) ** (1/3))
                    bin_size = int(np.ceil(data_range / bin_width))

                # Constrain to between 4 and 12 bins
                bin_size = max(4, min(12, bin_size))

            # Adjust the range to whole numbers
            min_val = np.floor(min_val)
            max_val = np.ceil(max_val)

            # Create evenly spaced bins between min and max
            bins = np.linspace(min_val, max_val, bin_size + 1)

            # Calculate histogram using numpy with the determined bins
            counts, bins = np.histogram(valid_data, bins=bins)

            # Check if there's enough variation in the frequency distribution
            if len(set(counts)) <= 1 or np.std(counts) < 0.1 * np.mean(counts):
                logger.warning(f"Skipping Histogram: Not enough variation in frequency distribution")
                return None

            # Create whole number bin labels
            bin_labels = []
            for i in range(len(bins)-1):
                start = int(np.floor(bins[i]))
                end = int(np.ceil(bins[i+1]))
                label = f"{start}-{end}"
                bin_labels.append(label)

            chart_data = {
                "chart_type": "histogram",
                "library": "plotly",
                "data": {
                    "x": bin_labels,
                    "y": counts.tolist(),
                    "type": "bar"
                },
                "layout": {
                    "title": fields.get("chart_title", "Distribution Analysis"),
                    "xaxis": {
                        "title": fields.get("x_axis_title", f"Range of {field_to_use}"),
                        "tickangle": 45 if len(bin_labels) > 6 else 0
                    },
                    "yaxis": {
                        "title": fields.get("y_axis_title", "Frequency")
                    },
                    "bargap": 0.1
                }
            }
            return self._ensure_serializable(chart_data)

        except Exception as e:
            logger.error(f"Error preparing histogram data: {str(e)}")
            return None
    
    def _prepare_distribution_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """Prepares data for box plots and violin plots."""
        try:
            x_field = fields.get("x")  # categorical/grouping field
            y_field = fields.get("numeric")  # numerical field for distribution

            if not y_field or y_field not in df.columns:
                logger.warning(f"Skipping {chart_type}: Missing numeric field {y_field}")
                return None

            # Ensure numeric type
            df[y_field] = pd.to_numeric(df[y_field], errors='coerce')

            data = {
                "chart_type": chart_type,
                "library": "plotly",
                "data": {
                    "y": df[y_field].dropna().tolist(),
                    "type": chart_type
                },
                "layout": {
                    "title": fields.get("chart_title", ""),
                    "yaxis": {"title": fields.get("y_axis_title", y_field)}
                }
            }

            # If grouping field is provided
            if x_field and x_field in df.columns:
                grouped_data = []
                for group in df[x_field].unique():
                    group_values = df[df[x_field] == group][y_field].dropna().tolist()
                    if group_values:
                        grouped_data.append({
                            "y": group_values,
                            "name": str(group),
                            "type": chart_type
                        })
                data["data"] = grouped_data
                data["layout"]["xaxis"] = {"title": fields.get("x_axis_title", x_field)}

            return data

        except Exception as e:
            logger.error(f"Error preparing {chart_type} data: {str(e)}")
            return None
    
    def _prepare_violin_plot_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for violin plots."""
        x_field = fields.get("x")
        numeric_field = fields.get("numeric")

        if not all([x_field, numeric_field]):
            return None

        try:
            data = []
            for category in df[x_field].unique():
                values = df[df[x_field] == category][numeric_field].dropna()
                if len(values) > 0:
                    data.append({
                        "type": "violin",
                        "x": [category] * len(values),  # Repeat category for each value
                        "y": values.tolist(),
                        "name": str(category),
                        "box": {
                            "visible": True
                        },
                        "meanline": {
                            "visible": True
                        }
                    })

            return {
                "chart_type": "violin",
                "library": "plotly",
                "data": data,
                "layout": {
                    "title": fields.get("chart_title", ""),
                    "xaxis": {"title": fields.get("x_axis_title", x_field)},
                    "yaxis": {"title": fields.get("y_axis_title", numeric_field)},
                    "violinmode": "group"
                }
            }
        except Exception as e:
            logger.error(f"Error preparing violin plot data: {str(e)}")
            return None

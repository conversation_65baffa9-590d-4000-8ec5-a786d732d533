"""
Funnel Chart Aggregator

This module handles data aggregation for funnel charts.
"""

import pandas as pd
from app.core.logging_config import configure_logging
from app.services.data_aggregator.base import ChartAggregatorBase

logger = configure_logging()


class FunnelChartAggregator(ChartAggregatorBase):
    """Class for funnel chart data aggregation."""
    
    def prepare_chart_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """
        Prepare data for funnel charts.
        
        Args:
            df: The pandas DataFrame containing the data
            chart_type: The type of chart to prepare data for
            fields: Dictionary of fields to use for the chart
            
        Returns:
            Dictionary containing the chart data
        """
        if chart_type == "funnel":
            return self._prepare_funnel_chart_data(df, fields)
        else:
            logger.warning(f"Unsupported funnel chart type: {chart_type}")
            return None
    
    def _prepare_funnel_chart_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for funnel charts."""
        try:
            x_field = fields.get("x")
            x_type = fields.get("x_type")
            numeric_field = fields.get("numeric")
            
            if not x_field or not x_type:
                logger.warning("Skipping funnel chart: Missing x or x_type")
                return None
            
            # Group data by x_field
            if numeric_field and numeric_field != "count":
                grouped = df.groupby(x_field)[numeric_field].sum().reset_index()
            else:
                grouped = df.groupby(x_field).size().reset_index(name='count')
                numeric_field = "count"
            
            # Sort by value descending for funnel effect
            grouped = grouped.sort_values(numeric_field, ascending=False)
            
            # Limit to reasonable number of stages for funnel
            if len(grouped) > 8:
                grouped = grouped.head(8)
                logger.info(f"Limited funnel chart to top 8 stages")
            
            # Calculate conversion rates for funnel
            values = grouped[numeric_field].tolist()
            labels = grouped[x_field].tolist()
            
            # Add conversion percentages to labels (except for the first stage)
            if len(values) > 1:
                enhanced_labels = [labels[0]]  # First stage without percentage
                for i in range(1, len(values)):
                    conversion_rate = (values[i] / values[0]) * 100
                    enhanced_labels.append(f"{labels[i]} ({conversion_rate:.1f}%)")
                labels = enhanced_labels
            
            chart_title = fields.get("chart_title", f"{x_field.replace('_', ' ').title()} Funnel")
            chart_data = {
                "chart_type": "funnel",
                "library": "plotly",
                "data": {
                    "y": labels,
                    "x": values,
                    "type": "funnel",
                    "textinfo": "value+percent initial",
                    "textposition": "inside",
                    "connector": {
                        "line": {
                            "color": "royalblue",
                            "dash": "dot",
                            "width": 3
                        }
                    }
                },
                "layout": {
                    "title": chart_title,
                    "funnelmode": "stack",
                    "showlegend": False,
                    "margin": {"l": 150, "r": 50, "t": 80, "b": 50}
                }
            }
            return self._ensure_serializable(chart_data)
            
        except Exception as e:
            logger.error(f"Error preparing funnel chart data: {str(e)}")
            return None
    
    def _prepare_funnel_area_chart_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for funnel area charts (alternative funnel visualization)."""
        try:
            x_field = fields.get("x")
            x_type = fields.get("x_type")
            numeric_field = fields.get("numeric")
            
            if not x_field or not x_type:
                logger.warning("Skipping funnel area chart: Missing x or x_type")
                return None
            
            # Group data by x_field
            if numeric_field and numeric_field != "count":
                grouped = df.groupby(x_field)[numeric_field].sum().reset_index()
            else:
                grouped = df.groupby(x_field).size().reset_index(name='count')
                numeric_field = "count"
            
            # Sort by value descending for funnel effect
            grouped = grouped.sort_values(numeric_field, ascending=False)
            
            # Limit to reasonable number of stages
            if len(grouped) > 8:
                grouped = grouped.head(8)
                logger.info(f"Limited funnel area chart to top 8 stages")
            
            chart_title = fields.get("chart_title", f"{x_field.replace('_', ' ').title()} Funnel Area")
            chart_data = {
                "chart_type": "funnel",
                "library": "plotly",
                "data": {
                    "y": grouped[x_field].tolist(),
                    "x": grouped[numeric_field].tolist(),
                    "type": "funnelarea",
                    "textinfo": "label+value+percent total",
                    "textposition": "inside"
                },
                "layout": {
                    "title": chart_title,
                    "showlegend": False,
                    "margin": {"l": 50, "r": 50, "t": 80, "b": 50}
                }
            }
            return self._ensure_serializable(chart_data)
            
        except Exception as e:
            logger.error(f"Error preparing funnel area chart data: {str(e)}")
            return None

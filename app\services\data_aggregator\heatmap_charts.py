"""
Heatmap charts data aggregation module.
"""

import pandas as pd
import numpy as np
from app.core.logging_config import configure_logging
from app.services.data_aggregator.base import ChartAggregatorBase

logger = configure_logging('app.services.data_aggregator.heatmap_charts')

class HeatmapChartAggregator(ChartAggregatorBase):
    """Class for heatmap chart data aggregation."""

    def prepare_chart_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """
        Prepare data for heatmap charts (heatmap, calendar_heatmap).

        Args:
            df: The pandas DataFrame containing the data
            chart_type: The type of chart to prepare data for
            fields: Dictionary of fields to use for the chart

        Returns:
            Dictionary containing the chart data
        """
        if chart_type == "heatmap":
            return self._prepare_heatmap_data(df, fields)
        elif chart_type == "calendar_heatmap":
            return self._prepare_calendar_heatmap_data(df, fields)
        else:
            logger.warning(f"Unsupported heatmap chart type: {chart_type}")
            return None

    def _prepare_heatmap_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepare data for heatmap visualization"""
        x_field = fields.get("x")
        y_field = fields.get("y")
        numeric_field = fields.get("numeric")
        agg_func = fields.get("agg", "mean")
        time_agg = fields.get("time_agg")

        if not all([x_field, y_field]):
            logger.warning("Skipping Heatmap: Missing required fields")
            return None

        try:
            df = df.copy()

            # Handle count aggregation specially
            if numeric_field == "count" or agg_func == "count":
                # Use count aggregation
                agg_func = "count"
                # Create a temporary column for counting
                df["_count"] = 1
                numeric_field = "_count"

            # Handle datetime x-axis
            is_datetime_x = fields.get("x_type") == "datetime"
            if is_datetime_x:
                if not pd.api.types.is_datetime64_any_dtype(df[x_field]):
                    logger.info(f"Converting {x_field} to datetime for heatmap chart")
                    df[x_field] = pd.to_datetime(df[x_field], errors='coerce')

                    # Check if conversion was successful
                    if df[x_field].isna().all():
                        logger.warning(f"Skipping Heatmap: Could not convert {x_field} to datetime")
                        return None

                    # Drop rows with NaT values
                    df = df.dropna(subset=[x_field])

                    if df.empty:
                        logger.warning(f"Skipping Heatmap: No valid datetime values after conversion")
                        return None

                # Apply time aggregation before grouping using the common method
                # Create both a sort key and a display column
                df = self._format_datetime_columns(df, x_field, time_agg,
                                                sort_key_col='x_sort_key',
                                                display_col='x_display')

                # Use the display column for grouping instead of the original x_field
                x_field_for_grouping = 'x_display'

                # Create a mapping from display values to sort keys for later sorting
                display_to_sort_key = dict(zip(df['x_display'], df['x_sort_key']))

                logger.info(f"Created datetime mapping for heatmap: {display_to_sort_key}")
            else:
                x_field_for_grouping = x_field
                display_to_sort_key = None

            # Group by x and y fields using the appropriate x field
            if is_datetime_x:
                # Use the display column for grouping
                x_grouping_field = 'x_display'
            else:
                x_grouping_field = x_field

            logger.info(f"Using {x_grouping_field} for x-axis grouping in heatmap")

            if agg_func == "count":
                # For count, we can use size
                grouped = df.groupby([x_grouping_field, y_field]).size().reset_index(name="count")
                numeric_field = "count"
            else:
                # For other aggregations, use the specified numeric field
                if numeric_field not in df.columns:
                    logger.warning(f"Skipping Heatmap: Missing numeric field {numeric_field}")
                    return None
                grouped = df.groupby([x_grouping_field, y_field])[numeric_field].agg(agg_func).reset_index()

            # Create pivot table
            pivot_table = grouped.pivot(
                index=y_field,
                columns=x_grouping_field,
                values=numeric_field
            ).fillna(0)

            # Check if all values in the pivot table are the same
            # If so, skip this heatmap as it doesn't provide useful insights
            if pivot_table.size > 1:  # Make sure there's at least 2 cells
                unique_values = np.unique(pivot_table.values)
                if len(unique_values) == 1:
                    logger.info(f"Skipping Heatmap: All values are the same ({unique_values[0]})")
                    return None

            # Sort columns chronologically if datetime
            if is_datetime_x:
                # Create a function to sort by the sort key
                def sort_by_key(col):
                    return display_to_sort_key.get(col, col)

                # Sort the columns using the sort keys
                sorted_columns = sorted(pivot_table.columns, key=sort_by_key)
                logger.info(f"Sorted columns for heatmap: {sorted_columns}")

                # Reindex the pivot table with the sorted columns
                pivot_table = pivot_table.reindex(sorted_columns, axis=1)
                x_labels = pivot_table.columns.tolist()

                # Log the final order
                logger.info(f"Final x-axis order for heatmap: {x_labels}")

                # User-friendly formats are already applied to x_field
                # No need for tick_format as we're using the pre-formatted values
                tick_format = None
            else:
                x_labels = pivot_table.columns.tolist()
                tick_format = None

            # Prepare layout
            layout = {
                "title": fields.get("chart_title", "Heatmap"),
                "xaxis": {
                    "title": fields.get("x_axis_title", x_field.replace('_', ' ').title()),
                    "side": "bottom",
                    "type": "category",
                    "tickformat": tick_format if is_datetime_x else None,
                    "dtick": 1 if time_agg == "monthly" else None
                },
                "yaxis": {
                    "title": fields.get("y_axis_title", y_field.replace('_', ' ').title()),
                    "type": "category"
                }
            }

            chart_data = {
                "chart_type": "heatmap",
                "library": "plotly",
                "data": {
                    "z": pivot_table.values.tolist(),
                    "x": x_labels,
                    "y": pivot_table.index.tolist(),
                    "type": "heatmap",
                    "colorscale": "Viridis",
                    "hoverongaps": False
                },
                "layout": layout,
                "time_aggregation": time_agg if is_datetime_x else None
            }

            # Return serializable data (which will be rounded by _ensure_serializable)
            return self._ensure_serializable(chart_data)

        except Exception as e:
            logger.error(f"Error preparing heatmap data: {str(e)}")
            return None

    def _prepare_calendar_heatmap_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for calendar heatmap visualization."""
        x_field = fields.get("x")
        x_type = fields.get("x_type")
        numeric_field = fields.get("numeric", "count")
        time_agg = fields.get("time_agg", "daily")  # Default to daily for calendar heatmap

        if not x_field or x_type != "datetime":
            logger.warning(f"Skipping Calendar Heatmap: Missing datetime field")
            return None

        try:
            # Convert to datetime with error handling
            try:
                # Ensure x_field is properly converted to datetime before using .dt accessor
                if not pd.api.types.is_datetime64_any_dtype(df[x_field]):
                    logger.info(f"Converting {x_field} to datetime for calendar heatmap")
                    df[x_field] = pd.to_datetime(df[x_field], errors='coerce')

                # Check if conversion was successful (no NaT values)
                if df[x_field].isna().all():
                    logger.warning(f"Skipping Calendar Heatmap: Could not convert {x_field} to datetime")
                    return None

                # Drop rows with NaT values
                df = df.dropna(subset=[x_field])

                if df.empty:
                    logger.warning(f"Skipping Calendar Heatmap: No valid datetime values in {x_field}")
                    return None

                # Extract date components
                df['year'] = df[x_field].dt.year
                df['month'] = df[x_field].dt.month
                df['day'] = df[x_field].dt.day

                # Apply time aggregation with user-friendly formats using the common method
                df = self._format_datetime_columns(df, x_field, time_agg,
                                                sort_key_col='date_sort_key',
                                                display_col='date_display')

                # For quarterly aggregation, ensure proper Q1, Q2, Q3, Q4 format
                if time_agg == "quarterly":
                    logger.info("Applying quarterly formatting for calendar heatmap")
                    # Override the date_display column with proper quarterly format
                    df['date_display'] = 'Q' + df[x_field].dt.quarter.astype(str) + ' ' + df[x_field].dt.year.astype(str)
                    logger.info(f"Quarterly formatted dates: {df['date_display'].unique().tolist()}")
            except Exception as e:
                logger.error(f"Error converting {x_field} to datetime: {str(e)}")
                return None

            # Group by date using sort key for chronological ordering
            if numeric_field == "count":
                grouped = df.groupby(['date_sort_key', 'date_display']).size().reset_index(name='count')
                numeric_field = "count"
            else:
                grouped = df.groupby(['date_sort_key', 'date_display'])[numeric_field].sum().reset_index()

            # Sort by the chronological key
            grouped = grouped.sort_values('date_sort_key')

            # Check if all values are the same - skip if they are
            if len(grouped) > 1:  # Make sure there's at least 2 data points
                unique_values = grouped[numeric_field].nunique()
                if unique_values == 1:
                    logger.info(f"Skipping Calendar Heatmap: All values are the same ({grouped[numeric_field].iloc[0]})")
                    return None

            # Create a date column for component extraction
            grouped['date'] = pd.to_datetime(grouped['date_sort_key'], errors='coerce')

            # Check if conversion was successful
            if grouped['date'].isna().all():
                logger.warning(f"Skipping Calendar Heatmap: Could not convert date_sort_key to datetime")
                return None

            # Drop rows with NaT values
            grouped = grouped.dropna(subset=['date'])

            if grouped.empty:
                logger.warning(f"Skipping Calendar Heatmap: No valid datetime values after grouping")
                return None

            # Now extract the components safely
            grouped['year'] = grouped['date'].dt.year
            grouped['month'] = grouped['date'].dt.month
            grouped['day'] = grouped['date'].dt.day

            # Log the data before creating the chart
            logger.info(f"Calendar heatmap data before sorting: {grouped[['date_sort_key', 'date_display', numeric_field]].head().to_dict('records')}")

            # Ensure data is sorted chronologically by date_sort_key
            grouped = grouped.sort_values('date_sort_key')

            # Log the sorted data
            logger.info(f"Calendar heatmap data after sorting: {grouped[['date_sort_key', 'date_display', numeric_field]].head().to_dict('records')}")

            # Create calendar heatmap data
            calendar_data = {
                "x": grouped['date_display'].tolist(),  # Use user-friendly format for display
                "y": grouped[numeric_field].tolist(),
                "z": grouped[numeric_field].tolist(),  # For color intensity
                "type": "heatmap",
                "colorscale": "Viridis"
            }

            # Log the final x values to verify order
            logger.info(f"Final x values for calendar heatmap: {grouped['date_display'].tolist()}")

            return {
                "chart_type": "calendar_heatmap",
                "library": "plotly",
                "data": calendar_data,
                "layout": {
                    "title": fields.get("chart_title", f"Calendar View of {x_field}"),
                    "xaxis": {"title": fields.get("x_axis_title", "Date")},
                    "yaxis": {"title": fields.get("y_axis_title", numeric_field.replace('_', ' ').title())},
                    "calendar": True  # Flag for frontend to render as calendar
                }
            }

        except Exception as e:
            logger.error(f"Error preparing calendar heatmap data: {str(e)}")
            return None

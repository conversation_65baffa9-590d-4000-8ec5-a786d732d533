"""
Hierarchical charts data aggregation module.

This module contains functions for preparing data for hierarchical charts like treemap, sunburst, and icicle.
"""

import pandas as pd
import numpy as np
from app.core.logging_config import configure_logging
from app.services.data_aggregator.base import ChartAggregatorBase

logger = configure_logging('app.services.data_aggregator.hierarchical_charts')

class HierarchicalChartAggregator(ChartAggregatorBase):
    """Class for hierarchical chart data aggregation."""
    
    def prepare_chart_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """
        Prepare data for hierarchical charts (treemap, sunburst, icicle).
        
        Args:
            df: The pandas DataFrame containing the data
            chart_type: The type of chart to prepare data for
            fields: Dictionary of fields to use for the chart
            
        Returns:
            Dictionary containing the chart data
        """
        return self._prepare_hierarchical_data(df, chart_type, fields)
    
    def _prepare_hierarchical_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """Prepares data for hierarchical charts (treemap, sunburst, icicle)."""
        # Extract hierarchy fields and numeric field
        hierarchy = fields.get("hierarchy")
        numeric_field = fields.get("numeric")
        time_agg = fields.get("time_agg", "monthly")  # Default to monthly for datetime columns

        # Log the fields for debugging
        logger.info(f"Preparing {chart_type} with hierarchy: {hierarchy}, numeric: {numeric_field}")

        if not hierarchy or not isinstance(hierarchy, list) or len(hierarchy) == 0:
            logger.warning(f"Skipping {chart_type}: Missing or invalid hierarchy field list")
            return None

        try:
            # Check if all hierarchy columns exist in the dataframe
            for col in hierarchy:
                if col not in df.columns:
                    logger.warning(f"Skipping {chart_type}: Hierarchy column {col} not found in DataFrame")
                    return None

                # Check if any hierarchy column is a datetime column and format it
                if pd.api.types.is_datetime64_any_dtype(df[col]):
                    logger.info(f"Formatting datetime column {col} for hierarchical chart")
                    # Create a copy of the dataframe to avoid modifying the original
                    df = df.copy()
                    # Format the datetime column with user-friendly format
                    df = self._format_datetime_columns(df, col, time_agg,
                                                   sort_key_col=f"{col}_sort_key",
                                                   display_col=col)  # Overwrite the original column

            # If numeric field is 'count' or not specified, we'll count rows
            if numeric_field == "count" or not numeric_field:
                # Group by all hierarchy levels and count rows
                grouped = df.groupby(hierarchy).size().reset_index(name="count")
                value_field = "count"
            else:
                # Check if numeric field exists
                if numeric_field not in df.columns:
                    logger.warning(f"Skipping {chart_type}: Numeric field {numeric_field} not found in DataFrame")
                    return None

                # Group by all hierarchy levels and sum the numeric field
                grouped = df.groupby(hierarchy)[numeric_field].sum().reset_index()
                value_field = numeric_field

            # Prepare data for hierarchical charts
            if chart_type == "treemap":
                return self._prepare_treemap_data(grouped, hierarchy, value_field, fields)
            elif chart_type == "sunburst":
                return self._prepare_sunburst_data(grouped, hierarchy, value_field, fields)
            elif chart_type == "icicle":
                return self._prepare_icicle_data(grouped, hierarchy, value_field, fields)
            else:
                logger.warning(f"Unsupported hierarchical chart type: {chart_type}")
                return None

        except Exception as e:
            logger.error(f"Error preparing {chart_type} data: {str(e)}")
            return None
    
    def _prepare_treemap_data(self, grouped_df: pd.DataFrame, hierarchy: list, value_field: str, fields: dict) -> dict:
        """Prepares data for treemap charts."""
        try:
            # Create labels and parents for treemap
            labels = []
            parents = []
            values = []
            ids = []

            # Add root node
            root_name = "Total"
            labels.append(root_name)
            parents.append("")
            values.append(grouped_df[value_field].sum())
            ids.append(root_name)

            # Process each level of the hierarchy
            for level in range(len(hierarchy)):
                # Get columns for current path
                path_cols = hierarchy[:level+1]

                # Group by the current path
                level_data = grouped_df.groupby(path_cols)[value_field].sum().reset_index()

                for _, row in level_data.iterrows():
                    # Create label for current node
                    # Use the formatted value if it's already been processed by _format_datetime_columns
                    label = str(row[hierarchy[level]])
                    labels.append(label)

                    # Create ID for current node (full path)
                    node_id = "-".join([str(row[col]) for col in path_cols])
                    ids.append(node_id)

                    # Create parent ID
                    if level == 0:
                        # First level nodes have root as parent
                        parents.append(root_name)
                    else:
                        # Other nodes have their parent path as parent
                        parent_path = "-".join([str(row[col]) for col in path_cols[:-1]])
                        parents.append(parent_path)

                    # Add value
                    values.append(row[value_field])

            return {
                "chart_type": "treemap",
                "library": "plotly",
                "data": {
                    "type": "treemap",
                    "labels": labels,
                    "parents": parents,
                    "values": values,
                    "ids": ids,
                    "branchvalues": "total"
                },
                "layout": {
                    "title": fields.get("chart_title", f"Treemap of {' > '.join(hierarchy)}")
                }
            }
        except Exception as e:
            logger.error(f"Error preparing treemap data: {str(e)}")
            return None
    
    def _prepare_sunburst_data(self, grouped_df: pd.DataFrame, hierarchy: list, value_field: str, fields: dict) -> dict:
        """Prepares data for sunburst charts."""
        try:
            # Create labels and parents for sunburst
            labels = []
            parents = []
            values = []
            ids = []

            # Add root node
            root_name = "Total"
            labels.append(root_name)
            parents.append("")
            values.append(grouped_df[value_field].sum())
            ids.append(root_name)

            # Process each level of the hierarchy
            for level in range(len(hierarchy)):
                # Get columns for current path
                path_cols = hierarchy[:level+1]

                # Group by the current path
                level_data = grouped_df.groupby(path_cols)[value_field].sum().reset_index()

                for _, row in level_data.iterrows():
                    # Create label for current node
                    # Use the formatted value if it's already been processed by _format_datetime_columns
                    label = str(row[hierarchy[level]])
                    labels.append(label)

                    # Create ID for current node (full path)
                    node_id = "-".join([str(row[col]) for col in path_cols])
                    ids.append(node_id)

                    # Create parent ID
                    if level == 0:
                        # First level nodes have root as parent
                        parents.append(root_name)
                    else:
                        # Other nodes have their parent path as parent
                        parent_path = "-".join([str(row[col]) for col in path_cols[:-1]])
                        parents.append(parent_path)

                    # Add value
                    values.append(row[value_field])

            return {
                "chart_type": "sunburst",
                "library": "plotly",
                "data": {
                    "type": "sunburst",
                    "labels": labels,
                    "parents": parents,
                    "values": values,
                    "ids": ids,
                    "branchvalues": "total"
                },
                "layout": {
                    "title": fields.get("chart_title", f"Sunburst of {' > '.join(hierarchy)}")
                }
            }
        except Exception as e:
            logger.error(f"Error preparing sunburst data: {str(e)}")
            return None
    
    def _prepare_icicle_data(self, grouped_df: pd.DataFrame, hierarchy: list, value_field: str, fields: dict) -> dict:
        """Prepares data for icicle charts."""
        try:
            # Handle NaN, Infinity, and -Infinity values in the value field
            if value_field in grouped_df.columns:
                # Replace NaN with 0
                grouped_df[value_field] = grouped_df[value_field].fillna(0)

                # Replace Infinity and -Infinity with large but finite values
                grouped_df[value_field] = grouped_df[value_field].replace([float('inf'), float('-inf')], [1e10, -1e10])

                # Log any replacements
                if grouped_df[value_field].isnull().any() or (grouped_df[value_field] == 1e10).any() or (grouped_df[value_field] == -1e10).any():
                    logger.warning(f"Replaced non-finite values in {value_field} for icicle chart")

            # Create labels and parents for icicle
            labels = []
            parents = []
            values = []
            ids = []

            # Add root node
            root_name = "Total"
            labels.append(root_name)
            parents.append("")
            # Ensure the sum is a finite value
            total_value = grouped_df[value_field].sum()
            if pd.isna(total_value) or np.isinf(total_value):
                logger.warning(f"Root node has non-finite value: {total_value}, replacing with 0")
                total_value = 0
            values.append(float(total_value))
            ids.append(root_name)

            # Process each level of the hierarchy
            for level in range(len(hierarchy)):
                # Get columns for current path
                path_cols = hierarchy[:level+1]

                # Group by the current path
                level_data = grouped_df.groupby(path_cols)[value_field].sum().reset_index()

                # Handle any non-finite values in the aggregated data
                if value_field in level_data.columns:
                    # Replace NaN with 0
                    level_data[value_field] = level_data[value_field].fillna(0)

                    # Replace Infinity and -Infinity with large but finite values
                    level_data[value_field] = level_data[value_field].replace([float('inf'), float('-inf')], [1e10, -1e10])

                for _, row in level_data.iterrows():
                    # Create label for current node
                    label = str(row[hierarchy[level]])
                    labels.append(label)

                    # Create ID for current node (full path)
                    node_id = "-".join([str(row[col]) for col in path_cols])
                    ids.append(node_id)

                    # Create parent ID
                    if level == 0:
                        # First level nodes have root as parent
                        parents.append(root_name)
                    else:
                        # Other nodes have their parent path as parent
                        parent_path = "-".join([str(row[col]) for col in path_cols[:-1]])
                        parents.append(parent_path)

                    # Add value (ensure it's a finite float)
                    node_value = row[value_field]
                    if pd.isna(node_value) or np.isinf(node_value):
                        logger.warning(f"Node {node_id} has non-finite value: {node_value}, replacing with 0")
                        node_value = 0
                    values.append(float(node_value))

            # Create the chart data
            chart_data = {
                "chart_type": "icicle",
                "library": "plotly",
                "data": {
                    "type": "icicle",
                    "labels": labels,
                    "parents": parents,
                    "values": values,
                    "ids": ids,
                    "branchvalues": "total"
                },
                "layout": {
                    "title": fields.get("chart_title", f"Icicle of {' > '.join(hierarchy)}")
                }
            }

            # Use the _ensure_serializable method to guarantee JSON compatibility
            return self._ensure_serializable(chart_data)
        except Exception as e:
            logger.error(f"Error preparing icicle data: {str(e)}")
            return None

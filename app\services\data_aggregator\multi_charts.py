"""
Multi-charts data aggregation module.
"""

import pandas as pd
import numpy as np
from app.core.logging_config import configure_logging
from app.services.data_aggregator.base import ChartAggregatorBase

logger = configure_logging('app.services.data_aggregator.multi_charts')

class MultiChartAggregator(ChartAggregatorBase):
    """Class for multi-chart data aggregation."""
    
    def prepare_chart_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """
        Prepare data for multi-charts (multi_line, multi_area, combo_bar_line).
        
        Args:
            df: The pandas DataFrame containing the data
            chart_type: The type of chart to prepare data for
            fields: Dictionary of fields to use for the chart
            
        Returns:
            Dictionary containing the chart data
        """
        if chart_type == "multi_line":
            return self._prepare_multi_line_data(df, fields)
        elif chart_type == "multi_area":
            return self._prepare_multi_area_data(df, fields)
        elif chart_type == "combo_bar_line":
            return self._prepare_combo_bar_line_data(df, fields)
        else:
            logger.warning(f"Unsupported multi-chart type: {chart_type}")
            return None
    
    def _prepare_multi_line_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for multi-line charts (multiple line series on same chart)."""
        x_field = fields.get("x")
        x_type = fields.get("x_type")
        y_fields = fields.get("y_fields", [])  # List of numerical fields
        time_agg = fields.get("time_agg", "monthly")

        if not x_field or not x_type or not y_fields:
            logger.warning(f"Skipping multi_line: Missing required fields")
            return None

        try:
            # Handle datetime x-axis
            if x_type == "datetime":
                # Convert to datetime with proper error handling
                if not pd.api.types.is_datetime64_any_dtype(df[x_field]):
                    logger.info(f"Converting {x_field} to datetime for multi_line chart")
                    df[x_field] = pd.to_datetime(df[x_field], errors='coerce')

                    # Check if conversion was successful
                    if df[x_field].isna().all():
                        logger.warning(f"Skipping multi_line: Could not convert {x_field} to datetime")
                        return None

                    # Drop rows with NaT values
                    df = df.dropna(subset=[x_field])

                    if df.empty:
                        logger.warning(f"Skipping multi_line: No valid datetime values after conversion")
                        return None

                # Create period for grouping with user-friendly formats using the common method
                df = self._format_datetime_columns(df, x_field, time_agg,
                                                sort_key_col='period_sort_key',
                                                display_col='period')

                # Create traces for each y field
                traces = []
                for y_field in y_fields:
                    if y_field in df.columns:
                        # Group by period and aggregate, using sort key for chronological ordering
                        grouped = df.groupby(['period_sort_key', 'period'])[y_field].agg('mean').reset_index()
                        grouped = grouped.sort_values('period_sort_key')  # Sort chronologically

                        traces.append({
                            "x": grouped['period'].tolist(),
                            "y": grouped[y_field].tolist(),
                            "type": "scatter",
                            "mode": "lines+markers",
                            "name": y_field.replace('_', ' ').title()
                        })
            else:
                # For non-datetime x-axis
                traces = []
                for y_field in y_fields:
                    if y_field in df.columns:
                        traces.append({
                            "x": df[x_field].tolist(),
                            "y": df[y_field].tolist(),
                            "type": "scatter",
                            "mode": "lines+markers",
                            "name": y_field.replace('_', ' ').title()
                        })

            if not traces:
                logger.warning(f"Skipping multi_line: No valid y fields found")
                return None

            return {
                "chart_type": "multi_line",
                "library": "plotly",
                "data": traces,
                "layout": {
                    "title": fields.get("chart_title", "Multiple Metrics Over Time"),
                    "xaxis": {"title": fields.get("x_axis_title", "Date" if x_type == "datetime" else x_field.replace('_', ' ').title())},
                    "yaxis": {"title": fields.get("y_axis_title", "Value")},
                    "legend": {"orientation": "h", "y": 1.1}
                }
            }

        except Exception as e:
            logger.error(f"Error preparing multi-line chart data: {str(e)}")
            return None
    
    def _prepare_multi_area_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for multi-area charts (multiple stacked area series)."""
        x_field = fields.get("x")
        x_type = fields.get("x_type")
        y_fields = fields.get("y_fields", [])  # List of numerical fields
        time_agg = fields.get("time_agg", "monthly")

        if not x_field or not x_type or not y_fields:
            logger.warning(f"Skipping multi_area: Missing required fields")
            return None

        try:
            # Handle datetime x-axis
            if x_type == "datetime":
                # Convert to datetime with proper error handling
                if not pd.api.types.is_datetime64_any_dtype(df[x_field]):
                    logger.info(f"Converting {x_field} to datetime for multi_area chart")
                    df[x_field] = pd.to_datetime(df[x_field], errors='coerce')

                    # Check if conversion was successful
                    if df[x_field].isna().all():
                        logger.warning(f"Skipping multi_area: Could not convert {x_field} to datetime")
                        return None

                    # Drop rows with NaT values
                    df = df.dropna(subset=[x_field])

                    if df.empty:
                        logger.warning(f"Skipping multi_area: No valid datetime values after conversion")
                        return None

                # Create period for grouping with user-friendly formats using the common method
                df = self._format_datetime_columns(df, x_field, time_agg,
                                                sort_key_col='period_sort_key',
                                                display_col='period')

                # Create traces for each y field
                traces = []
                for y_field in y_fields:
                    if y_field in df.columns:
                        # Group by period and aggregate, using sort key for chronological ordering
                        grouped = df.groupby(['period_sort_key', 'period'])[y_field].agg('sum').reset_index()
                        grouped = grouped.sort_values('period_sort_key')  # Sort chronologically

                        traces.append({
                            "x": grouped['period'].tolist(),
                            "y": grouped[y_field].tolist(),
                            "type": "scatter",
                            "mode": "lines",
                            "fill": "tozeroy",
                            "name": y_field.replace('_', ' ').title()
                        })
            else:
                # For non-datetime x-axis
                traces = []
                for y_field in y_fields:
                    if y_field in df.columns:
                        traces.append({
                            "x": df[x_field].tolist(),
                            "y": df[y_field].tolist(),
                            "type": "scatter",
                            "mode": "lines",
                            "fill": "tozeroy",
                            "name": y_field.replace('_', ' ').title()
                        })

            if not traces:
                logger.warning(f"Skipping multi_area: No valid y fields found")
                return None

            return {
                "chart_type": "multi_area",
                "library": "plotly",
                "data": traces,
                "layout": {
                    "title": fields.get("chart_title", "Cumulative Trends Over Time"),
                    "xaxis": {"title": fields.get("x_axis_title", "Date" if x_type == "datetime" else x_field.replace('_', ' ').title())},
                    "yaxis": {"title": fields.get("y_axis_title", "Value")},
                    "legend": {"orientation": "h", "y": 1.1}
                }
            }

        except Exception as e:
            logger.error(f"Error preparing multi-area chart data: {str(e)}")
            return None
    
    def _prepare_combo_bar_line_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for combo bar-line charts (bar chart with line overlay)."""
        x_field = fields.get("x")
        x_type = fields.get("x_type")
        y1_field = fields.get("y1")  # For bar chart
        y2_field = fields.get("y2")  # For line chart

        if not all([x_field, x_type, y1_field, y2_field]):
            logger.warning(f"Skipping combo_bar_line: Missing required fields")
            return None

        try:
            # Group by x_field and aggregate both y fields
            if x_type == "categorical":
                # For categorical x-axis, group and aggregate
                grouped = df.groupby(x_field).agg({y1_field: 'sum', y2_field: 'sum'}).reset_index()
            else:
                # For other types, use the data as is
                grouped = df[[x_field, y1_field, y2_field]].copy()

            # Create two separate traces
            bar_trace = {
                "x": grouped[x_field].tolist(),
                "y": grouped[y1_field].tolist(),
                "type": "bar",
                "name": y1_field.replace('_', ' ').title()
            }

            line_trace = {
                "x": grouped[x_field].tolist(),
                "y": grouped[y2_field].tolist(),
                "type": "scatter",
                "mode": "lines+markers",
                "name": y2_field.replace('_', ' ').title(),
                "yaxis": "y2"  # Use secondary y-axis
            }

            return {
                "chart_type": "combo_bar_line",
                "library": "plotly",
                "data": [bar_trace, line_trace],
                "layout": {
                    "title": fields.get("chart_title", f"{y1_field.replace('_', ' ').title()} and {y2_field.replace('_', ' ').title()} by {x_field.replace('_', ' ').title()}"),
                    "xaxis": {"title": fields.get("x_axis_title", x_field.replace('_', ' ').title())},
                    "yaxis": {"title": fields.get("y_axis_title", y1_field.replace('_', ' ').title())},
                    "yaxis2": {
                        "title": y2_field.replace('_', ' ').title(),
                        "overlaying": "y",
                        "side": "right"
                    },
                    "legend": {"orientation": "h", "y": 1.1}
                }
            }

        except Exception as e:
            logger.error(f"Error preparing combo bar-line chart data: {str(e)}")
            return None

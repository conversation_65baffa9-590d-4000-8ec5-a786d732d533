"""
Multi-group charts data aggregation module.
"""

import pandas as pd
import numpy as np
from app.core.logging_config import configure_logging
from app.services.data_aggregator.base import ChartAggregatorBase

logger = configure_logging('app.services.data_aggregator.multi_group_charts')

class MultiGroupChartAggregator(ChartAggregatorBase):
    """Class for multi-group chart data aggregation."""

    def prepare_chart_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """
        Prepare data for multi-group charts (grouped_bar).

        Args:
            df: The pandas DataFrame containing the data
            chart_type: The type of chart to prepare data for
            fields: Dictionary of fields to use for the chart

        Returns:
            Dictionary containing the chart data
        """
        if chart_type == "grouped_bar":
            return self._prepare_dual_category_data(df, chart_type, fields)
        else:
            logger.warning(f"Unsupported multi-group chart type: {chart_type}")
            return None

    def _prepare_dual_category_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """Prepares data for dual category charts (grouped_bar, etc)."""
        x_field = fields.get("x")
        x_type = fields.get("x_type")
        group_field = fields.get("group")
        # group_type is used in frontend rendering
        _ = fields.get("group_type", "categorical")
        num_field = fields.get("numeric")
        agg_func = fields.get("agg", "sum")

        if not x_field or not x_type or not group_field:
            logger.warning(f"Skipping {chart_type}: Missing x, x_type, or group.")
            return None

        try:
            # Handle datetime formatting for x-axis if needed
            if x_type == "datetime":
                # Convert to datetime with proper error handling
                if not pd.api.types.is_datetime64_any_dtype(df[x_field]):
                    logger.info(f"Converting {x_field} to datetime for {chart_type} chart")
                    df[x_field] = pd.to_datetime(df[x_field], errors='coerce')

                    # Check if conversion was successful
                    if df[x_field].isna().all():
                        logger.warning(f"Skipping {chart_type}: Could not convert {x_field} to datetime")
                        return None

                    # Drop rows with NaT values
                    df = df.dropna(subset=[x_field])

                    if df.empty:
                        logger.warning(f"Skipping {chart_type}: No valid datetime values after conversion")
                        return None

                # Format datetime for quarterly display
                time_agg = fields.get("time_agg", "monthly")

                # Create a temporary column for grouping with proper formatting
                df = df.copy()
                df['formatted_date'] = df[x_field]

                if time_agg == "quarterly":
                    # Format as Q1 2023, Q2 2023, etc.
                    df['formatted_date'] = 'Q' + df[x_field].dt.quarter.astype(str) + ' ' + df[x_field].dt.year.astype(str)
                elif time_agg == "monthly":
                    # Format as Jan 2023, Feb 2023, etc.
                    df['formatted_date'] = df[x_field].dt.strftime('%b %Y')
                elif time_agg == "yearly":
                    # Format as 2023, 2024, etc.
                    df['formatted_date'] = df[x_field].dt.strftime('%Y')

                # Use the formatted date for grouping
                x_field_for_grouping = 'formatted_date'
            else:
                x_field_for_grouping = x_field

            # Group by both x and group fields
            grouped = df.groupby([x_field_for_grouping, group_field])[num_field].agg(agg_func).reset_index()

            # Skip if all numeric values are the same
            if len(grouped[num_field].unique()) <= 1:
                logger.info(f"Skipping {chart_type}: No variation in {num_field} values after {agg_func} aggregation")
                return None

            # Group data by the x_field
            pivot_data = grouped.pivot(index=x_field_for_grouping, columns=group_field, values=num_field).fillna(0)

            # Create data for each group as a separate trace
            data = []
            for col in pivot_data.columns:
                # Format the name to be more user-friendly if it's a datetime
                name = str(col)
                if isinstance(col, pd.Timestamp):
                    # Format datetime objects in the legend to match our quarterly format
                    quarter = col.quarter
                    year = col.year
                    name = f"Q{quarter} {year}"

                data.append({
                    "x": pivot_data.index.tolist(),
                    "y": pivot_data[col].tolist(),
                    "name": name,
                    "type": "bar"
                })

            chart_data = {
                "chart_type": chart_type,
                "library": "plotly",
                "data": data,
                "layout": {
                    "barmode": "group",
                    "title": fields.get("chart_title", f"{x_field.replace('_', ' ').title()} by {group_field.replace('_', ' ').title()}"),
                    "xaxis": {"title": fields.get("x_axis_title", x_field.replace('_', ' ').title())},
                    "yaxis": {"title": fields.get("y_axis_title", num_field.replace('_', ' ').title())}
                }
            }
            return self._ensure_serializable(chart_data)

        except Exception as e:
            logger.error(f"Error preparing {chart_type} data: {str(e)}")
            return None

    def _prepare_group_based_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """Prepares data for group-based charts (grouped_bar, etc)."""
        x_field = fields.get("x")
        group_field = fields.get("group")
        numeric_field = fields.get("numeric")
        agg_func = fields.get("agg", "sum")

        if not all([x_field, group_field, numeric_field]):
            logger.warning(f"Skipping {chart_type}: Missing required fields")
            return None

        try:
            df = df.copy()

            # Handle datetime aggregation
            if fields.get("x_type") == "datetime":
                # Convert to datetime with proper error handling
                if not pd.api.types.is_datetime64_any_dtype(df[x_field]):
                    logger.info(f"Converting {x_field} to datetime for {chart_type} chart")
                    df[x_field] = pd.to_datetime(df[x_field], errors='coerce')

                    # Check if conversion was successful
                    if df[x_field].isna().all():
                        logger.warning(f"Skipping {chart_type}: Could not convert {x_field} to datetime")
                        return None

                    # Drop rows with NaT values
                    df = df.dropna(subset=[x_field])

                    if df.empty:
                        logger.warning(f"Skipping {chart_type}: No valid datetime values after conversion")
                        return None

                # Create a date group column for aggregation with user-friendly formats using the common method
                time_agg = fields.get("time_agg", "monthly")  # Default to monthly aggregation
                df = self._format_datetime_columns(df, x_field, time_agg,
                                                sort_key_col='date_sort_key',
                                                display_col='date_group')

                # Double-check that quarterly formatting is correct
                if time_agg == "quarterly":
                    # Ensure date_group column has Q1, Q2, etc. format
                    df['date_group'] = 'Q' + df[x_field].dt.quarter.astype(str) + ' ' + df[x_field].dt.year.astype(str)

                # First aggregate by date_group and group_field, using sort key for chronological ordering
                grouped = df.groupby(['date_sort_key', 'date_group', group_field])[numeric_field].agg(agg_func).reset_index()

                # Create pivot table using the aggregated data
                pivot_table = grouped.pivot(
                    index=['date_sort_key', 'date_group'],
                    columns=group_field,
                    values=numeric_field
                ).fillna(0)

                # Sort by the chronological key
                pivot_table = pivot_table.sort_index(level='date_sort_key')

                # Reset index to get date_group as a column for x values
                pivot_table = pivot_table.reset_index(level='date_sort_key', drop=True)

                # Get the formatted date values for x-axis
                x_values = pivot_table.index.tolist()

                # Check if we need to format the x-axis values for quarterly data
                if time_agg == "quarterly" and x_values and isinstance(x_values[0], str) and not x_values[0].startswith('Q'):
                    # Try to extract quarter and year from the period values if they're not already in Q# YYYY format
                    try:
                        # Convert to datetime first if they're not already in the right format
                        temp_df = pd.DataFrame({'date': x_values})
                        temp_df['date'] = pd.to_datetime(temp_df['date'], errors='coerce')
                        # Format as Q# YYYY
                        x_values = ['Q' + str(d.quarter) + ' ' + str(d.year) for d in temp_df['date'] if not pd.isna(d)]
                    except Exception as e:
                        logger.warning(f"Could not format quarterly x-axis values: {e}")
                        # Keep original values if formatting fails
            else:
                # For non-datetime fields, use original pivot logic
                pivot_table = df.pivot_table(
                    values=numeric_field,
                    index=x_field,
                    columns=group_field,
                    aggfunc=agg_func,
                    fill_value=0
                )

            # Check if all values in the pivot table are the same
            # If so, skip this chart as it doesn't provide useful insights
            if pivot_table.size > 1:  # Make sure there's at least 2 cells
                unique_values = np.unique(pivot_table.values)
                if len(unique_values) == 1:
                    logger.info(f"Skipping {chart_type}: All values are the same ({unique_values[0]})")
                    return None

            # Transform data into separate traces for each group
            data = []
            if 'x_values' not in locals():
                x_values = pivot_table.index.tolist()  # Use the index if x_values wasn't set above

            for group in pivot_table.columns:
                # Format the name to be more user-friendly if it's a datetime
                name = str(group)
                if isinstance(group, pd.Timestamp):
                    # Format datetime objects in the legend to match our quarterly format
                    quarter = group.quarter
                    year = group.year
                    name = f"Q{quarter} {year}"

                data.append({
                    "x": x_values,  # These are already the formatted date strings
                    "y": pivot_table[group].tolist(),
                    "type": "bar",
                    "name": name  # This will show in the legend
                })

            return {
                "chart_type": chart_type,
                "library": "plotly",
                "data": data,
                "layout": {
                    "barmode": "group",
                    "title": fields.get("chart_title", ""),
                    "xaxis": {"title": fields.get("x_axis_title", x_field)},
                    "yaxis": {"title": fields.get("y_axis_title", numeric_field)}
                }
            }

        except Exception as e:
            logger.error(f"Error preparing {chart_type} data: {e}")
            return None

"""
Scatter charts data aggregation module.
"""

import pandas as pd
import numpy as np
from app.core.logging_config import configure_logging
from app.services.data_aggregator.base import ChartAggregatorBase

logger = configure_logging('app.services.data_aggregator.scatter_charts')

class ScatterChartAggregator(ChartAggregatorBase):
    """Class for scatter chart data aggregation."""
    
    def prepare_chart_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """
        Prepare data for scatter charts (scatter, bubble, scatter_matrix).

        Args:
            df: The pandas DataFrame containing the data
            chart_type: The type of chart to prepare data for
            fields: Dictionary of fields to use for the chart

        Returns:
            Dictionary containing the chart data
        """
        if chart_type == "scatter":
            return self._prepare_scatter_data(df, fields)
        elif chart_type == "bubble":
            return self._prepare_bubble_chart_data(df, fields)
        elif chart_type == "scatter_matrix":
            return self._prepare_scatter_matrix_data(df, fields)
        else:
            logger.warning(f"Unsupported scatter chart type: {chart_type}")
            return None
    
    def _prepare_scatter_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for scatter charts."""
        x_field = fields.get("x")
        x_type = fields.get("x_type")
        y_field = fields.get("y")
        
        if not x_field or not x_type or not y_field:
            logger.warning("Skipping scatter chart: Missing required fields")
            return None
        
        try:
            if x_type == "numerical" and y_field:
                # Ensure both fields are numeric and clean the data
                x_data = pd.to_numeric(df[x_field], errors='coerce').dropna()
                y_data = pd.to_numeric(df[y_field], errors='coerce').dropna()

                # Check if we have enough data points
                if len(x_data) < 2 or len(y_data) < 2:
                    logger.warning(f"Skipping scatter chart: Not enough valid data points")
                    return None

                # Check for variation in both axes - skip if either axis has no variation
                x_variation = x_data.nunique() > 1 and x_data.std() > 0
                y_variation = y_data.nunique() > 1 and y_data.std() > 0

                if not x_variation:
                    logger.info(f"Skipping scatter chart: No variation in X-axis ({x_field}) - all values are identical")
                    return None

                if not y_variation:
                    logger.info(f"Skipping scatter chart: No variation in Y-axis ({y_field}) - all values are identical")
                    return None

                # Align the data (in case dropna removed different indices)
                common_indices = x_data.index.intersection(y_data.index)
                x_values = x_data.loc[common_indices].tolist()
                y_values = y_data.loc[common_indices].tolist()

                chart_data = {
                    "chart_type": "scatter",
                    "library": "plotly",
                    "data": {
                        "x": x_values,
                        "y": y_values,
                        "type": "scatter",
                        "mode": "markers"
                    },
                    "layout": {
                        "title": fields.get("chart_title", f"Relationship between {x_field.replace('_', ' ').title()} and {y_field.replace('_', ' ').title()}"),
                        "xaxis": {"title": fields.get("x_axis_title", x_field.replace('_', ' ').title())},
                        "yaxis": {"title": fields.get("y_axis_title", y_field.replace('_', ' ').title())}
                    }
                }
                return self._ensure_serializable(chart_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Error preparing scatter chart data: {str(e)}")
            return None
    
    def _prepare_bubble_chart_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for bubble charts."""
        x_field = fields.get("x")
        y_field = fields.get("y")
        size_field = fields.get("size")
        agg_func = fields.get("agg", "sum")

        if not all([x_field, y_field, size_field]):
            logger.warning("Skipping bubble chart: Missing required fields")
            return None

        try:
            # Group and aggregate data if x is categorical
            if fields.get("x_type") == "categorical":
                grouped = df.groupby(x_field).agg({
                    y_field: agg_func,
                    size_field: agg_func
                }).reset_index()
            else:
                grouped = df

            # Check for variation in Y-axis and size values - skip if no variation
            y_variation = grouped[y_field].nunique() > 1 and grouped[y_field].std() > 0
            size_variation = grouped[size_field].nunique() > 1 and grouped[size_field].std() > 0

            if not y_variation:
                logger.info(f"Skipping bubble chart: No variation in Y-axis ({y_field}) - all values are identical")
                return None

            if not size_variation:
                logger.info(f"Skipping bubble chart: No variation in bubble size ({size_field}) - all values are identical")
                return None

            return {
                "chart_type": "bubble_chart",
                "library": "plotly",
                "data": {
                    "x": grouped[x_field].tolist(),
                    "y": grouped[y_field].tolist(),
                    "size": grouped[size_field].tolist(),
                    "type": "scatter",
                    "mode": "markers",
                    "marker": {"sizemode": "area"}
                },
                "layout": {
                    "title": fields.get("chart_title", "Bubble Chart"),
                    "xaxis": {"title": fields.get("x_axis_title", x_field)},
                    "yaxis": {"title": fields.get("y_axis_title", y_field)}
                }
            }

        except Exception as e:
            logger.error(f"Error preparing bubble chart data: {str(e)}")
            return None

    def _prepare_scatter_matrix_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for scatter matrix charts."""
        try:
            # Get all numeric columns for scatter matrix
            numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()

            # Remove any columns that might not be suitable for scatter matrix
            excluded_columns = ['id', 'index', 'row_number']
            numeric_columns = [col for col in numeric_columns if col.lower() not in excluded_columns]

            if len(numeric_columns) < 2:
                logger.warning("Skipping scatter matrix: Need at least 2 numeric columns")
                return None

            # Limit to reasonable number of columns (max 6 for readability)
            if len(numeric_columns) > 6:
                numeric_columns = numeric_columns[:6]
                logger.info(f"Limited scatter matrix to first 6 numeric columns")

            # Sample data if too many rows for performance
            sample_df = df
            if len(df) > 1000:
                sample_df = df.sample(n=1000, random_state=42)
                logger.info(f"Sampled 1000 rows for scatter matrix performance")

            # Prepare data for scatter matrix
            matrix_data = []
            for col in numeric_columns:
                matrix_data.append({
                    'label': col.replace('_', ' ').title(),
                    'values': sample_df[col].dropna().tolist()
                })

            chart_title = fields.get("chart_title", "Scatter Matrix")
            chart_data = {
                "chart_type": "scatter_matrix",
                "library": "plotly",
                "data": {
                    "type": "splom",  # Scatter plot matrix
                    "dimensions": matrix_data,
                    "showupperhalf": False,  # Only show lower triangle
                    "diagonal": {"visible": False},  # Hide diagonal
                    "marker": {
                        "size": 4,
                        "opacity": 0.6,
                        "color": "blue"
                    }
                },
                "layout": {
                    "title": chart_title,
                    "dragmode": "select",
                    "hovermode": "closest",
                    "plot_bgcolor": "rgba(240,240,240,0.95)",
                    "margin": {"l": 80, "r": 80, "t": 80, "b": 80}
                }
            }
            return self._ensure_serializable(chart_data)

        except Exception as e:
            logger.error(f"Error preparing scatter matrix data: {str(e)}")
            return None

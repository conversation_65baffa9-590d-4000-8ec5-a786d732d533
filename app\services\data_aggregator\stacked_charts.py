"""
Stacked charts data aggregation module.
"""

import pandas as pd
import numpy as np
from app.core.logging_config import configure_logging
from app.services.data_aggregator.base import ChartAggregatorBase

logger = configure_logging('app.services.data_aggregator.stacked_charts')

class StackedChartAggregator(ChartAggregatorBase):
    """Class for stacked chart data aggregation."""

    def prepare_chart_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """
        Prepare data for stacked charts (stacked_bar, stacked_line, stacked_area).

        Args:
            df: The pandas DataFrame containing the data
            chart_type: The type of chart to prepare data for
            fields: Dictionary of fields to use for the chart

        Returns:
            Dictionary containing the chart data
        """
        if chart_type == "stacked_bar":
            return self._prepare_dual_category_data(df, chart_type, fields)
        elif chart_type == "stacked_line":
            return self._prepare_stacked_line_data(df, fields)
        elif chart_type == "stacked_area":
            return self._prepare_stacked_area_data(df, fields)
        else:
            logger.warning(f"Unsupported stacked chart type: {chart_type}")
            return None

    def _prepare_dual_category_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """Prepares data for dual category charts (stacked_bar, etc)."""
        x_field = fields.get("x")
        x_type = fields.get("x_type")
        group_field = fields.get("group")
        # group_type is used in frontend rendering
        _ = fields.get("group_type", "categorical")
        num_field = fields.get("numeric")
        agg_func = fields.get("agg", "sum")

        if not x_field or not x_type or not group_field:
            logger.warning(f"Skipping {chart_type}: Missing x, x_type, or group.")
            return None

        try:
            # Group by both x and group fields
            grouped = df.groupby([x_field, group_field])[num_field].agg(agg_func).reset_index()

            # Skip if all numeric values are the same
            if len(grouped[num_field].unique()) <= 1:
                logger.info(f"Skipping {chart_type}: No variation in {num_field} values after {agg_func} aggregation")
                return None

            if chart_type == "stacked_bar":
                # Pivot to create data suitable for stacked bar
                pivot_table = grouped.pivot(index=x_field, columns=group_field, values=num_field).fillna(0)

                # Additional check for variation within each group
                if all(len(pivot_table[col].unique()) <= 1 for col in pivot_table.columns):
                    logger.info(f"Skipping {chart_type}: No variation within any group")
                    return None

                # Check if all values are the same across all groups
                if len(pivot_table) > 1:
                    # Calculate sum for each x value across all groups
                    row_sums = pivot_table.sum(axis=1)
                    if row_sums.nunique() == 1:
                        logger.info(f"Skipping {chart_type}: All categories have the same total value ({row_sums.iloc[0]})")
                        return None

                # Create data for each group as a separate trace
                data = []
                for col in pivot_table.columns:
                    data.append({
                        "x": pivot_table.index.tolist(),
                        "y": pivot_table[col].tolist(),
                        "name": str(col),
                        "type": "bar"
                    })

                chart_data = {
                    "chart_type": chart_type,
                    "library": "plotly",
                    "data": data,
                    "layout": {
                        "barmode": "stack",
                        "title": fields.get("chart_title", f"{x_field.replace('_', ' ').title()} by {group_field.replace('_', ' ').title()}"),
                        "xaxis": {"title": fields.get("x_axis_title", x_field.replace('_', ' ').title())},
                        "yaxis": {"title": fields.get("y_axis_title", num_field if num_field else "Count")}
                    }
                }
                return self._ensure_serializable(chart_data)
            else:
                # Group data by the x_field
                pivot_data = grouped.pivot(index=x_field, columns=group_field, values=num_field).fillna(0)

                # Create data for each group as a separate trace
                data = []
                for col in pivot_data.columns:
                    data.append({
                        "x": pivot_data.index.tolist(),
                        "y": pivot_data[col].tolist(),
                        "name": str(col),
                        "type": "bar"
                    })

                chart_data = {
                    "chart_type": chart_type,
                    "library": "plotly",
                    "data": data,
                    "layout": {
                        "barmode": "group",
                        "title": fields.get("chart_title", f"{x_field.replace('_', ' ').title()} by {group_field.replace('_', ' ').title()}"),
                        "xaxis": {"title": fields.get("x_axis_title", x_field.replace('_', ' ').title())},
                        "yaxis": {"title": fields.get("y_axis_title", num_field.replace('_', ' ').title())}
                    }
                }
                return self._ensure_serializable(chart_data)

        except Exception as e:
            logger.error(f"Error preparing {chart_type} data: {str(e)}")
            return None

    def _prepare_stacked_line_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for stacked line charts (multiple stacked line series)."""
        x_field = fields.get("x")
        x_type = fields.get("x_type")
        group_field = fields.get("group")
        numeric_field = fields.get("numeric")
        time_agg = fields.get("time_agg", "monthly")

        if not x_field or not x_type or not group_field or not numeric_field:
            logger.warning(f"Skipping stacked_line: Missing required fields")
            return None

        try:
            # Handle datetime x-axis
            if x_type == "datetime":
                # Convert to datetime with proper error handling
                if not pd.api.types.is_datetime64_any_dtype(df[x_field]):
                    logger.info(f"Converting {x_field} to datetime for stacked_line chart")
                    df[x_field] = pd.to_datetime(df[x_field], errors='coerce')

                    # Check if conversion was successful
                    if df[x_field].isna().all():
                        logger.warning(f"Skipping stacked_line: Could not convert {x_field} to datetime")
                        return None

                    # Drop rows with NaT values
                    df = df.dropna(subset=[x_field])

                    if df.empty:
                        logger.warning(f"Skipping stacked_line: No valid datetime values after conversion")
                        return None

                # Create period for grouping with user-friendly formats using the common method
                df = self._format_datetime_columns(df, x_field, time_agg,
                                                sort_key_col='period_sort_key',
                                                display_col='period')

                # Double-check that quarterly formatting is correct
                if time_agg == "quarterly":
                    # Ensure period column has Q1, Q2, etc. format
                    df['period'] = 'Q' + df[x_field].dt.quarter.astype(str) + ' ' + df[x_field].dt.year.astype(str)

                # Group by period and group field, using sort key for chronological ordering
                grouped = df.groupby(['period_sort_key', 'period', group_field])[numeric_field].sum().reset_index()

                # Pivot the data to get group field as columns
                pivot_table = grouped.pivot(index=['period_sort_key', 'period'], columns=group_field, values=numeric_field).fillna(0)

                # Sort by the chronological key
                pivot_table = pivot_table.sort_index(level='period_sort_key')

                # Reset index to get period as a column for x values
                pivot_table = pivot_table.reset_index(level='period_sort_key', drop=True)

                # Get the formatted period values for x-axis
                x_values = pivot_table.index.tolist()

                # Check if we need to format the x-axis values for quarterly data
                if time_agg == "quarterly" and x_values and isinstance(x_values[0], str) and not x_values[0].startswith('Q'):
                    # Try to extract quarter and year from the period values if they're not already in Q# YYYY format
                    try:
                        # Convert to datetime first if they're not already in the right format
                        temp_df = pd.DataFrame({'date': x_values})
                        temp_df['date'] = pd.to_datetime(temp_df['date'], errors='coerce')
                        # Format as Q# YYYY
                        x_values = ['Q' + str(d.quarter) + ' ' + str(d.year) for d in temp_df['date'] if not pd.isna(d)]
                    except Exception as e:
                        logger.warning(f"Could not format quarterly x-axis values: {e}")
                        # Keep original values if formatting fails
            else:
                # For non-datetime x-axis
                # Group by x field and group field
                grouped = df.groupby([x_field, group_field])[numeric_field].sum().reset_index()

                # Pivot the data to get group field as columns
                pivot_table = grouped.pivot(index=x_field, columns=group_field, values=numeric_field).fillna(0)

                x_values = pivot_table.index.tolist()

            # Create traces for each group
            traces = []
            for col in pivot_table.columns:
                # Format the name to be more user-friendly if it's a datetime
                name = str(col)
                if isinstance(col, pd.Timestamp):
                    # Format datetime objects in the legend to match our quarterly format
                    quarter = col.quarter
                    year = col.year
                    name = f"Q{quarter} {year}"

                traces.append({
                    "x": x_values,  # These are already the formatted date strings from 'period' column
                    "y": pivot_table[col].tolist(),
                    "type": "scatter",
                    "mode": "lines",
                    "name": name,
                    "stackgroup": "one"  # This makes it a stacked line chart
                })

            if not traces:
                logger.warning(f"Skipping stacked_line: No valid groups found")
                return None

            return {
                "chart_type": "stacked_line",
                "library": "plotly",
                "data": traces,
                "layout": {
                    "title": fields.get("chart_title", f"Stacked {numeric_field.replace('_', ' ').title()} by {group_field.replace('_', ' ').title()}"),
                    "xaxis": {"title": fields.get("x_axis_title", "Date" if x_type == "datetime" else x_field.replace('_', ' ').title())},
                    "yaxis": {"title": fields.get("y_axis_title", numeric_field.replace('_', ' ').title())},
                    "legend": {"orientation": "h", "y": 1.1}
                }
            }

        except Exception as e:
            logger.error(f"Error preparing stacked line chart data: {str(e)}")
            return None

    def _prepare_stacked_area_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for stacked area charts (multiple stacked area series)."""
        x_field = fields.get("x")
        x_type = fields.get("x_type")
        group_field = fields.get("group")
        numeric_field = fields.get("numeric")
        time_agg = fields.get("time_agg", "monthly")

        if not x_field or not x_type or not group_field or not numeric_field:
            logger.warning(f"Skipping stacked_area: Missing required fields")
            return None

        try:
            # Handle datetime x-axis
            if x_type == "datetime":
                # Convert to datetime with proper error handling
                if not pd.api.types.is_datetime64_any_dtype(df[x_field]):
                    logger.info(f"Converting {x_field} to datetime for stacked_area chart")
                    df[x_field] = pd.to_datetime(df[x_field], errors='coerce')

                    # Check if conversion was successful
                    if df[x_field].isna().all():
                        logger.warning(f"Skipping stacked_area: Could not convert {x_field} to datetime")
                        return None

                    # Drop rows with NaT values
                    df = df.dropna(subset=[x_field])

                    if df.empty:
                        logger.warning(f"Skipping stacked_area: No valid datetime values after conversion")
                        return None

                # Create period for grouping with user-friendly formats using the common method
                df = self._format_datetime_columns(df, x_field, time_agg,
                                                sort_key_col='period_sort_key',
                                                display_col='period')

                # Double-check that quarterly formatting is correct
                if time_agg == "quarterly":
                    # Ensure period column has Q1, Q2, etc. format
                    df['period'] = 'Q' + df[x_field].dt.quarter.astype(str) + ' ' + df[x_field].dt.year.astype(str)

                # Group by period and group field, using sort key for chronological ordering
                grouped = df.groupby(['period_sort_key', 'period', group_field])[numeric_field].sum().reset_index()

                # Pivot the data to get group field as columns
                pivot_table = grouped.pivot(index=['period_sort_key', 'period'], columns=group_field, values=numeric_field).fillna(0)

                # Sort by the chronological key
                pivot_table = pivot_table.sort_index(level='period_sort_key')

                # Reset index to get period as a column for x values
                pivot_table = pivot_table.reset_index(level='period_sort_key', drop=True)

                # Get the formatted period values for x-axis
                x_values = pivot_table.index.tolist()

                # Check if we need to format the x-axis values for quarterly data
                if time_agg == "quarterly" and x_values and isinstance(x_values[0], str) and not x_values[0].startswith('Q'):
                    # Try to extract quarter and year from the period values if they're not already in Q# YYYY format
                    try:
                        # Convert to datetime first if they're not already in the right format
                        temp_df = pd.DataFrame({'date': x_values})
                        temp_df['date'] = pd.to_datetime(temp_df['date'], errors='coerce')
                        # Format as Q# YYYY
                        x_values = ['Q' + str(d.quarter) + ' ' + str(d.year) for d in temp_df['date'] if not pd.isna(d)]
                    except Exception as e:
                        logger.warning(f"Could not format quarterly x-axis values: {e}")
                        # Keep original values if formatting fails
            else:
                # For non-datetime x-axis
                # Group by x field and group field
                grouped = df.groupby([x_field, group_field])[numeric_field].sum().reset_index()

                # Pivot the data to get group field as columns
                pivot_table = grouped.pivot(index=x_field, columns=group_field, values=numeric_field).fillna(0)

                x_values = pivot_table.index.tolist()

            # Create traces for each group
            traces = []
            for col in pivot_table.columns:
                # Format the name to be more user-friendly if it's a datetime
                name = str(col)
                if isinstance(col, pd.Timestamp):
                    # Format datetime objects in the legend to match our quarterly format
                    quarter = col.quarter
                    year = col.year
                    name = f"Q{quarter} {year}"

                traces.append({
                    "x": x_values,  # These are already the formatted date strings from 'period' column
                    "y": pivot_table[col].tolist(),
                    "type": "scatter",
                    "mode": "lines",
                    "name": name,
                    "stackgroup": "one",  # This makes it a stacked area chart
                    "fill": "tonexty"     # This adds the area fill
                })

            if not traces:
                logger.warning(f"Skipping stacked_area: No valid groups found")
                return None

            return {
                "chart_type": "stacked_area",
                "library": "plotly",
                "data": traces,
                "layout": {
                    "title": fields.get("chart_title", f"Stacked Area: {numeric_field.replace('_', ' ').title()} by {group_field.replace('_', ' ').title()}"),
                    "xaxis": {"title": fields.get("x_axis_title", "Date" if x_type == "datetime" else x_field.replace('_', ' ').title())},
                    "yaxis": {"title": fields.get("y_axis_title", numeric_field.replace('_', ' ').title())},
                    "legend": {"orientation": "h", "y": 1.1}
                }
            }

        except Exception as e:
            logger.error(f"Error preparing stacked area chart data: {str(e)}")
            return None

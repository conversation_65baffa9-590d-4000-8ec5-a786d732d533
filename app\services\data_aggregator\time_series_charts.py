"""
Time series charts data aggregation module.

This module contains functions for preparing data for time series charts like line and area.
"""

import pandas as pd
import numpy as np
from app.core.logging_config import configure_logging
from app.services.data_aggregator.base import ChartAggregatorBase

logger = configure_logging('app.services.data_aggregator.time_series_charts')

class TimeSeriesChartAggregator(ChartAggregatorBase):
    """Class for time series chart data aggregation."""
    
    def prepare_chart_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """
        Prepare data for time series charts (line, area).
        
        Args:
            df: The pandas DataFrame containing the data
            chart_type: The type of chart to prepare data for
            fields: Dictionary of fields to use for the chart
            
        Returns:
            Dictionary containing the chart data
        """
        if chart_type == "line":
            return self._prepare_line_chart_data(df, fields)
        elif chart_type == "area":
            return self._prepare_area_chart_data(df, fields)
        else:
            logger.warning(f"Unsupported time series chart type: {chart_type}")
            return None
    
    def _prepare_line_chart_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for line charts."""
        try:
            x_field = fields.get("x")
            x_type = fields.get("x_type")
            numeric_field = fields.get("numeric")
            time_agg = fields.get("time_agg", "monthly")
            
            if not x_field or not x_type or not numeric_field:
                logger.warning("Skipping line chart: Missing required fields")
                return None
            
            if x_type == "datetime":
                # Convert to datetime with proper error handling
                if not pd.api.types.is_datetime64_any_dtype(df[x_field]):
                    logger.info(f"Converting {x_field} to datetime for line chart")
                    df[x_field] = pd.to_datetime(df[x_field], errors='coerce')

                    # Check if conversion was successful
                    if df[x_field].isna().all():
                        logger.warning(f"Skipping line chart: Could not convert {x_field} to datetime")
                        return None

                    # Drop rows with NaT values
                    df = df.dropna(subset=[x_field])

                    if df.empty:
                        logger.warning(f"Skipping line chart: No valid datetime values after conversion")
                        return None

                # Create period for grouping with user-friendly formats using the common method
                df = self._format_datetime_columns(df, x_field, time_agg,
                                                sort_key_col='period_sort_key',
                                                display_col='period')

                # Group by period and aggregate
                grouped = df.groupby(['period_sort_key', 'period'])[numeric_field].agg('mean').reset_index()
                grouped = grouped.sort_values('period_sort_key')  # Sort by the chronological key

                # Check for variation in Y-axis values - skip if no variation
                if len(grouped) > 1 and grouped[numeric_field].nunique() == 1:
                    logger.info(f"Skipping line chart: No variation in Y-axis ({numeric_field}) - all values are identical ({grouped[numeric_field].iloc[0]})")
                    return None

                # Rename period column back to x_field
                grouped = grouped.rename(columns={'period': x_field})

                chart_data = {
                    "chart_type": "line",
                    "library": "plotly",
                    "data": {
                        "x": grouped[x_field].tolist(),
                        "y": grouped[numeric_field].tolist(),
                        "type": "scatter",
                        "mode": "lines"
                    },
                    "layout": {
                        "title": fields.get("chart_title", f"{numeric_field.replace('_', ' ').title()} Over Time"),
                        "xaxis": {"title": fields.get("x_axis_title", "Date")},
                        "yaxis": {"title": fields.get("y_axis_title", numeric_field.replace('_', ' ').title())}
                    }
                }
                return self._ensure_serializable(chart_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Error preparing line chart data: {str(e)}")
            return None
    
    def _prepare_area_chart_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for area charts."""
        try:
            x_field = fields.get("x")
            x_type = fields.get("x_type")
            numeric_field = fields.get("numeric")
            time_agg = fields.get("time_agg", "monthly")
            
            if not x_field or not x_type or not numeric_field:
                logger.warning("Skipping area chart: Missing required fields")
                return None
            
            if x_type == "datetime":
                # Convert to datetime with proper error handling
                if not pd.api.types.is_datetime64_any_dtype(df[x_field]):
                    logger.info(f"Converting {x_field} to datetime for area chart")
                    df[x_field] = pd.to_datetime(df[x_field], errors='coerce')

                    # Check if conversion was successful
                    if df[x_field].isna().all():
                        logger.warning(f"Skipping area chart: Could not convert {x_field} to datetime")
                        return None

                    # Drop rows with NaT values
                    df = df.dropna(subset=[x_field])

                    if df.empty:
                        logger.warning(f"Skipping area chart: No valid datetime values after conversion")
                        return None

                # Create period for grouping with user-friendly formats using the common method
                df = self._format_datetime_columns(df, x_field, time_agg,
                                                sort_key_col='period_sort_key',
                                                display_col='period')

                # Group by period and aggregate
                grouped = df.groupby(['period_sort_key', 'period'])[numeric_field].agg('sum').reset_index()
                grouped = grouped.sort_values('period_sort_key')  # Sort by the chronological key

                # Check for variation in Y-axis values - skip if no variation
                if len(grouped) > 1 and grouped[numeric_field].nunique() == 1:
                    logger.info(f"Skipping area chart: No variation in Y-axis ({numeric_field}) - all values are identical ({grouped[numeric_field].iloc[0]})")
                    return None

                # Rename period column back to x_field
                grouped = grouped.rename(columns={'period': x_field})

                chart_data = {
                    "chart_type": "area",
                    "library": "plotly",
                    "data": {
                        "x": grouped[x_field].tolist(),
                        "y": grouped[numeric_field].tolist(),
                        "type": "scatter",
                        "mode": "lines",
                        "fill": "tozeroy"
                    },
                    "layout": {
                        "title": fields.get("chart_title", f"Cumulative {numeric_field.replace('_', ' ').title()}"),
                        "xaxis": {"title": fields.get("x_axis_title", "Date")},
                        "yaxis": {"title": fields.get("y_axis_title", f"Total {numeric_field.replace('_', ' ').title()}")}
                    }
                }
                return self._ensure_serializable(chart_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Error preparing area chart data: {str(e)}")
            return None
    
    def _prepare_time_series_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares time series data with proper aggregation."""
        try:
            x_field = fields.get("x")
            numeric_field = fields.get("numeric")
            chart_type = fields.get("chart_type", "line")
            time_agg = fields.get("time_agg", "monthly")
            agg_func = fields.get("agg", "mean")
            # date_format is used for formatting in the frontend
            _ = fields.get("date_format", "%b %Y")  # Default to user-friendly format

            if not all([x_field, numeric_field]):
                return None

            # Convert to datetime
            df[x_field] = pd.to_datetime(df[x_field])

            # Apply time aggregation with user-friendly formats using the common method
            df = self._format_datetime_columns(df, x_field, time_agg,
                                           sort_key_col='sort_key',
                                           display_col=x_field)  # Overwrite the original column with formatted dates

            # Group and aggregate
            grouped = df.groupby(['sort_key', x_field])[numeric_field].agg(agg_func).reset_index()
            grouped = grouped.sort_values('sort_key')  # Sort chronologically

            # Check for variation in Y-axis values - skip if no variation
            if len(grouped) > 1 and grouped[numeric_field].nunique() == 1:
                logger.info(f"Skipping {chart_type} chart: No variation in Y-axis ({numeric_field}) - all values are identical ({grouped[numeric_field].iloc[0]})")
                return None

            return {
                "chart_type": chart_type,
                "library": "plotly",
                "data": {
                    "x": grouped[x_field].tolist(),
                    "y": grouped[numeric_field].tolist(),
                    "type": "scatter",
                    "mode": "lines"
                },
                "layout": {
                    "title": fields.get("chart_title", f"{numeric_field.replace('_', ' ').title()} Over Time"),
                    "xaxis": {"title": fields.get("x_axis_title", "Date")},
                    "yaxis": {"title": fields.get("y_axis_title", numeric_field.replace('_', ' ').title())}
                }
            }

        except Exception as e:
            logger.error(f"Error preparing time series data: {str(e)}")
            return None

"""
Timeline charts data aggregation module.
"""

import pandas as pd
import numpy as np
from app.core.logging_config import configure_logging
from app.services.data_aggregator.base import ChartAggregatorBase

logger = configure_logging('app.services.data_aggregator.timeline_charts')

class TimelineChartAggregator(ChartAggregatorBase):
    """Class for timeline chart data aggregation."""
    
    def prepare_chart_data(self, df: pd.DataFrame, chart_type: str, fields: dict) -> dict:
        """
        Prepare data for timeline charts.
        
        Args:
            df: The pandas DataFrame containing the data
            chart_type: The type of chart to prepare data for
            fields: Dictionary of fields to use for the chart
            
        Returns:
            Dictionary containing the chart data
        """
        if chart_type == "timeline":
            return self._prepare_timeline_data(df, fields)
        else:
            logger.warning(f"Unsupported timeline chart type: {chart_type}")
            return None
    
    def _prepare_timeline_data(self, df: pd.DataFrame, fields: dict) -> dict:
        """Prepares data for timeline charts (event-based visualization)."""
        x_field = fields.get("x")
        x_type = fields.get("x_type")
        group_field = fields.get("group")
        time_agg = fields.get("time_agg", "daily")

        if not x_field or x_type != "datetime":
            logger.warning(f"Skipping Timeline: Missing datetime field")
            return None

        try:
            # Ensure x_field is properly converted to datetime before using .dt accessor
            if not pd.api.types.is_datetime64_any_dtype(df[x_field]):
                logger.warning(f"Converting {x_field} to datetime before applying time aggregation")
                df[x_field] = pd.to_datetime(df[x_field], errors='coerce')

                # Check if conversion was successful
                if df[x_field].isna().all():
                    logger.warning(f"Skipping Timeline: Could not convert {x_field} to datetime for time aggregation")
                    return None

                # Drop rows with NaT values
                df = df.dropna(subset=[x_field])

                if df.empty:
                    logger.warning(f"Skipping Timeline: No valid datetime values after conversion for time aggregation")
                    return None

            # Apply time aggregation with user-friendly formats using the common method
            df = self._format_datetime_columns(df, x_field, time_agg,
                                            sort_key_col='date_sort_key',
                                            display_col='date_group')

            # Group by date and group field if provided
            if group_field and group_field in df.columns:
                # Group by date and category, using sort key for chronological ordering
                grouped = df.groupby(['date_sort_key', 'date_group', group_field]).size().reset_index(name='count')
                grouped = grouped.sort_values('date_sort_key')  # Sort chronologically

                # Create timeline data with categories
                timeline_data = []
                for group_name, group_df in grouped.groupby(group_field):
                    timeline_data.append({
                        "x": group_df['date_group'].tolist(),
                        "y": [str(group_name)] * len(group_df),
                        "mode": "markers",
                        "type": "scatter",
                        "name": str(group_name),
                        "marker": {
                            "size": group_df['count'].tolist(),
                            "sizemode": "area",
                            "sizeref": 0.1,
                            "sizemin": 4
                        }
                    })

                return {
                    "chart_type": "timeline",
                    "library": "plotly",
                    "data": timeline_data,
                    "layout": {
                        "title": fields.get("chart_title", f"Timeline of Events"),
                        "xaxis": {"title": fields.get("x_axis_title", "Date")},
                        "yaxis": {"title": fields.get("y_axis_title", "Category"), "type": "category"},
                        "height": 400,
                        "margin": {"l": 100}
                    }
                }
            else:
                # Simple timeline without categories
                grouped = df.groupby(['date_sort_key', 'date_group']).size().reset_index(name='count')
                grouped = grouped.sort_values('date_sort_key')  # Sort chronologically

                return {
                    "chart_type": "timeline",
                    "library": "plotly",
                    "data": {
                        "x": grouped['date_group'].tolist(),
                        "y": grouped['count'].tolist(),
                        "type": "bar",
                        "marker": {"color": "rgba(50, 171, 96, 0.7)"}
                    },
                    "layout": {
                        "title": fields.get("chart_title", f"Timeline of Events"),
                        "xaxis": {"title": fields.get("x_axis_title", "Date")},
                        "yaxis": {"title": fields.get("y_axis_title", "Count")}
                    }
                }

        except Exception as e:
            logger.error(f"Error preparing timeline data: {str(e)}")
            return None

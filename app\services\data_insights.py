import pandas as pd
import numpy as np
import json
from typing import Dict, List
from app.core.logging_config import configure_logging
from app.core.config import Settings

logger = configure_logging()
settings = Settings()

class DataInsights:
    """
    Data Insights service for generating insights and patterns from data.
    Provides methods to analyze data and extract meaningful insights.
    """


    def generate_data_insights(self, aggregated_data: dict) -> List[str]:
        """
        Generate insights based on exploratory data analysis of aggregated data.

        Args:
            aggregated_data (dict): Dictionary containing aggregated data with metrics

        Returns:
            List[str]: List of 5-10 insights derived from the data
        """
        insights = []
        try:
            # Log the aggregated data structure
            logger.info(f"Generating insights from data with keys: {list(aggregated_data.keys())}")

            # Check if data is present and valid
            data = aggregated_data.get('data', [])
            if not data:
                logger.warning("No data provided for insights generation")
                return ["Insufficient data for analysis"]

            # Convert data to pandas DataFrame for analysis
            df = pd.DataFrame(data)
            logger.info(f"Created DataFrame with shape: {df.shape}, columns: {list(df.columns)}")

            if df.empty:
                logger.warning("DataFrame is empty after conversion")
                return ["Insufficient data for analysis"]

            # 1. Basic Statistics and Trends
            for col in df.select_dtypes(include=[np.number]).columns:
                mean_val = df[col].mean()
                median_val = df[col].median()

                # Check for skewness
                if abs(mean_val - median_val) > (df[col].std() * 0.5):
                    direction = "positively" if mean_val > median_val else "negatively"
                    insights.append(
                        f"The distribution of {col} is {direction} skewed, with mean "
                        f"{mean_val:.2f} and median {median_val:.2f}"
                    )

            # 2. Time Series Analysis (if datetime column exists)
            date_cols = df.select_dtypes(include=['datetime64']).columns
            for date_col in date_cols:
                # Identify trends
                df['month'] = df[date_col].dt.to_period('M')
                monthly_counts = df.groupby('month').size()

                if len(monthly_counts) > 1:
                    growth_rate = ((monthly_counts.iloc[-1] / monthly_counts.iloc[0]) - 1) * 100

                    # Format dates in user-friendly format
                    start_date = monthly_counts.index[0].start_time
                    end_date = monthly_counts.index[-1].start_time
                    start_date_str = start_date.strftime("%b %Y")  # "Jan 2024"
                    end_date_str = end_date.strftime("%b %Y")  # "Jan 2024"

                    insights.append(
                        f"Overall {'growth' if growth_rate > 0 else 'decline'} of "
                        f"{abs(growth_rate):.1f}% observed from {start_date_str} "
                        f"to {end_date_str}"
                    )

            # 3. Correlation Analysis
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) >= 2:
                corr_matrix = df[numeric_cols].corr()
                strong_correlations = []

                for i in range(len(numeric_cols)):
                    for j in range(i + 1, len(numeric_cols)):
                        corr = corr_matrix.iloc[i, j]
                        if abs(corr) > 0.7:
                            strong_correlations.append(
                                f"Strong {'positive' if corr > 0 else 'negative'} correlation "
                                f"({corr:.2f}) between {numeric_cols[i]} and {numeric_cols[j]}"
                            )

                if strong_correlations:
                    insights.extend(strong_correlations[:2])  # Add top 2 correlations

            # 4. Categorical Analysis
            cat_cols = df.select_dtypes(include=['object']).columns
            for col in cat_cols:
                value_counts = df[col].value_counts()
                if not value_counts.empty:
                    top_category = value_counts.index[0]
                    percentage = (value_counts.iloc[0] / len(df)) * 100
                    insights.append(
                        f"'{top_category}' is the most frequent category in {col}, "
                        f"representing {percentage:.1f}% of the data"
                    )

            # 5. Outlier Detection
            for col in numeric_cols:
                q1 = df[col].quantile(0.25)
                q3 = df[col].quantile(0.75)
                iqr = q3 - q1
                outliers = df[(df[col] < q1 - 1.5 * iqr) | (df[col] > q3 + 1.5 * iqr)]

                if len(outliers) > 0:
                    outlier_percentage = (len(outliers) / len(df)) * 100
                    insights.append(
                        f"Detected {len(outliers)} outliers ({outlier_percentage:.1f}%) "
                        f"in {col}"
                    )

            # 6. Missing Value Analysis
            missing_data = df.isnull().sum()
            significant_missing = missing_data[missing_data > 0]
            if not significant_missing.empty:
                for col, count in significant_missing.items():
                    percentage = (count / len(df)) * 100
                    if percentage > 5:  # Only report if more than 5% missing
                        insights.append(
                            f"Missing data in {col}: {percentage:.1f}% of values are missing"
                        )

            # Limit to top 10 most significant insights
            insights = insights[:10]
            logger.info(f"Generated {len(insights)} insights from data analysis")

            # If we have too few insights, add some general statistics
            if len(insights) < 5:
                logger.info("Adding general statistics to supplement insights")
                for col in numeric_cols:
                    if len(insights) >= 10:
                        break
                    try:
                        mean_val = df[col].mean()
                        min_val = df[col].min()
                        max_val = df[col].max()
                        insights.append(
                            f"Average {col}: {mean_val:.2f}, "
                            f"Range: {min_val:.2f} to {max_val:.2f}"
                        )
                    except Exception as e:
                        logger.warning(f"Error calculating statistics for column {col}: {e}")

            final_insights = insights if insights else ["No significant patterns found in the data"]
            logger.info(f"Final insights: {final_insights}")
            return final_insights

        except Exception as e:
            import traceback
            error_trace = traceback.format_exc()
            logger.error(f"Error generating data insights: {e}")
            logger.error(f"Traceback: {error_trace}")

            # Check if the error is related to data structure
            if 'data' in str(e).lower() or 'dataframe' in str(e).lower() or 'column' in str(e).lower():
                return ["Insufficient or invalid data for analysis"]
            else:
                return ["Error occurred while analyzing the data"]

    def generate_executive_summary(self, aggregated_data: dict) -> List[str]:
        """
        Generate an executive summary of the data using LLM.

        Args:
            aggregated_data (dict): Dictionary containing aggregated data with metrics

        Returns:
            List[str]: List of 5-6 executive summary points
        """
        try:
            # Log the aggregated data structure in more detail
            logger.info(f"Generating executive summary from data with keys: {list(aggregated_data.keys())}")
            logger.info(f"Aggregated data type: {type(aggregated_data)}")

            # More detailed logging of the input data
            if not isinstance(aggregated_data, dict):
                logger.error(f"Expected dict for aggregated_data, got {type(aggregated_data)}")
                return ["Insufficient data for executive summary: Invalid data format"]

            # Check if data is present and valid
            if 'data' not in aggregated_data:
                logger.error("'data' key missing in aggregated_data")
                logger.info(f"Available keys: {list(aggregated_data.keys())}")
                return ["Insufficient data for executive summary: Missing data field"]

            data = aggregated_data.get('data', [])
            logger.info(f"Data type: {type(data)}, length: {len(data) if hasattr(data, '__len__') else 'unknown'}")

            if not data:
                logger.warning("Empty data array provided for executive summary generation")
                return ["Insufficient data for executive summary: Empty dataset"]

            # Log a sample of the data to understand its structure
            if isinstance(data, list) and len(data) > 0:
                logger.info(f"First data item type: {type(data[0])}")
                logger.info(f"First data item sample: {str(data[0])[:200]}...")
            else:
                logger.warning(f"Data is not a list or is empty: {type(data)}")
                return ["Insufficient data for executive summary: Invalid data structure"]

            # Prepare the data for the LLM
            # Convert to a more readable format for the LLM
            data_sample = data[:50]  # Limit to first 50 records to avoid token limits

            # Extract metadata if available
            metadata = aggregated_data.get('metadata', {})
            chart_title = metadata.get('title', 'Chart')
            x_axis_label = metadata.get('x_axis', 'Category')
            y_axis_label = metadata.get('y_axis', 'Value')

            # Create a prompt for the LLM
            prompt = f"""
            Generate an executive summary of the following data from a {chart_title} chart.
            The x-axis represents {x_axis_label} and the y-axis represents {y_axis_label}.

            The summary should:
            - Consist of 5-6 concise, actionable bullet points
            - Highlight key trends, patterns, and insights
            - Focus on business implications and recommendations
            - Be written in a professional, executive-friendly tone

            ---
            ### Chart Information:
            - Title: {chart_title}
            - X-Axis: {x_axis_label}
            - Y-Axis: {y_axis_label}

            ### Data Sample:
            {json.dumps(data_sample, indent=2)}

            ---
            ### Expected JSON Output:
            Strictly return only a valid JSON object in this format:

            ```json
            {{
                "summary_points": [
                    "Point 1: Key insight about the {x_axis_label} data with business implications.",
                    "Point 2: Another important trend or pattern observed in the {y_axis_label} values.",
                    "Point 3: Recommendation based on the {chart_title} analysis.",
                    "Point 4: Notable metric or KPI with context.",
                    "Point 5: Strategic insight derived from the data."
                ]
            }}
            ```

            🚨 **Important:**
            - Ensure the output is in valid JSON format
            - Each point should be a complete sentence or two
            - Focus on actionable insights rather than just describing the data
            - Do NOT add explanations outside the JSON structure
            """

            # Define the expected response format
            response_format = {
                "type": "object",
                "properties": {
                    "summary_points": {
                        "type": "array",
                        "items": {"type": "string"}
                    }
                }
            }

            # Generate the executive summary using the LLM
            logger.info("Calling LLM to generate executive summary")
            result = self.client.generate(prompt, response_format)
            logger.info(f"LLM response received, type: {type(result)}, length: {len(result) if isinstance(result, str) else 'unknown'}")

            # Log a sample of the response for debugging
            if isinstance(result, str):
                logger.info(f"LLM response sample: {result[:200]}...")
            else:
                logger.error(f"Unexpected LLM response type: {type(result)}")
                return ["Error generating executive summary: Unexpected response type"]

            try:
                # Parse the response
                logger.info("Parsing LLM response as JSON")
                parsed_result = json.loads(result)
                logger.info(f"Parsed result keys: {list(parsed_result.keys())}")

                # Extract the summary points
                if "response" in parsed_result:
                    logger.info(f"Response field keys: {list(parsed_result['response'].keys())}")

                    if "summary_points" in parsed_result["response"]:
                        summary_points = parsed_result["response"]["summary_points"]
                        logger.info(f"Generated {len(summary_points)} executive summary points")
                        logger.info(f"Summary points: {summary_points}")
                        return summary_points
                    else:
                        logger.error("LLM response missing 'summary_points' field")
                        logger.info(f"Available fields in response: {list(parsed_result['response'].keys())}")
                        return ["Unable to generate executive summary: Missing summary points"]
                else:
                    logger.error("LLM response missing 'response' field")
                    logger.info(f"Available fields: {list(parsed_result.keys())}")
                    return ["Unable to generate executive summary: Invalid response structure"]

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON from LLM response: {e}")
                logger.error(f"Raw response: {result}")
                return ["Error generating executive summary: Invalid JSON response"]

        except Exception as e:
            import traceback
            error_trace = traceback.format_exc()
            logger.error(f"Error generating executive summary: {e}")
            logger.error(f"Traceback: {error_trace}")

            # Provide more specific error messages based on the exception type
            if "LLMClient" in str(e) or "mistral" in str(e).lower():
                logger.error("LLM client error detected")
                return ["Error generating executive summary: LLM service unavailable"]
            elif "json" in str(e).lower():
                logger.error("JSON parsing error detected")
                return ["Error generating executive summary: Invalid response format"]
            elif "data" in str(e).lower():
                logger.error("Data processing error detected")
                return ["Error generating executive summary: Data processing error"]
            else:
                return [f"Error occurred while generating executive summary: {str(e)}"]

import pandas as pd
import io
from fastapi import UploadFile
from typing import Dict, List, Optional
from app.core.exceptions import FileUploadError
from app.core.logging_config import configure_logging
import os

logger = configure_logging()

class DataLoader:
    """
    Data Loader service for handling various data loading operations.
    Provides methods to load data from different sources like CSV files.
    """

    def __init__(self):
        # Common numeric columns for type inference
        self.numeric_columns = ['quantity', 'price', 'amount', 'cost',
                              'value', 'number', 'num', 'hours', 'stock']

    async def load_csv_data(self, file: UploadFile) -> pd.DataFrame:
        """
        Load data from an uploaded CSV or Excel file.
        Handles file validation and initial loading with proper type inference.

        Args:
            file: The uploaded CSV or Excel file

        Returns:
            pandas DataFrame with the loaded data

        Raises:
            FileUploadError: If the file is not a CSV/Excel or cannot be read
        """
        # Get file extension
        filename = file.filename.lower() if file.filename else ""
        file_ext = os.path.splitext(filename)[1]

        # Validate file type
        supported_extensions = ['.csv', '.xlsx', '.xls']
        if file_ext not in supported_extensions:
            raise FileUploadError(f"Only CSV or Excel files are supported. Supported formats: {', '.join(supported_extensions)}")

        try:
            contents = await file.read()

            # Handle CSV files
            if file_ext == '.csv':
                logger.debug(f"Loading CSV file: {filename}")
                # Enhanced CSV loading with proper delimiter detection
                return self._load_csv_with_header_detection(contents, filename)

            # Handle Excel files (.xlsx, .xls)
            elif file_ext in ['.xlsx', '.xls']:
                logger.debug(f"Loading Excel file: {filename}")

                # First, detect the actual file format
                actual_format = self._detect_file_format(contents, filename)
                logger.debug(f"Detected file format: {actual_format} for file: {filename}")

                if actual_format == 'html':
                    # Try to parse as HTML and extract table data
                    return self._parse_html_as_excel(contents, filename)
                elif actual_format == 'xml':
                    # Try to parse as XML
                    return self._parse_xml_as_excel(contents, filename)
                elif actual_format == 'csv':
                    # Parse as CSV/TSV
                    return self._parse_csv_like_content(contents, filename)
                elif actual_format in ['excel', 'unknown']:
                    # Try standard Excel parsing
                    return self._parse_excel_file(contents, filename, file_ext)
                else:
                    raise FileUploadError(f"Unsupported file format detected: {actual_format}")

        except UnicodeDecodeError as e:
            logger.error(f"Unicode decode error for file {filename}: {str(e)}")
            raise FileUploadError(f"Error decoding file. Please ensure the file is in UTF-8 encoding: {str(e)}")
        except Exception as e:
            logger.error(f"Error reading file {filename}: {str(e)}")
            raise FileUploadError(f"Error reading {file_ext.upper()} file: {str(e)}")

    async def load_excel_data(self, file: UploadFile, sheet_name: Optional[str] = None) -> pd.DataFrame:
        """
        Load Excel data from an uploaded file with optional sheet selection.

        Args:
            file: The uploaded Excel file (.xlsx or .xls)
            sheet_name: Optional sheet name to load. If None, loads the first sheet.

        Returns:
            pandas DataFrame with the loaded data

        Raises:
            FileUploadError: If the file is not an Excel file or cannot be read
        """
        filename = file.filename.lower() if file.filename else ""
        file_ext = os.path.splitext(filename)[1]

        if file_ext not in ['.xlsx', '.xls']:
            raise FileUploadError("Only Excel files (.xlsx, .xls) are supported for this method")

        try:
            contents = await file.read()
            excel_data = io.BytesIO(contents)

            # Get available sheets
            excel_file = pd.ExcelFile(excel_data)
            available_sheets = excel_file.sheet_names
            logger.debug(f"Available sheets in Excel file: {available_sheets}")

            # Determine which sheet to load
            target_sheet = sheet_name if sheet_name and sheet_name in available_sheets else 0

            if sheet_name and sheet_name not in available_sheets:
                logger.warning(f"Requested sheet '{sheet_name}' not found. Using first sheet: '{available_sheets[0]}'")

            # Load the data
            # Read with header=None to let our sanitizer handle header detection
            df = pd.read_excel(
                excel_data,
                sheet_name=target_sheet,
                header=None  # Don't assume header location, let sanitizer handle it
            )

            logger.info(f"Successfully loaded Excel sheet: {available_sheets[target_sheet] if isinstance(target_sheet, int) else target_sheet}")
            return df

        except Exception as e:
            logger.error(f"Error reading Excel file {filename}: {str(e)}")
            raise FileUploadError(f"Error reading Excel file: {str(e)}")

    def get_supported_file_types(self) -> List[str]:
        """
        Get list of supported file types.

        Returns:
            List of supported file extensions
        """
        return ['.csv', '.xlsx', '.xls']

    async def get_excel_sheet_names(self, file: UploadFile) -> List[str]:
        """
        Get list of sheet names from an Excel file.

        Args:
            file: The uploaded Excel file

        Returns:
            List of sheet names

        Raises:
            FileUploadError: If the file is not an Excel file or cannot be read
        """
        filename = file.filename.lower() if file.filename else ""
        file_ext = os.path.splitext(filename)[1]

        if file_ext not in ['.xlsx', '.xls']:
            raise FileUploadError("Only Excel files (.xlsx, .xls) are supported for sheet name extraction")

        try:
            contents = await file.read()
            excel_data = io.BytesIO(contents)
            excel_file = pd.ExcelFile(excel_data)
            return excel_file.sheet_names
        except Exception as e:
            logger.error(f"Error reading Excel file sheet names from {filename}: {str(e)}")
            raise FileUploadError(f"Error reading Excel file sheet names: {str(e)}")

    def _detect_file_format(self, contents: bytes, filename: str) -> str:
        """
        Detect the actual file format based on file content.

        Args:
            contents: File contents as bytes
            filename: Original filename

        Returns:
            Detected format: 'excel', 'html', 'xml', 'csv', or 'unknown'
        """
        try:
            original_contents = contents

            # Remove multiple BOMs if present (some files have duplicate BOMs)
            while contents.startswith(b'\xef\xbb\xbf'):
                contents = contents[3:]
                logger.debug("Removed UTF-8 BOM from file")

            # Get a larger sample to check file signature and content
            header = contents[:500].lower()

            # Check for XML content first (before HTML since HTML can contain XML)
            # But also check if it's actually delimited data with XML header
            if (header.startswith(b'<?xml') or
                header.startswith(b'<?') or  # Generic XML processing instruction
                b'<xml' in header or
                (b'<?' in header and b'>' in header)):  # XML processing instruction pattern

                # Check if this is actually delimited data with XML wrapper
                try:
                    text_content = contents.decode('utf-8', errors='ignore')
                    # If we find significant tab or comma separation, treat as delimited data
                    if '\t' in text_content or ',' in text_content:
                        lines = text_content.split('\n')
                        data_lines = [line for line in lines if line.strip() and not line.strip().startswith('<')]
                        if len(data_lines) > 2:
                            # Check if data lines have consistent delimiters
                            tab_counts = [line.count('\t') for line in data_lines[:5]]
                            comma_counts = [line.count(',') for line in data_lines[:5]]
                            if (tab_counts and max(tab_counts) > 2) or (comma_counts and max(comma_counts) > 2):
                                return 'csv'  # Treat as delimited data
                except:
                    pass

                return 'xml'

            # Check for HTML content
            if any(tag in header for tag in [b'<html', b'<!doctype', b'<table', b'<tr>', b'<td>', b'<th>', b'<body>']):
                return 'html'

            # Check for Excel file signatures (use original contents with BOM)
            # XLSX files start with PK (ZIP signature)
            if original_contents.startswith(b'PK\x03\x04'):
                return 'excel'

            # XLS files have specific signatures
            if original_contents.startswith(b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'):  # OLE2 signature
                return 'excel'

            # Additional check for tab-separated values (common in financial data exports)
            try:
                text_content = contents.decode('utf-8', errors='ignore')[:2000]

                # Check for tab-separated content
                if '\t' in text_content and '\n' in text_content:
                    tab_count = text_content.count('\t')
                    line_count = text_content.count('\n')
                    if tab_count > line_count:  # More tabs than lines suggests TSV
                        return 'csv'  # Treat TSV as CSV for processing

                # Check for comma-separated content
                if ',' in text_content and '\n' in text_content:
                    comma_count = text_content.count(',')
                    line_count = text_content.count('\n')
                    if comma_count > line_count:  # More commas than lines suggests CSV
                        return 'csv'

                # Check if it looks like structured data with HTML-like tags
                if '<' in text_content and '>' in text_content:
                    # Count tag-like patterns
                    import re
                    tag_pattern = r'<[^>]+>'
                    tags = re.findall(tag_pattern, text_content[:1000])
                    if len(tags) > 5:  # Multiple tags suggest HTML/XML
                        if any(html_tag in text_content.lower() for html_tag in ['<table', '<tr>', '<td>', '<th>']):
                            return 'html'
                        else:
                            return 'xml'

            except Exception as decode_error:
                logger.debug(f"Error decoding content for format detection: {str(decode_error)}")
                pass

            return 'unknown'

        except Exception as e:
            logger.warning(f"Error detecting file format for {filename}: {str(e)}")
            return 'unknown'

    def _parse_excel_file(self, contents: bytes, filename: str, file_ext: str) -> pd.DataFrame:
        """
        Parse a standard Excel file.

        Args:
            contents: File contents as bytes
            filename: Original filename
            file_ext: File extension

        Returns:
            pandas DataFrame
        """
        excel_data = io.BytesIO(contents)

        # Determine the appropriate engine
        engine = 'openpyxl' if file_ext == '.xlsx' else 'xlrd'

        try:
            excel_file = pd.ExcelFile(excel_data, engine=engine)
            sheet_names = excel_file.sheet_names
            logger.debug(f"Excel file has {len(sheet_names)} sheet(s): {sheet_names}")

            # Use the first sheet by default
            df = pd.read_excel(
                excel_data,
                sheet_name=0,
                engine=engine,
                header=None
            )

            if len(sheet_names) > 1:
                logger.info(f"Excel file contains multiple sheets. Using first sheet: '{sheet_names[0]}'")

            logger.info(f"Successfully loaded Excel file: {filename} (shape: {df.shape})")
            return df

        except ImportError as import_error:
            logger.error(f"Missing Excel engine dependencies: {str(import_error)}")
            raise FileUploadError(f"Excel support not available. Please install required dependencies: {str(import_error)}")
        except Exception as excel_error:
            logger.error(f"Error reading Excel file with engine {engine}: {str(excel_error)}")
            # Fallback: try other engines
            return self._try_excel_fallbacks(excel_data, filename)

    def _try_excel_fallbacks(self, excel_data: io.BytesIO, filename: str) -> pd.DataFrame:
        """
        Try different Excel parsing methods as fallbacks.

        Args:
            excel_data: Excel data as BytesIO
            filename: Original filename

        Returns:
            pandas DataFrame
        """
        fallback_engines = ['openpyxl', 'xlrd', None]

        for engine in fallback_engines:
            try:
                excel_data.seek(0)
                if engine:
                    df = pd.read_excel(excel_data, engine=engine, header=None)
                else:
                    df = pd.read_excel(excel_data, header=None)
                logger.info(f"Successfully loaded Excel file with {engine or 'default'} engine: {filename}")
                return df
            except Exception as e:
                logger.debug(f"Failed to read with {engine or 'default'} engine: {str(e)}")
                continue

        raise FileUploadError(f"Unable to read Excel file with any available engine")

    def _parse_html_as_excel(self, contents: bytes, filename: str) -> pd.DataFrame:
        """
        Parse HTML content and extract table data.

        Args:
            contents: File contents as bytes
            filename: Original filename

        Returns:
            pandas DataFrame
        """
        try:
            # Try to decode the content
            if contents.startswith(b'\xef\xbb\xbf'):
                contents = contents[3:]  # Remove BOM

            # Try different encodings
            for encoding in ['utf-8', 'latin-1', 'cp1252']:
                try:
                    html_content = contents.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise UnicodeDecodeError("Unable to decode HTML content")

            # First, try to detect if this is actually tab/comma-separated data with minimal HTML
            if self._looks_like_delimited_data(html_content):
                logger.info("HTML content appears to be delimited data, trying CSV/TSV parsing")
                try:
                    return self._parse_delimited_from_html(html_content, filename)
                except Exception as e:
                    logger.warning(f"Delimited parsing failed: {str(e)}, trying HTML table parsing")

            # Try pandas HTML parsing
            try:
                tables = pd.read_html(io.StringIO(html_content), header=None)
                if tables:
                    # Use the largest table (most likely to contain the main data)
                    df = max(tables, key=len)
                    logger.info(f"Successfully parsed HTML file as table: {filename} (shape: {df.shape})")
                    return df
            except ImportError:
                logger.warning("lxml not available, falling back to manual HTML parsing")
                # Fallback to manual HTML table parsing
                return self._parse_html_manually(html_content, filename)
            except Exception as e:
                logger.warning(f"pandas HTML parsing failed: {str(e)}, trying manual parsing")
                return self._parse_html_manually(html_content, filename)

            raise ValueError("No tables found in HTML content")

        except Exception as e:
            logger.error(f"Error parsing HTML file {filename}: {str(e)}")
            raise FileUploadError(f"Unable to parse HTML file as Excel: {str(e)}")

    def _parse_html_manually(self, html_content: str, filename: str) -> pd.DataFrame:
        """
        Manually parse HTML content to extract table data without requiring lxml.

        Args:
            html_content: HTML content as string
            filename: Original filename

        Returns:
            pandas DataFrame
        """
        import re

        try:
            # Find all table elements
            table_pattern = r'<table[^>]*>(.*?)</table>'
            tables = re.findall(table_pattern, html_content, re.DOTALL | re.IGNORECASE)

            if not tables:
                raise ValueError("No tables found in HTML content")

            # Process each table and find the one with the most data
            best_table = None
            max_rows = 0

            for table_content in tables:
                # Extract rows
                row_pattern = r'<tr[^>]*>(.*?)</tr>'
                rows = re.findall(row_pattern, table_content, re.DOTALL | re.IGNORECASE)

                if len(rows) > max_rows:
                    max_rows = len(rows)
                    best_table = rows

            if not best_table:
                raise ValueError("No table rows found")

            # Parse the best table
            data = []
            for row in best_table:
                # Extract cells (both td and th)
                cell_pattern = r'<(?:td|th)[^>]*>(.*?)</(?:td|th)>'
                cells = re.findall(cell_pattern, row, re.DOTALL | re.IGNORECASE)

                # Clean cell content (remove HTML tags and decode entities)
                cleaned_cells = []
                for cell in cells:
                    # Remove HTML tags
                    clean_cell = re.sub(r'<[^>]+>', '', cell)
                    # Decode common HTML entities
                    clean_cell = clean_cell.replace('&nbsp;', ' ')
                    clean_cell = clean_cell.replace('&amp;', '&')
                    clean_cell = clean_cell.replace('&lt;', '<')
                    clean_cell = clean_cell.replace('&gt;', '>')
                    clean_cell = clean_cell.replace('&quot;', '"')
                    # Strip whitespace
                    clean_cell = clean_cell.strip()
                    cleaned_cells.append(clean_cell)

                if cleaned_cells:  # Only add non-empty rows
                    data.append(cleaned_cells)

            if not data:
                raise ValueError("No data extracted from HTML table")

            # Create DataFrame
            df = pd.DataFrame(data)
            logger.info(f"Successfully parsed HTML file manually: {filename} (shape: {df.shape})")
            return df

        except Exception as e:
            logger.error(f"Manual HTML parsing failed for {filename}: {str(e)}")
            raise ValueError(f"Unable to parse HTML table: {str(e)}")

    def _parse_xml_as_excel(self, contents: bytes, filename: str) -> pd.DataFrame:
        """
        Parse XML content and extract data.

        Args:
            contents: File contents as bytes
            filename: Original filename

        Returns:
            pandas DataFrame
        """
        try:
            # Remove BOM if present
            if contents.startswith(b'\xef\xbb\xbf'):
                contents = contents[3:]

            # Try to decode the content
            for encoding in ['utf-8', 'latin-1', 'cp1252']:
                try:
                    xml_content = contents.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise UnicodeDecodeError("Unable to decode XML content")

            # Try to parse as XML and convert to DataFrame
            import xml.etree.ElementTree as ET

            root = ET.fromstring(xml_content)

            # Look for table-like structures
            data = []
            headers = []

            # Try to find repeating elements that could be rows
            for child in root:
                if len(list(child)) > 0:  # Has sub-elements
                    row_data = {}
                    for subchild in child:
                        if subchild.text:
                            row_data[subchild.tag] = subchild.text
                            if subchild.tag not in headers:
                                headers.append(subchild.tag)
                    if row_data:
                        data.append(row_data)

            if not data:
                raise ValueError("No tabular data found in XML")

            df = pd.DataFrame(data, columns=headers)
            logger.info(f"Successfully parsed XML file: {filename} (shape: {df.shape})")
            return df

        except Exception as e:
            logger.error(f"Error parsing XML file {filename}: {str(e)}")
            raise FileUploadError(f"Unable to parse XML file as Excel: {str(e)}")

    def _parse_csv_like_content(self, contents: bytes, filename: str) -> pd.DataFrame:
        """
        Parse CSV/TSV-like content that might be disguised as Excel.

        Args:
            contents: File contents as bytes
            filename: Original filename

        Returns:
            pandas DataFrame
        """
        try:
            # Remove BOM if present
            while contents.startswith(b'\xef\xbb\xbf'):
                contents = contents[3:]

            # Try to decode the content
            for encoding in ['utf-8', 'latin-1', 'cp1252']:
                try:
                    text_content = contents.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise UnicodeDecodeError("Unable to decode content")

            # Determine the separator
            separator = None

            # Check for tab separation first (common in financial data)
            if '\t' in text_content:
                tab_count = text_content.count('\t')
                comma_count = text_content.count(',')
                if tab_count > comma_count:
                    separator = '\t'

            # Fall back to comma separation
            if separator is None and ',' in text_content:
                separator = ','

            if separator is None:
                raise ValueError("No clear separator found in content")

            # Parse as CSV/TSV
            df = pd.read_csv(
                io.StringIO(text_content),
                sep=separator,
                header=None,  # Let our sanitizer handle header detection
                encoding=None,  # Already decoded
                on_bad_lines='skip'  # Skip problematic lines
            )

            file_type = 'TSV' if separator == '\t' else 'CSV'
            logger.info(f"Successfully parsed {filename} as {file_type}: {df.shape}")
            return df

        except Exception as e:
            logger.error(f"Error parsing CSV-like content from {filename}: {str(e)}")
            raise FileUploadError(f"Unable to parse file as CSV/TSV: {str(e)}")

    def _looks_like_delimited_data(self, content: str) -> bool:
        """
        Check if HTML content actually contains delimited data.

        Args:
            content: HTML content as string

        Returns:
            bool: True if it looks like delimited data
        """
        try:
            # Remove HTML tags to see the raw content
            import re
            clean_content = re.sub(r'<[^>]+>', '', content)

            # Check for high frequency of tabs or commas
            lines = clean_content.split('\n')
            if len(lines) < 2:
                return False

            # Sample a few lines to check for consistent delimiters
            sample_lines = [line.strip() for line in lines if line.strip()]
            if len(sample_lines) < 2:
                return False

            # Take a reasonable sample (not just first 10 lines which might be metadata)
            data_lines = sample_lines[:20]  # Increased sample size

            # Check for consistent tab or comma separation
            tab_counts = [line.count('\t') for line in data_lines if line.count('\t') > 0]
            comma_counts = [line.count(',') for line in data_lines if line.count(',') > 0]

            # If we have lines with tabs/commas and they're somewhat consistent
            if tab_counts and len(tab_counts) >= 2:
                avg_tabs = sum(tab_counts) / len(tab_counts)
                if avg_tabs > 2:  # At least 2 tabs per line on average
                    return True

            if comma_counts and len(comma_counts) >= 2:
                avg_commas = sum(comma_counts) / len(comma_counts)
                if avg_commas > 2:  # At least 2 commas per line on average
                    return True

            return False

        except Exception:
            return False

    def _parse_delimited_from_html(self, html_content: str, filename: str) -> pd.DataFrame:
        """
        Extract delimited data from HTML content.

        Args:
            html_content: HTML content as string
            filename: Original filename

        Returns:
            pandas DataFrame
        """
        try:
            import re

            # Remove HTML tags but preserve the structure
            clean_content = re.sub(r'<[^>]+>', '', html_content)

            # Split into lines and clean up
            lines = [line.strip() for line in clean_content.split('\n') if line.strip()]

            if not lines:
                raise ValueError("No content found after cleaning HTML")

            # Determine separator by checking the first few data lines
            separator = None
            for line in lines[:5]:
                if '\t' in line and line.count('\t') > 2:
                    separator = '\t'
                    break
                elif ',' in line and line.count(',') > 2:
                    separator = ','
                    break

            if separator is None:
                raise ValueError("No clear separator found")

            # Create a clean delimited content
            delimited_content = '\n'.join(lines)

            # Parse as CSV/TSV
            df = pd.read_csv(
                io.StringIO(delimited_content),
                sep=separator,
                header=None,
                on_bad_lines='skip',
                encoding=None
            )

            logger.info(f"Successfully extracted delimited data from HTML: {filename} (shape: {df.shape})")
            return df

        except Exception as e:
            logger.error(f"Error extracting delimited data from HTML {filename}: {str(e)}")
            raise ValueError(f"Unable to extract delimited data: {str(e)}")

    def _load_csv_with_header_detection(self, contents: bytes, filename: str) -> pd.DataFrame:
        """
        Load CSV with enhanced delimiter detection and header handling.

        Args:
            contents: Raw file contents
            filename: Name of the file

        Returns:
            pandas DataFrame with proper column separation
        """
        try:
            # Decode content
            if contents.startswith(b'\xef\xbb\xbf'):
                contents = contents[3:]  # Remove BOM

            # Try different encodings
            text_content = None
            for encoding in ['utf-8', 'latin-1', 'cp1252']:
                try:
                    text_content = contents.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue

            if text_content is None:
                raise UnicodeDecodeError("Unable to decode CSV content")

            # Detect delimiter by analyzing the content
            delimiter = self._detect_csv_delimiter(text_content)
            logger.debug(f"Detected delimiter: '{delimiter}' for file: {filename}")

            # Pre-process content to handle inconsistent column counts
            processed_content = self._preprocess_csv_content(text_content, delimiter)

            # Load CSV with detected delimiter, no header assumption
            df = pd.read_csv(
                io.StringIO(processed_content),
                sep=delimiter,
                header=None,  # Let sanitizer detect the true header
                skip_blank_lines=False,  # Keep blank lines for header detection
                on_bad_lines='skip',  # Skip problematic lines
                quotechar='"',  # Handle quoted fields properly
                encoding=None  # Already decoded
            )

            logger.info(f"Successfully loaded CSV: {filename} (shape: {df.shape})")
            return df

        except Exception as e:
            logger.error(f"Error loading CSV file {filename}: {str(e)}")
            raise FileUploadError(f"Unable to load CSV file: {str(e)}")

    def _detect_csv_delimiter(self, content: str) -> str:
        """
        Detect the most likely delimiter in CSV content.

        Args:
            content: Text content of the CSV

        Returns:
            Most likely delimiter character
        """
        import csv

        # Get first few lines for analysis
        lines = content.split('\n')[:10]
        sample_content = '\n'.join(lines)

        # Try to use csv.Sniffer
        try:
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample_content, delimiters=',;\t|').delimiter
            logger.debug(f"CSV Sniffer detected delimiter: '{delimiter}'")
            return delimiter
        except Exception as e:
            logger.debug(f"CSV Sniffer failed: {e}, falling back to manual detection")

        # Manual delimiter detection
        delimiters = [',', ';', '\t', '|']
        delimiter_scores = {}

        for delimiter in delimiters:
            score = 0
            consistent_count = None

            for line in lines:
                if line.strip():  # Skip empty lines
                    count = line.count(delimiter)
                    if count > 0:
                        score += count
                        if consistent_count is None:
                            consistent_count = count
                        elif consistent_count == count:
                            score += 2  # Bonus for consistency

            delimiter_scores[delimiter] = score

        # Return the delimiter with the highest score
        best_delimiter = max(delimiter_scores, key=delimiter_scores.get)
        logger.debug(f"Manual detection scores: {delimiter_scores}, selected: '{best_delimiter}'")

        return best_delimiter if delimiter_scores[best_delimiter] > 0 else ','

    def _preprocess_csv_content(self, content: str, delimiter: str) -> str:
        """
        Preprocess CSV content to handle inconsistent column counts and formatting issues.

        Args:
            content: Raw CSV content
            delimiter: Detected delimiter

        Returns:
            Preprocessed CSV content
        """
        import csv
        from io import StringIO

        lines = content.split('\n')
        processed_lines = []
        max_columns = 0

        # First pass: determine the maximum number of columns
        for line in lines:
            if line.strip():  # Skip empty lines for column count analysis
                try:
                    reader = csv.reader([line], delimiter=delimiter, quotechar='"')
                    parsed_line = next(reader)
                    max_columns = max(max_columns, len(parsed_line))
                except Exception:
                    # If parsing fails, count delimiters as fallback
                    max_columns = max(max_columns, line.count(delimiter) + 1)

        logger.debug(f"Detected maximum columns: {max_columns}")

        # Second pass: normalize all lines to have the same number of columns
        for line in lines:
            if not line.strip():  # Keep empty lines as-is
                processed_lines.append(line)
                continue

            try:
                # Parse the line properly
                reader = csv.reader([line], delimiter=delimiter, quotechar='"')
                parsed_line = next(reader)

                # Pad with empty strings if needed
                while len(parsed_line) < max_columns:
                    parsed_line.append('')

                # Truncate if too many columns (shouldn't happen, but safety)
                if len(parsed_line) > max_columns:
                    parsed_line = parsed_line[:max_columns]

                # Reconstruct the line with proper quoting
                output = StringIO()
                writer = csv.writer(output, delimiter=delimiter, quotechar='"', quoting=csv.QUOTE_MINIMAL)
                writer.writerow(parsed_line)
                processed_lines.append(output.getvalue().strip())

            except Exception as e:
                logger.warning(f"Error processing line: '{line}' - {e}")
                # Fallback: split by delimiter and pad
                parts = line.split(delimiter)
                while len(parts) < max_columns:
                    parts.append('')
                processed_lines.append(delimiter.join(parts[:max_columns]))

        return '\n'.join(processed_lines)

    # Additional data loading methods can be added here in the future
    # For example:
    # async def load_json_data(self, file: UploadFile) -> pd.DataFrame:
    #     ...
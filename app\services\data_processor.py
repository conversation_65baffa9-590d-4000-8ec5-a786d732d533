import pandas as pd
import warnings
from contextlib import contextmanager
from app.core.logging_config import configure_logging
from typing import Dict, List

logger = configure_logging('app.services.data_processor')

@contextmanager
def suppress_datetime_warnings():
    """Context manager to suppress pandas datetime format warnings."""
    with warnings.catch_warnings():
        warnings.filterwarnings(
            "ignore",
            message="Could not infer format, so each element will be parsed individually, falling back to `dateutil`",
            category=UserWarning
        )
        yield

class DataProcessor:
    @staticmethod
    def _safe_column_lower(column_name):
        """
        Safely convert column name to lowercase, handling NaN and non-string values.

        Args:
            column_name: The column name to convert

        Returns:
            str: Lowercase string representation of the column name
        """
        if pd.isna(column_name):
            return ''
        return str(column_name).lower()

    def process_data(self, df: pd.DataFrame) -> dict:
        """Process the DataFrame and return processed data with metadata."""
        # Ensure df is a DataFrame
        if not isinstance(df, pd.DataFrame):
            df = pd.DataFrame()

        # Always include total_rows in the default response
        default_response = {
            "df": df,
            "column_types": {
                "categorical": [],
                "numerical": [],
                "datetime": []
            },
            "metadata": {
                "total_rows": len(df),
                "unique_counts": {},
                "categorical_columns": [],
                "numerical_columns": [],
                "datetime_columns": [],
                "sample_data": [],
                "date_metadata": {}
            }
        }

        if df.empty:
            return default_response

        try:
            df = self.parse_dates(df)

            # Extract datetime components for categorical analysis
            df = self._extract_datetime_components(df)

            column_types = self._identify_column_types(df)
            metadata = self._generate_metadata(df, column_types)

            # Ensure total_rows is present in metadata
            if 'total_rows' not in metadata:
                metadata['total_rows'] = len(df)

            return {
                "df": df,
                "column_types": column_types,
                "metadata": metadata
            }
        except Exception as e:
            logger.error(f"Data Processing Error: {e}")
            return default_response

    def parse_dates(self, df: pd.DataFrame) -> pd.DataFrame:
        """Parses date columns and handles inconsistencies."""
        # Common date patterns to check - keep machine-readable formats for parsing
        # The display formats will be applied later when rendering charts
        # Enhanced patterns with better coverage for international formats
        date_patterns = [
            # Date only patterns
            '%Y-%m-%d',
            '%d/%m/%Y',  # DD/MM/YYYY (European format)
            '%m/%d/%Y',  # MM/DD/YYYY (US format)
            '%Y/%m/%d',  # YYYY/MM/DD (ISO-like)
            '%d-%m-%Y',  # DD-MM-YYYY
            '%m-%d-%Y',  # MM-DD-YYYY
            '%Y.%m.%d',  # YYYY.MM.DD
            '%d.%m.%Y',  # DD.MM.YYYY
            '%b %d %Y',  # Jan 01 2024
            '%B %d %Y',  # January 01 2024
            '%d %b %Y',  # 01 Jan 2024
            '%d %B %Y',  # 01 January 2024

            # DateTime patterns with seconds
            '%Y-%m-%d %H:%M:%S',  # YYYY-MM-DD HH:MM:SS
            '%Y/%m/%d %H:%M:%S',  # YYYY/MM/DD HH:MM:SS
            '%d/%m/%Y %H:%M:%S',  # DD/MM/YYYY HH:MM:SS (European)
            '%m/%d/%Y %H:%M:%S',  # MM/DD/YYYY HH:MM:SS (US)
            '%d-%m-%Y %H:%M:%S',  # DD-MM-YYYY HH:MM:SS
            '%m-%d-%Y %H:%M:%S',  # MM-DD-YYYY HH:MM:SS
            '%d.%m.%Y %H:%M:%S',  # DD.MM.YYYY HH:MM:SS

            # DateTime patterns without seconds (HH:MM format)
            '%d/%m/%Y %H:%M',     # DD/MM/YYYY HH:MM (European - priority)
            '%m/%d/%Y %H:%M',     # MM/DD/YYYY HH:MM (US)
            '%Y/%m/%d %H:%M',     # YYYY/MM/DD HH:MM
            '%Y-%m-%d %H:%M',     # YYYY-MM-DD HH:MM
            '%d-%m-%Y %H:%M',     # DD-MM-YYYY HH:MM
            '%m-%d-%Y %H:%M',     # MM-DD-YYYY HH:MM
            '%d.%m.%Y %H:%M',     # DD.MM.YYYY HH:MM

            # ISO and other standard formats
            '%Y-%m-%dT%H:%M:%S',      # ISO format
            '%Y-%m-%dT%H:%M:%S.%f',   # ISO format with microseconds
            '%Y-%m-%dT%H:%M:%SZ',     # ISO format with Z
            '%Y-%m-%d %H:%M:%S.%f',   # Timestamp with microseconds
            '%Y-%m-%dT%H:%M',         # ISO format without seconds
        ]

        for col in df.select_dtypes(include=["object"]).columns:
            # Skip obvious non-date columns
            col_lower = self._safe_column_lower(col)
            if any(keyword in col_lower for keyword in ['name', 'product', 'category', 'type', 'id']):
                continue

            # Try to identify date columns
            sample = df[col].dropna().head(10)
            is_date = False

            # First try with explicit formats for better performance and consistency
            for pattern in date_patterns:
                try:
                    # Try parsing a sample with the pattern
                    pd.to_datetime(sample, format=pattern)
                    df[col] = pd.to_datetime(df[col], format=pattern)
                    is_date = True
                    logger.debug(f"Successfully parsed {col} as date with format {pattern}")
                    break
                except ValueError:
                    continue

            # If no explicit format worked but column name suggests it's a date
            if not is_date and any(keyword in col_lower for keyword in ['date', 'time', 'day', 'month', 'year', 'created', 'updated', 'timestamp']):
                try:
                    # Try to detect the format from the first non-null value
                    first_value = df[col].dropna().iloc[0] if not df[col].dropna().empty else None
                    if first_value:
                        # Try to guess the format based on the first value
                        if '-' in first_value and ':' in first_value:  # Likely a datetime with hyphens
                            # Try both with and without seconds
                            for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M']:
                                try:
                                    df[col] = pd.to_datetime(df[col], format=fmt)
                                    is_date = True
                                    logger.debug(f"Successfully parsed {col} as date with format {fmt}")
                                    break
                                except ValueError:
                                    continue
                        elif '/' in first_value and ':' in first_value:  # Likely a datetime with slashes
                            # Try to determine if it's DD/MM/YYYY or MM/DD/YYYY format
                            # Check if first part could be day (> 12) to distinguish DD/MM from MM/DD
                            parts = first_value.split('/')
                            if len(parts) >= 2:
                                try:
                                    first_part = int(parts[0])
                                    if first_part > 12:  # Must be DD/MM/YYYY format
                                        formats_to_try = ['%d/%m/%Y %H:%M:%S', '%d/%m/%Y %H:%M']
                                    else:
                                        # Could be either, try DD/MM first (more common internationally)
                                        formats_to_try = ['%d/%m/%Y %H:%M:%S', '%d/%m/%Y %H:%M', '%m/%d/%Y %H:%M:%S', '%m/%d/%Y %H:%M']
                                except ValueError:
                                    # If first part is not numeric, try common formats
                                    formats_to_try = ['%d/%m/%Y %H:%M:%S', '%d/%m/%Y %H:%M', '%m/%d/%Y %H:%M:%S', '%m/%d/%Y %H:%M']
                            else:
                                formats_to_try = ['%d/%m/%Y %H:%M:%S', '%d/%m/%Y %H:%M', '%m/%d/%Y %H:%M:%S', '%m/%d/%Y %H:%M']

                            for fmt in formats_to_try:
                                try:
                                    df[col] = pd.to_datetime(df[col], format=fmt)
                                    is_date = True
                                    logger.debug(f"Successfully parsed {col} as date with format {fmt}")
                                    break
                                except ValueError:
                                    continue
                        elif '-' in first_value:  # Likely a date with hyphens
                            df[col] = pd.to_datetime(df[col], format='%Y-%m-%d')
                            is_date = True
                            logger.debug(f"Successfully parsed {col} as date with format %Y-%m-%d")
                        elif '/' in first_value:  # Likely a date with slashes
                            # Try to determine DD/MM/YYYY vs MM/DD/YYYY
                            parts = first_value.split('/')
                            if len(parts) >= 2:
                                try:
                                    first_part = int(parts[0])
                                    if first_part > 12:  # Must be DD/MM/YYYY format
                                        formats_to_try = ['%d/%m/%Y']
                                    else:
                                        # Could be either, try DD/MM first
                                        formats_to_try = ['%d/%m/%Y', '%m/%d/%Y']
                                except ValueError:
                                    formats_to_try = ['%d/%m/%Y', '%m/%d/%Y']
                            else:
                                formats_to_try = ['%d/%m/%Y', '%m/%d/%Y']

                            for fmt in formats_to_try:
                                try:
                                    df[col] = pd.to_datetime(df[col], format=fmt)
                                    is_date = True
                                    logger.debug(f"Successfully parsed {col} as date with format {fmt}")
                                    break
                                except ValueError:
                                    continue
                        else:
                            # Last resort with explicit format
                            df[col] = pd.to_datetime(df[col], format='%Y%m%d')
                            is_date = True
                            logger.debug(f"Successfully parsed {col} as date with format %Y%m%d")

                        if is_date:
                            logger.debug(f"Successfully parsed {col} as date with inferred format from first value")
                except (ValueError, IndexError, TypeError):
                    is_date = False

            # If still not identified as a date but column name strongly suggests it's a date
            if not is_date and any(keyword == col_lower for keyword in ['date', 'created_at', 'updated_at', 'timestamp']):
                try:
                    # Use dateutil parser with yearfirst=True for more consistent parsing
                    # Suppress the format inference warning
                    with suppress_datetime_warnings():
                        df[col] = pd.to_datetime(df[col], yearfirst=True, errors='raise')
                    is_date = True
                    logger.debug(f"Successfully parsed {col} as date with dateutil parser (yearfirst=True)")
                except (ValueError, TypeError):
                    logger.debug(f"Column {col} is not a date column despite name suggestion")

            # Last resort: try automatic parsing if not yet identified as a date
            # BUT only if the column name strongly suggests it's a date AND the values don't look like numbers
            if not is_date and any(keyword in col_lower for keyword in ['date', 'created_at', 'updated_at', 'timestamp']):
                # Additional check: make sure the values don't look like pure numbers
                sample_str = str(df[col].dropna().iloc[0]) if not df[col].dropna().empty else ""
                # Skip if it looks like a number (contains only digits, commas, periods, and minus signs)
                if not all(c.isdigit() or c in '.,- ' for c in sample_str):
                    try:
                        # Use dateutil parser with default settings as last resort
                        # Suppress the format inference warning since we're intentionally using dateutil
                        with suppress_datetime_warnings():
                            df[col] = pd.to_datetime(df[col], errors='raise')
                        logger.debug(f"Successfully parsed {col} as date with dateutil parser")
                    except (ValueError, TypeError):
                        logger.debug(f"Column {col} is not a date column")
                else:
                    logger.debug(f"Skipping date parsing for {col} as it looks like numeric data")

        return df

    def _extract_datetime_components(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Extract datetime components (date, month, year, hour) from datetime columns
        and add them as new columns if they meet categorical eligibility criteria.
        """
        try:
            datetime_columns = df.select_dtypes(include=['datetime64']).columns.tolist()

            if not datetime_columns:
                logger.debug("No datetime columns found for component extraction")
                return df

            logger.info(f"Extracting datetime components from {len(datetime_columns)} datetime columns")

            for col in datetime_columns:
                try:
                    col_data = df[col].dropna()
                    if col_data.empty:
                        logger.debug(f"Skipping {col}: no valid datetime values")
                        continue

                    # Extract date component (without time)
                    date_col_name = f"{col}_date"
                    df[date_col_name] = df[col].dt.date

                    # Check if date component is eligible for categorical analysis
                    if self._is_datetime_component_categorical(df[date_col_name]):
                        logger.info(f"Added categorical date component: {date_col_name}")
                    else:
                        # Remove if not suitable for categorical analysis
                        df.drop(columns=[date_col_name], inplace=True)
                        logger.debug(f"Removed {date_col_name}: not suitable for categorical analysis")

                    # Extract month component
                    month_col_name = f"{col}_month"
                    df[month_col_name] = df[col].dt.month_name()

                    # Check if month component is eligible for categorical analysis
                    if self._is_datetime_component_categorical(df[month_col_name]):
                        logger.info(f"Added categorical month component: {month_col_name}")
                    else:
                        # Remove if not suitable for categorical analysis
                        df.drop(columns=[month_col_name], inplace=True)
                        logger.debug(f"Removed {month_col_name}: not suitable for categorical analysis")

                    # Extract year component
                    year_col_name = f"{col}_year"
                    df[year_col_name] = df[col].dt.year.astype(str)

                    # Check if year component is eligible for categorical analysis
                    if self._is_datetime_component_categorical(df[year_col_name]):
                        logger.info(f"Added categorical year component: {year_col_name}")
                    else:
                        # Remove if not suitable for categorical analysis
                        df.drop(columns=[year_col_name], inplace=True)
                        logger.debug(f"Removed {year_col_name}: not suitable for categorical analysis")

                    # Extract hour component only if dataset spans one day
                    if self._is_single_day_dataset(col_data):
                        hour_col_name = f"{col}_hour"
                        df[hour_col_name] = df[col].dt.hour.astype(str) + ":00"

                        logger.debug(f"Created hour component {hour_col_name} with {df[hour_col_name].nunique()} unique values")

                        # Check if hour component is eligible for categorical analysis
                        if self._is_datetime_component_categorical(df[hour_col_name]):
                            logger.info(f"Added categorical hour component: {hour_col_name}")
                        else:
                            # Remove if not suitable for categorical analysis
                            df.drop(columns=[hour_col_name], inplace=True)
                            logger.debug(f"Removed {hour_col_name}: not suitable for categorical analysis")
                    else:
                        logger.debug(f"Skipping hour extraction for {col}: dataset spans multiple days")

                except Exception as e:
                    logger.warning(f"Error extracting components from datetime column {col}: {str(e)}")
                    continue

            return df

        except Exception as e:
            logger.error(f"Error in datetime component extraction: {str(e)}")
            return df

    def _is_datetime_component_categorical(self, series: pd.Series) -> bool:
        """
        Check if a datetime component series meets categorical eligibility criteria.

        Args:
            series: Pandas series containing datetime component values

        Returns:
            bool: True if the component is suitable for categorical analysis
        """
        try:
            # Remove null values for analysis
            clean_series = series.dropna()

            if clean_series.empty:
                return False

            total_count = len(clean_series)
            unique_count = clean_series.nunique()
            unique_ratio = unique_count / total_count if total_count > 0 else 0

            # Categorical eligibility criteria for datetime components
            # - Must have at least 2 unique values
            # - Must have reasonable number of categories (not too many unique values)
            # - Unique ratio should not be too high (indicating it's not an ID-like field)

            min_unique_values = 2
            max_unique_values = 100  # Reasonable limit for categorical analysis
            max_unique_ratio = 0.9   # Similar to existing datetime categorical logic

            is_eligible = (
                unique_count >= min_unique_values and
                unique_count <= max_unique_values and
                unique_ratio <= max_unique_ratio
            )

            logger.debug(
                f"Datetime component eligibility check: "
                f"unique_count={unique_count}, unique_ratio={unique_ratio:.4f}, "
                f"eligible={is_eligible}"
            )

            return is_eligible

        except Exception as e:
            logger.warning(f"Error checking datetime component categorical eligibility: {str(e)}")
            return False

    def _is_single_day_dataset(self, datetime_series: pd.Series) -> bool:
        """
        Check if the datetime series spans only one day.

        Args:
            datetime_series: Pandas series containing datetime values

        Returns:
            bool: True if the dataset spans only one day
        """
        try:
            clean_series = datetime_series.dropna()

            if clean_series.empty:
                return False

            # Get the date range
            min_date = clean_series.min().date()
            max_date = clean_series.max().date()

            is_single_day = min_date == max_date

            if is_single_day:
                logger.info(f"Dataset spans single day: {min_date}")
            else:
                logger.debug(f"Dataset spans multiple days: {min_date} to {max_date}")

            return is_single_day

        except Exception as e:
            logger.warning(f"Error checking if dataset is single day: {str(e)}")
            return False

    def _identify_column_types(self, df: pd.DataFrame) -> Dict[str, List[str]]:
        """Identify column types in the DataFrame."""
        column_types = {
            "categorical": [],
            "numerical": [],
            "datetime": [],
            "text": [],  # New type for text columns like descriptions
            "id": []    # New type for identifier columns
        }

        # Log column dtypes for debugging
        logger.debug(f"DataFrame dtypes: {df.dtypes}")

        # First pass: identify obvious types and collect stats
        column_stats = {}
        categorical_candidates = []
        text_candidates = []
        id_candidates = []

        for column in df.columns:
            # Safely convert column name to lowercase for comparisons
            column_lower = self._safe_column_lower(column)

            try:
                # Get the column data - handle potential duplicate column names
                column_data = df[column]

                # If we get a DataFrame instead of a Series (due to duplicate column names),
                # take the first column
                if isinstance(column_data, pd.DataFrame):
                    logger.warning(f"Duplicate column name detected: {column}. Using first occurrence.")
                    column_data = column_data.iloc[:, 0]
                    # Update the DataFrame to remove duplicates by renaming them
                    duplicate_cols = [col for col in df.columns if col == column]
                    if len(duplicate_cols) > 1:
                        for i, dup_col_idx in enumerate(df.columns.get_indexer(duplicate_cols)):
                            if i > 0:  # Keep the first one, rename the rest
                                df.columns.values[dup_col_idx] = f"{column}_duplicate_{i}"

                # Calculate statistics for each column
                unique_count = column_data.nunique()
                unique_ratio = unique_count / len(df) if len(df) > 0 else 0
                avg_str_length = 0

                # Calculate average string length for object columns
                if hasattr(column_data, 'dtype') and column_data.dtype == 'object':
                    avg_str_length = column_data.astype(str).str.len().mean()

            except Exception as e:
                logger.error(f"Error processing column {column}: {str(e)}")
                # Set default values if we can't process the column
                unique_count = 0
                unique_ratio = 0
                avg_str_length = 0
                column_data = pd.Series(dtype='object')  # Create empty series as fallback

            column_stats[column] = {
                'unique_count': unique_count,
                'unique_ratio': unique_ratio,
                'avg_str_length': avg_str_length
            }

            logger.debug(f"Column {column}: unique_count={unique_count}, unique_ratio={unique_ratio:.4f}, avg_str_length={avg_str_length:.2f}")

            # Check for ID columns first (high unique ratio or ID-like column names)
            is_id_column = False

            # Special handling for datetime columns with high unique ratio
            if pd.api.types.is_datetime64_any_dtype(column_data):
                # Always add to datetime columns first
                column_types["datetime"].append(column)

                try:
                    # Check if the column would still have high unique ratio after removing timestamp
                    date_only_series = column_data.dt.date
                    date_only_unique_count = date_only_series.nunique()
                    date_only_unique_ratio = date_only_unique_count / len(df) if len(df) > 0 else 0

                    logger.debug(f"Datetime column {column}: date-only unique_count={date_only_unique_count}, unique_ratio={date_only_unique_ratio:.4f}")
                except Exception as e:
                    logger.error(f"Error processing datetime column {column}: {str(e)}")
                    # Set default values if we can't calculate
                    date_only_unique_count = 0
                    date_only_unique_ratio = 0

                # If unique ratio is high but not extremely high, or if we have a reasonable number of unique dates,
                # add to categorical columns as well
                if (date_only_unique_ratio < 0.9 or date_only_unique_count <= 100) and date_only_unique_count >= 2:
                    logger.debug(f"Adding datetime column {column} to categorical columns (date-only unique ratio: {date_only_unique_ratio:.4f})")
                    categorical_candidates.append((column, date_only_unique_count, 'datetime'))
                    continue
                elif unique_ratio > 0.95:
                    # Only if the original unique ratio is very high, consider as ID
                    id_candidates.append((column, unique_count))
                    is_id_column = True
                    logger.debug(f"Identified datetime column {column} as ID column (date-only unique ratio: {date_only_unique_ratio:.4f})")
                continue

            # Check if column name suggests it's an ID
            id_keywords = ['id', 'key', 'code', 'number', 'num', 'identifier', 'uuid', 'guid']
            if any(keyword in column_lower for keyword in id_keywords) or 'issue' in column_lower:
                # If it has high unique ratio, it's likely an ID
                if unique_ratio > 0.9:
                    id_candidates.append((column, unique_count))
                    is_id_column = True
                    logger.debug(f"Identified {column} as ID column based on name and high unique ratio")

            # Also check for high unique ratio columns that might be IDs
            # But be more careful - only classify as ID if it's really an ID-like column
            # AND it doesn't look like numeric data
            elif unique_ratio > 0.95 and any(keyword in column_lower for keyword in id_keywords):
                # Additional check: make sure it's not numeric data that happens to have high uniqueness
                is_likely_numeric = False
                try:
                    if hasattr(column_data, 'str'):
                        cleaned_values = column_data.str.replace(',', '').str.replace('$', '').str.replace('%', '').str.strip()
                        numeric_values = pd.to_numeric(cleaned_values, errors='coerce')
                        if numeric_values.notna().sum() > 0.7 * len(numeric_values):
                            is_likely_numeric = True
                except:
                    pass

                if not is_likely_numeric:
                    id_candidates.append((column, unique_count))
                    is_id_column = True
                    logger.debug(f"Identified {column} as ID column based on very high unique ratio ({unique_ratio:.4f})")
                else:
                    logger.debug(f"Skipping ID classification for {column} as it appears to be numeric data")

            # Skip further processing for ID columns
            if is_id_column:
                continue

            # First, try to convert columns with numeric names if they're not already numeric
            # Check for both exact matches and partial matches (e.g., "market value" contains "value")
            numeric_keywords = ['quantity', 'price', 'amount', 'cost', 'value', 'number', 'hours', 'stock', 'revenue', 'sales', 'profit', 'income', 'expense', 'total', 'sum', 'count', 'rate', 'percentage', 'percent']
            if any(keyword in column_lower for keyword in numeric_keywords) and not pd.api.types.is_numeric_dtype(column_data):
                try:
                    # Try to convert with cleaning for common numeric formats
                    if hasattr(column_data, 'str'):  # Check if it's a string-like series
                        cleaned_values = column_data.str.replace(',', '').str.replace('$', '').str.replace('%', '')
                        numeric_values = pd.to_numeric(cleaned_values, errors='coerce')
                    else:
                        numeric_values = pd.to_numeric(column_data, errors='coerce')

                    # Only convert if most values are successfully converted
                    if numeric_values.notna().sum() > 0.7 * len(numeric_values):
                        df[column] = numeric_values
                        # Update column_data to reflect the conversion
                        column_data = df[column]
                        logger.debug(f"Converted {column} to numeric type during column type identification")
                    else:
                        logger.debug(f"Skipping numeric conversion for {column}: too many non-numeric values")
                except Exception as e:
                    logger.warning(f"Failed to convert {column} to numeric during identification: {str(e)}")

            # Now identify the column type
            # Skip datetime check since we already handled it above
            if pd.api.types.is_numeric_dtype(column_data):
                # Check if it's a numeric ID column (high unique ratio)
                if unique_ratio > 0.9 and any(keyword in column_lower for keyword in id_keywords):
                    id_candidates.append((column, unique_count))
                    logger.debug(f"Identified {column} as numeric ID column")
                # Check if the column has few unique values relative to total rows
                elif unique_ratio < 0.05 and unique_count < 20 and not any(keyword in column_lower for keyword in numeric_keywords):
                    categorical_candidates.append((column, unique_count, 'numeric'))
                else:
                    column_types["numerical"].append(column)
                    logger.debug(f"Identified {column} as numerical type")
            else:
                # Try one more time to convert to numeric if the column name suggests it should be numeric
                if any(keyword in column_lower for keyword in numeric_keywords):
                    try:
                        # Try to convert with more aggressive error handling
                        if hasattr(column_data, 'str'):  # Check if it's a string-like series
                            numeric_values = pd.to_numeric(column_data.str.replace(',', '').str.replace('$', ''), errors='coerce')
                        else:
                            numeric_values = pd.to_numeric(column_data, errors='coerce')

                        if numeric_values.notna().sum() > 0.5 * len(numeric_values):  # If more than 50% converted successfully
                            df[column] = numeric_values
                            column_types["numerical"].append(column)
                            logger.debug(f"Forced {column} to numerical type with cleaning")
                            continue
                    except Exception as e:
                        logger.warning(f"Failed final attempt to convert {column} to numeric: {str(e)}")

                # Before categorizing, try one final attempt to detect if this could be numeric
                # This catches cases where numeric data doesn't have obvious column names
                try:
                    if hasattr(column_data, 'str'):  # Check if it's a string-like series
                        # Try to clean and convert common numeric formats
                        cleaned_values = column_data.str.replace(',', '').str.replace('$', '').str.replace('%', '').str.strip()
                        numeric_values = pd.to_numeric(cleaned_values, errors='coerce')

                        # If most values can be converted to numeric, treat as numeric
                        if numeric_values.notna().sum() > 0.8 * len(numeric_values):
                            df[column] = numeric_values
                            column_types["numerical"].append(column)
                            logger.debug(f"Detected {column} as numeric through general numeric detection")
                            continue
                except Exception as e:
                    logger.debug(f"General numeric detection failed for {column}: {str(e)}")

                # Determine if this is a categorical column or a text column
                if any(keyword in column_lower for keyword in ['name', 'description', 'desc', 'comment', 'review', 'feedback', 'text', 'summary']):
                    # Check if it's a long text column
                    if avg_str_length > 20 or unique_ratio > 0.8:
                        text_candidates.append((column, unique_count))
                    else:
                        categorical_candidates.append((column, unique_count, 'text'))
                elif unique_ratio > 0.8 and avg_str_length > 20:
                    # High unique ratio and long strings suggest text data
                    text_candidates.append((column, unique_count))
                elif column_lower in ['category', 'type', 'status', 'color', 'size', 'gender', 'availability', 'condition', 'brand', 'assignee']:
                    # Known categorical columns
                    categorical_candidates.append((column, unique_count, 'known'))
                else:
                    categorical_candidates.append((column, unique_count, 'other'))

        # Filter out categorical candidates with only one unique value
        filtered_categorical_candidates = []
        for col, count, col_type in categorical_candidates:
            if count <= 1:
                logger.debug(f"Skipping {col} as it has only {count} unique value(s)")
            else:
                filtered_categorical_candidates.append((col, count, col_type))

        # Sort filtered categorical candidates by unique count (ascending) to prioritize true categorical columns
        filtered_categorical_candidates.sort(key=lambda x: x[1])

        # Enhanced logic: Include more categorical columns for better chart variety
        # Categorize by unique count ranges for better distribution
        priority_categorical = []  # 2-10 unique values (highest priority)
        standard_categorical = []  # 11-50 unique values (standard priority)
        extended_categorical = []  # 51-200 unique values (extended for variety)

        for col, count, col_type in filtered_categorical_candidates:
            if 2 <= count <= 10:
                priority_categorical.append((col, count, col_type))
            elif 11 <= count <= 50:
                standard_categorical.append((col, count, col_type))
            elif 51 <= count <= 200:
                extended_categorical.append((col, count, col_type))

        # Add all priority categorical columns (2-10 unique values)
        for col, count, col_type in priority_categorical:
            column_types["categorical"].append(col)
            logger.debug(f"Added priority categorical column {col} (unique count: {count}, type: {col_type})")

        # Add up to 8 standard categorical columns (11-50 unique values)
        for col, count, col_type in standard_categorical[:8]:
            column_types["categorical"].append(col)
            logger.debug(f"Added standard categorical column {col} (unique count: {count}, type: {col_type})")

        # Add up to 5 extended categorical columns for variety (51-200 unique values)
        for col, count, col_type in extended_categorical[:5]:
            column_types["categorical"].append(col)
            logger.debug(f"Added extended categorical column {col} (unique count: {count}, type: {col_type})")

        # Log the total categorical columns identified
        logger.info(f"Total categorical columns identified: {len(column_types['categorical'])} "
                   f"(priority: {len(priority_categorical)}, standard: {min(len(standard_categorical), 8)}, "
                   f"extended: {min(len(extended_categorical), 5)})")

        # Add text columns
        for col, count in text_candidates:
            column_types["text"].append(col)
            logger.debug(f"Identified {col} as text column (unique count: {count})")

        # Add ID columns
        for col, count in id_candidates:
            column_types["id"].append(col)
            logger.debug(f"Added {col} to ID columns (unique count: {count})")

        # Log the final column type counts in a single message
        logger.info(
            f"Column counts: categorical={len(column_types['categorical'])}, "
            f"numerical={len(column_types['numerical'])}, "
            f"datetime={len(column_types['datetime'])}, "
            f"text={len(column_types['text'])}, "
            f"id={len(column_types['id'])}"
        )

        # For backward compatibility, move text columns to categorical
        column_types["categorical"].extend(column_types["text"])
        column_types.pop("text")

        # ID columns are not used for charts, so we don't need to include them in the return value
        column_types.pop("id")

        return column_types

    def _generate_metadata(self, df: pd.DataFrame, column_types: Dict[str, List[str]]) -> Dict:
        """Generate comprehensive metadata about the DataFrame for enhanced chart recommendations."""
        metadata = {
            "total_rows": len(df),
            "categorical_columns": column_types["categorical"],
            "numerical_columns": column_types["numerical"],
            "datetime_columns": column_types["datetime"],
            "unique_counts": {},
            "date_metadata": {},
            "sample_data": [],
            # Enhanced metadata for better chart recommendations
            "column_characteristics": {},  # Detailed info about each column
            "categorical_combinations": [],  # Possible combinations for multi-dimensional charts
            "data_quality": {}  # Data quality metrics
        }

        # Get unique counts and detailed characteristics for all columns
        all_columns = (column_types["categorical"] + column_types["numerical"] +
                      column_types["datetime"])

        for col in all_columns:
            try:
                col_data = df[col]
                if isinstance(col_data, pd.DataFrame):
                    col_data = col_data.iloc[:, 0]  # Take first column if duplicate names

                unique_count = col_data.nunique()
                metadata["unique_counts"][col] = unique_count

                # Enhanced column characteristics
                col_type = next((k for k, v in column_types.items() if col in v), "unknown")
                metadata["column_characteristics"][col] = {
                    "type": col_type,
                    "unique_count": unique_count,
                    "unique_ratio": unique_count / len(df) if len(df) > 0 else 0,
                    "null_count": col_data.isnull().sum(),
                    "null_ratio": col_data.isnull().sum() / len(df) if len(df) > 0 else 0
                }

                # Add type-specific characteristics
                if col_type == "categorical":
                    # Get top categories for categorical columns
                    top_categories = col_data.value_counts().head(10).to_dict()
                    metadata["column_characteristics"][col]["top_categories"] = top_categories
                    metadata["column_characteristics"][col]["avg_text_length"] = (
                        col_data.astype(str).str.len().mean() if not col_data.empty else 0
                    )
                elif col_type == "numerical":
                    # Get statistical info for numerical columns
                    if not col_data.empty and col_data.notna().any():
                        metadata["column_characteristics"][col].update({
                            "min_value": float(col_data.min()),
                            "max_value": float(col_data.max()),
                            "mean_value": float(col_data.mean()),
                            "std_value": float(col_data.std()) if col_data.std() is not None else 0
                        })
                elif col_type == "datetime":
                    # Get date range info for datetime columns
                    if not col_data.empty and col_data.notna().any():
                        metadata["column_characteristics"][col].update({
                            "min_date": col_data.min(),
                            "max_date": col_data.max(),
                            "date_range_days": (col_data.max() - col_data.min()).days if col_data.max() != col_data.min() else 0
                        })

            except Exception as e:
                logger.warning(f"Error getting characteristics for column {col}: {str(e)}")
                metadata["unique_counts"][col] = 0
                metadata["column_characteristics"][col] = {
                    "type": "unknown",
                    "unique_count": 0,
                    "unique_ratio": 0,
                    "null_count": 0,
                    "null_ratio": 0
                }

        # Generate categorical combinations for multi-dimensional charts
        categorical_cols = column_types["categorical"]
        if len(categorical_cols) >= 2:
            # Generate pairs of categorical columns for heatmaps, grouped charts, etc.
            for i, col1 in enumerate(categorical_cols[:10]):  # Limit to first 10 for performance
                for col2 in categorical_cols[i+1:10]:
                    try:
                        col1_unique = metadata["unique_counts"].get(col1, 0)
                        col2_unique = metadata["unique_counts"].get(col2, 0)
                        combined_cardinality = col1_unique * col2_unique

                        # Only include combinations that make sense for visualization
                        if (2 <= col1_unique <= 50 and 2 <= col2_unique <= 50 and
                            combined_cardinality <= 500):
                            metadata["categorical_combinations"].append({
                                "col1": col1,
                                "col2": col2,
                                "col1_unique": col1_unique,
                                "col2_unique": col2_unique,
                                "combined_cardinality": combined_cardinality,
                                "suitable_for": ["heatmap", "grouped_bar", "stacked_bar"]
                            })
                    except Exception as e:
                        logger.warning(f"Error generating combination for {col1} and {col2}: {str(e)}")
                        continue

        # Generate data quality metrics
        metadata["data_quality"] = {
            "total_columns": len(df.columns),
            "categorical_columns_count": len(column_types["categorical"]),
            "numerical_columns_count": len(column_types["numerical"]),
            "datetime_columns_count": len(column_types["datetime"]),
            "overall_null_ratio": df.isnull().sum().sum() / (len(df) * len(df.columns)) if len(df) > 0 and len(df.columns) > 0 else 0,
            "has_sufficient_data": len(df) >= 10,  # Minimum rows for meaningful charts
            "has_categorical_variety": len([col for col in column_types["categorical"]
                                          if metadata["unique_counts"].get(col, 0) >= 2]) >= 1
        }

        # Generate date metadata for datetime columns
        for col in column_types["datetime"]:
            try:
                col_data = df[col]
                if isinstance(col_data, pd.DataFrame):
                    col_data = col_data.iloc[:, 0]  # Take first column if duplicate names

                if col_data.notna().any():
                    # Ensure column is datetime type before using min/max and strftime
                    if not pd.api.types.is_datetime64_any_dtype(col_data):
                        logger.warning(f"Converting {col} to datetime before generating date metadata")
                        # Try to convert with explicit formats first
                        try:
                            # Check if the column contains ISO format dates
                            if col_data.astype(str).str.contains('T').any():
                                col_data = pd.to_datetime(col_data, format='%Y-%m-%dT%H:%M:%S', errors='coerce')
                            # Check if the column contains dates with hyphens
                            elif col_data.astype(str).str.contains('-').any():
                                col_data = pd.to_datetime(col_data, format='%Y-%m-%d', errors='coerce')
                            # Check if the column contains dates with slashes
                            elif col_data.astype(str).str.contains('/').any():
                                col_data = pd.to_datetime(col_data, format='%m/%d/%Y', errors='coerce')
                            else:
                                # Fall back to dateutil parser with yearfirst=True
                                with suppress_datetime_warnings():
                                    col_data = pd.to_datetime(col_data, yearfirst=True, errors='coerce')
                            # Update the DataFrame with the converted data
                            df[col] = col_data
                        except Exception as e:
                            logger.warning(f"Error converting {col} with explicit formats: {str(e)}")
                            # Fall back to default parser as last resort
                            with suppress_datetime_warnings():
                                col_data = pd.to_datetime(col_data, errors='coerce')
                            df[col] = col_data

                    # Check if conversion was successful
                    if col_data.isna().all():
                        logger.warning(f"Skipping date metadata for {col}: Could not convert to datetime")
                        continue

                    # Store both machine-readable and user-friendly date formats
                    min_date = col_data.min()
                    max_date = col_data.max()

                    metadata["date_metadata"][col] = {
                        "min_date": min_date.strftime("%Y-%m-%d"),  # Machine-readable format for backend processing
                        "max_date": max_date.strftime("%Y-%m-%d"),  # Machine-readable format for backend processing
                        "min_date_display": min_date.strftime("%d %b %Y"),  # User-friendly format for display
                        "max_date_display": max_date.strftime("%d %b %Y"),  # User-friendly format for display
                        "unique_dates": col_data.nunique()
                    }
            except Exception as e:
                logger.error(f"Error generating date metadata for {col}: {str(e)}")
                # Add basic metadata without date formatting
                try:
                    col_data = df[col]
                    if isinstance(col_data, pd.DataFrame):
                        col_data = col_data.iloc[:, 0]
                    metadata["date_metadata"][col] = {
                        "unique_dates": col_data.nunique()
                    }
                except:
                    metadata["date_metadata"][col] = {
                        "unique_dates": 0
                    }

        # Add sample data for each column
        for col in df.columns:
            try:
                col_data = df[col]
                if isinstance(col_data, pd.DataFrame):
                    col_data = col_data.iloc[:, 0]  # Take first column if duplicate names
                sample = col_data.dropna().head(5).tolist()
                metadata["sample_data"].append({
                    "column": col,
                    "type": next((k for k, v in column_types.items() if col in v), "unknown"),
                    "sample": sample
                })
            except Exception as e:
                logger.warning(f"Error getting sample data for column {col}: {str(e)}")
                metadata["sample_data"].append({
                    "column": col,
                    "type": "unknown",
                    "sample": []
                })

        return metadata

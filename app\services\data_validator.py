import pandas as pd
import numpy as np
from typing import Dict, <PERSON><PERSON>
from app.core.logging_config import configure_logging

logger = configure_logging()

class DataValidator:
    """
    Data Validator service for data cleaning and validation operations.
    Provides methods to clean, validate, and prepare data for analysis.
    """

    def __init__(self):
        # Common numeric columns for type inference
        self.numeric_columns = ['quantity', 'price', 'amount', 'cost',
                              'value', 'number', 'num', 'hours', 'stock']
             

    def clean_and_validate_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict]:
        """
        Performs data quality checks and cleaning operations.

        Args:
            df: The pandas DataFrame to clean and validate

        Returns:
            A tuple containing:
            - The cleaned DataFrame
            - A quality report dictionary with data quality metrics
        """

        # Step to clean the DataFrame before validation
        df = self._sanitize_dataframe(df)

        quality_report = {
            'original_shape': df.shape,
            'missing_values': df.isnull().sum().to_dict(),
            'duplicates': len(df) - len(df.drop_duplicates()),
            'data_types': df.dtypes.to_dict(),
            'numeric_columns_stats': {}
        }



        # Clean missing values
        df_cleaned = df.copy()

        # Handle numeric columns
        for col in df_cleaned.columns:
            # Ensure column name is a string before calling .lower()
            col_str = str(col).lower() if not pd.isna(col) else ''
            if col_str in self.numeric_columns:
                df_cleaned[col] = self._convert_to_numeric(df_cleaned[col])
                if pd.api.types.is_numeric_dtype(df_cleaned[col]):
                    quality_report['numeric_columns_stats'][col] = {
                        'mean': df_cleaned[col].mean(),
                        'median': df_cleaned[col].median(),
                        'std': df_cleaned[col].std(),
                        'outliers': self._detect_outliers(df_cleaned[col])
                    }

        # Remove duplicates
        df_cleaned = df_cleaned.drop_duplicates()

        # Update quality report
        quality_report['cleaned_shape'] = df_cleaned.shape

        return df_cleaned, quality_report
    

    def _sanitize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Sanitizes the DataFrame by removing empty rows and setting the correct header.
        Enhanced to detect true header rows by analyzing column count patterns and content.

        Args:
            df: The pandas DataFrame to sanitize.

        Returns:
            A sanitized pandas DataFrame.
        """
        logger.debug(f"Original DataFrame shape before sanitization: {df.shape}")

        # First, reset the index to ensure we work with sequential indices
        df = df.reset_index(drop=True)

        # Enhanced header detection algorithm
        header_row_index = self._detect_true_header_row(df)

        if header_row_index is not None:
            logger.debug(f"Found header row at index: {header_row_index}")

            # Extract the header row
            header_row = df.iloc[header_row_index]

            # Clean and prepare column names
            cleaned_columns = []
            for i, col in enumerate(header_row):
                if pd.isna(col) or str(col).strip() == '':
                    cleaned_columns.append(f'Column_{i+1}')
                else:
                    cleaned_columns.append(str(col).strip())

            # Set the new column names
            df.columns = cleaned_columns

            # Keep only the data rows (after the header)
            df = df.iloc[header_row_index + 1:].reset_index(drop=True)

            logger.debug(f"After header extraction - DataFrame shape: {df.shape}, columns: {list(df.columns)}")
        else:
            logger.warning("No clear header row found. Using default column names.")
            # If no header found, use default column names
            df.columns = [f'Column_{i+1}' for i in range(len(df.columns))]

        # Remove any remaining completely empty rows
        df = df.dropna(how='all').reset_index(drop=True)

        # Ensure all column names are strings and handle any remaining issues
        df.columns = [f'Column_{i+1}' if pd.isna(col) or str(col).strip() == ''
                      else str(col).strip() for i, col in enumerate(df.columns)]

        logger.debug(f"Final sanitized DataFrame shape: {df.shape}")
        return df

    def _detect_true_header_row(self, df: pd.DataFrame) -> int:
        """
        Enhanced header detection that analyzes column count patterns and content quality.

        Args:
            df: The pandas DataFrame to analyze

        Returns:
            Index of the true header row, or None if not found
        """
        if df.empty:
            return None

        # Step 1: Analyze column count patterns
        row_column_counts = []
        for idx in range(min(10, len(df))):  # Check first 10 rows
            row = df.iloc[idx]
            non_null_count = row.notna().sum()
            non_empty_count = sum(1 for val in row if pd.notna(val) and str(val).strip() != '')
            row_column_counts.append((idx, non_null_count, non_empty_count))

        # Find the row with the maximum number of meaningful columns
        max_columns = max(row_column_counts, key=lambda x: x[2])[2] if row_column_counts else 0

        # Step 2: Score each potential header row
        header_candidates = []

        for idx, non_null_count, non_empty_count in row_column_counts:
            # Skip rows with too few columns (likely summary/metadata)
            if non_empty_count < max(3, max_columns * 0.7):
                continue

            row = df.iloc[idx]
            score = self._score_header_row(row, non_empty_count, max_columns)

            if score > 0:  # Only consider rows with positive scores
                header_candidates.append((idx, score, non_empty_count))
                logger.debug(f"Row {idx}: score={score}, columns={non_empty_count}")

        # Step 3: Select the best header candidate
        if header_candidates:
            # Sort by score (descending), then by column count (descending)
            header_candidates.sort(key=lambda x: (x[1], x[2]), reverse=True)
            best_candidate = header_candidates[0]
            logger.info(f"Selected header row {best_candidate[0]} with score {best_candidate[1]} and {best_candidate[2]} columns")
            return best_candidate[0]

        return None

    def _score_header_row(self, row: pd.Series, column_count: int, max_columns: int) -> float:
        """
        Score a potential header row based on content characteristics.

        Args:
            row: The row to score
            column_count: Number of non-empty columns in this row
            max_columns: Maximum columns found in any row

        Returns:
            Score for this row (higher is better)
        """
        score = 0.0

        # Base score for having many columns
        column_ratio = column_count / max_columns if max_columns > 0 else 0
        score += column_ratio * 10

        # Analyze each value in the row
        string_values = [str(val).strip() for val in row.dropna() if str(val).strip() != '']

        if not string_values:
            return -10  # Empty row

        for val in string_values:
            val_lower = val.lower()

            # Strong positive indicators for headers
            header_keywords = [
                'name', 'id', 'date', 'time', 'type', 'category', 'status', 'price', 'amount',
                'quantity', 'description', 'product', 'customer', 'order', 'department',
                'salary', 'age', 'email', 'phone', 'address', 'value', 'weight', 'currency',
                'ticker', 'sektor', 'anlageklasse', 'marktwert', 'gewichtung', 'nominalwert',
                'kurs', 'standort', 'börse', 'marktwährung', 'emittententicker'
            ]

            if any(keyword in val_lower for keyword in header_keywords):
                score += 5

            # Boost for typical header patterns
            if len(val.split()) <= 3 and len(val) > 1:  # Short, meaningful names
                score += 3

            # Boost for alphanumeric headers (common in data files)
            if val.replace('_', '').replace(' ', '').replace('(', '').replace(')', '').replace('%', '').isalnum():
                score += 2

            # Penalties for non-header characteristics
            if ':' in val:  # Metadata often has colons
                score -= 4

            if len(val) > 30:  # Very long strings unlikely to be headers
                score -= 3

            # Penalty for date-like values (often metadata)
            if any(date_indicator in val_lower for date_indicator in ['jan', 'feb', 'mar', 'apr', 'may', 'jun',
                                                                      'jul', 'aug', 'sep', 'oct', 'nov', 'dec',
                                                                      '2020', '2021', '2022', '2023', '2024']):
                score -= 3

            # Penalty for numeric-only values in potential headers
            if val.replace(',', '').replace('.', '').replace('-', '').isdigit():
                score -= 2

        # Additional penalty for rows with very few columns compared to max
        if column_count < max_columns * 0.5:
            score -= 5

        return score

        # Ensure all column names are strings and handle any remaining issues
        df.columns = [f'Column_{i+1}' if pd.isna(col) or str(col).strip() == ''
                      else str(col).strip() for i, col in enumerate(df.columns)]

        # Handle duplicate column names
        df = self._handle_duplicate_columns(df)

        logger.debug(f"Final sanitized DataFrame shape: {df.shape}")
        return df

    def _handle_duplicate_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Handle duplicate column names by renaming them with suffixes.

        Args:
            df: The pandas DataFrame to process

        Returns:
            DataFrame with unique column names
        """
        # Check for duplicate column names
        if df.columns.duplicated().any():
            logger.warning("Duplicate column names detected. Renaming duplicates.")

            # Create a dictionary to track column name counts
            name_counts = {}
            new_columns = []

            for col in df.columns:
                if col in name_counts:
                    name_counts[col] += 1
                    new_col_name = f"{col}_duplicate_{name_counts[col]}"
                    new_columns.append(new_col_name)
                    logger.debug(f"Renamed duplicate column '{col}' to '{new_col_name}'")
                else:
                    name_counts[col] = 0
                    new_columns.append(col)

            df.columns = new_columns

        return df

    @staticmethod
    def _convert_to_numeric(series: pd.Series) -> pd.Series:
        """
        Convert series to numeric, handling common formats like currency and thousand separators.

        Args:
            series: The pandas Series to convert

        Returns:
            The converted pandas Series
        """
        if pd.api.types.is_numeric_dtype(series):
            return series

        try:
            # Handle currency and thousand separators
            cleaned = series.astype(str).str.replace('[$,]', '', regex=True)
            return pd.to_numeric(cleaned, errors='coerce')
        except Exception:
            return series

    @staticmethod
    def _detect_outliers(series: pd.Series) -> Dict:
        """
        Detect outliers in a numeric series using the IQR method.

        Args:
            series: The pandas Series to analyze

        Returns:
            A dictionary with outlier information
        """
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR

        return {
            'lower_bound': lower_bound,
            'upper_bound': upper_bound,
            'outlier_count': len(series[(series < lower_bound) | (series > upper_bound)])
        }

    # Additional validation methods can be added here
    # For example:
    # def validate_column_types(self, df: pd.DataFrame) -> Dict:
    #     """Validate column types and suggest corrections."""
    #     ...

    # def handle_missing_values(self, df: pd.DataFrame, strategy: str = 'drop') -> pd.DataFrame:
    #     """Handle missing values using different strategies."""
    #     ...
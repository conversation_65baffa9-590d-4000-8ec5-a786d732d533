"""
Executive Summary Service.

This module provides functionality for generating executive summaries from chart data.
"""

import json
from typing import Dict, List, Any
from app.core.logging_config import configure_logging
from app.services.data_insights import DataInsights

logger = configure_logging()

class ExecutiveSummaryService:
    """
    Executive Summary service for generating concise summaries from chart data.
    """
    
    def __init__(self):
        """Initialize the executive summary service."""
        self.data_insights = DataInsights()
    
    def generate_summary(self, chart_data_input: Dict[str, Any]) -> List[str]:
        """
        Generate an executive summary from chart data.
        
        Args:
            chart_data_input: Dictionary containing chart data
            
        Returns:
            List of executive summary points
        """
        logger.info(f"Executive summary service called with input type: {type(chart_data_input)}")
        
        # Handle case where no data is provided
        if chart_data_input is None:
            logger.error("No data provided to executive summary service")
            return ["Insufficient data for executive summary: No data provided"]
        
        if not isinstance(chart_data_input, dict):
            logger.error(f"Invalid data format provided to executive summary service: {type(chart_data_input)}")
            return ["Insufficient data for executive summary: Invalid data format"]
        
        # Log the incoming data structure (without the actual data to avoid huge logs)
        data_structure = {
            key: f"<{type(value).__name__} with {len(value) if hasattr(value, '__len__') else 'unknown'} items>"
            for key, value in chart_data_input.items()
        }
        logger.info(f"Processing data with structure: {data_structure}")
        
        # Handle nested chart_data fields (can be multiple levels deep)
        chart_data_input = self._extract_deepest_chart_data(chart_data_input)
        logger.info(f"Extracted deepest chart data with keys: {list(chart_data_input.keys())}")
        
        # Convert chart data to a format suitable for insights generation
        aggregated_data = self._convert_chart_data_to_tabular(chart_data_input)
        
        if not aggregated_data:
            logger.error("Failed to convert chart data to tabular format")
            return ["Insufficient data for executive summary: Data conversion failed"]
        
        # Generate executive summary using the DataInsights service
        summary_points = self.data_insights.generate_executive_summary(aggregated_data)
        logger.info(f"Generated executive summary: {summary_points}")
        
        return summary_points
    
    def _extract_deepest_chart_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract the deepest chart_data from nested structure.
        
        Args:
            data: Dictionary that may contain nested chart_data
            
        Returns:
            Dictionary with the deepest chart_data
        """
        logger.info(f"Extracting chart data, keys: {list(data.keys())}")
        
        if 'chart_data' in data and isinstance(data['chart_data'], dict):
            # Extract chart_id before going deeper
            chart_id = data.get('chart_id', '')
            
            # Go deeper
            result = self._extract_deepest_chart_data(data['chart_data'])
            
            # Add chart_id if not present in the result
            if chart_id and 'chart_id' not in result:
                result['chart_id'] = chart_id
                logger.info(f"Added chart_id to extracted data")
            
            return result
        else:
            # We've reached the deepest level or there's no more chart_data
            logger.info(f"Reached deepest chart_data")
            return data
    
    def _convert_chart_data_to_tabular(self, chart_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert chart data to tabular format for analysis.
        
        Args:
            chart_data: Dictionary containing chart data
            
        Returns:
            Dictionary with data in tabular format
        """
        # Check if this is a chart data object (from UI) or a raw data object
        is_chart_data = 'chart_type' in chart_data or 'type' in chart_data or 'z' in chart_data
        logger.info(f"Is chart data object: {is_chart_data}")
        
        if not is_chart_data:
            # Check if the data is in the expected format with a 'data' field
            if 'data' not in chart_data or not chart_data.get('data'):
                logger.error("Missing 'data' field in input")
                return None
            
            # Check if data is an empty list or not a list
            data = chart_data.get('data')
            if not isinstance(data, list) or len(data) == 0:
                logger.error(f"'data' field is empty or not a list: {type(data)}")
                return None
            
            # Use the data as is
            logger.info("Using raw data format for executive summary")
            return chart_data
        
        # Determine the chart type - could be in 'chart_type' or 'type' field
        chart_type = chart_data.get('chart_type') or chart_data.get('type')
        logger.info(f"Processing chart data object with chart_type: {chart_type}")
        
        # Add chart_type to the input if it's not there but type is
        if not chart_data.get('chart_type') and chart_data.get('type'):
            chart_data['chart_type'] = chart_data.get('type')
        
        # Process different chart types
        if chart_type == 'heatmap':
            return self._process_heatmap_data(chart_data)
        elif chart_type == 'bar':
            return self._process_bar_data(chart_data)
        elif chart_type == 'pie':
            return self._process_pie_data(chart_data)
        elif chart_type == 'line':
            return self._process_line_data(chart_data)
        else:
            logger.warning(f"Chart type {chart_type} not supported for executive summary")
            return None
    
    def _process_heatmap_data(self, chart_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process heatmap chart data."""
        # Extract x, y, and z values
        x_values = chart_data.get('x', [])
        y_values = chart_data.get('y', [])
        z_values = chart_data.get('z', [])
        
        if not x_values or not y_values or not z_values:
            logger.warning("Missing x, y, or z values in heatmap data")
            return None
        
        # Convert heatmap data to tabular format
        data = []
        for i, y_val in enumerate(y_values):
            for j, x_val in enumerate(x_values):
                if i < len(z_values) and j < len(z_values[i]):
                    data.append({
                        'x': x_val,
                        'y': y_val,
                        'value': z_values[i][j]
                    })
        
        # Create a data object suitable for insights generation
        aggregated_data = {'data': data}
        logger.info(f"Converted heatmap data to tabular format with {len(data)} rows")
        
        return aggregated_data
    
    def _process_bar_data(self, chart_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process bar chart data."""
        logger.info("Processing bar chart data")
        
        # Check if there's another level of nesting with chart_data
        if 'chart_data' in chart_data and isinstance(chart_data['chart_data'], dict):
            logger.info("Found nested chart_data in bar chart, using it")
            chart_data_obj = chart_data['chart_data']
        else:
            chart_data_obj = chart_data
        
        # Extract x and y values
        x_values = chart_data_obj.get('x', [])
        y_values = chart_data_obj.get('y', [])
        
        # If y is missing but we have x, try to create a count-based dataset
        if x_values and not y_values:
            logger.info("y_values missing but x_values present, creating count-based dataset")
            
            # Count occurrences of each x value
            from collections import Counter
            counts = Counter(x_values)
            
            # Create new x and y values
            x_values = list(counts.keys())
            y_values = list(counts.values())
        
        if not x_values:
            logger.error("Missing x values in bar chart data")
            return None
        
        if not y_values and x_values:
            # If we still don't have y values but have x values, create dummy y values
            logger.warning("Missing y values in bar chart data, creating dummy values")
            y_values = [1] * len(x_values)
        
        if len(x_values) != len(y_values):
            logger.warning(f"Mismatched x and y values in bar chart data: x={len(x_values)}, y={len(y_values)}")
            # Adjust lengths to match
            min_len = min(len(x_values), len(y_values))
            x_values = x_values[:min_len]
            y_values = y_values[:min_len]
        
        # Convert bar chart data to tabular format
        data = []
        for i in range(len(x_values)):
            data.append({
                'category': x_values[i],
                'value': y_values[i]
            })
        
        # Get chart metadata if available
        summary = chart_data.get('summary', {})
        title = summary.get('title', 'Bar Chart')
        x_axis = summary.get('x_axis', 'Category')
        y_axis = summary.get('y_axis', 'Value')
        
        # Add metadata to the aggregated data
        aggregated_data = {
            'data': data,
            'metadata': {
                'title': title,
                'x_axis': x_axis,
                'y_axis': y_axis
            }
        }
        
        logger.info(f"Converted bar chart data to tabular format with {len(data)} rows")
        
        return aggregated_data
    
    def _process_pie_data(self, chart_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process pie chart data."""
        # Extract labels and values
        labels = chart_data.get('labels', [])
        values = chart_data.get('values', [])
        
        if not labels or not values or len(labels) != len(values):
            logger.warning("Missing or mismatched labels and values in pie chart data")
            return None
        
        # Convert pie chart data to tabular format
        data = []
        for i in range(len(labels)):
            data.append({
                'category': labels[i],
                'value': values[i]
            })
        
        # Create a data object suitable for insights generation
        aggregated_data = {'data': data}
        logger.info(f"Converted pie chart data to tabular format with {len(data)} rows")
        
        return aggregated_data
    
    def _process_line_data(self, chart_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process line chart data."""
        # Extract x and y values
        x_values = chart_data.get('x', [])
        y_values = chart_data.get('y', [])
        
        if not x_values or not y_values or len(x_values) != len(y_values):
            logger.warning("Missing or mismatched x and y values in line chart data")
            return None
        
        # Convert line chart data to tabular format
        data = []
        for i in range(len(x_values)):
            data.append({
                'x': x_values[i],
                'y': y_values[i]
            })
        
        # Create a data object suitable for insights generation
        aggregated_data = {'data': data}
        logger.info(f"Converted line chart data to tabular format with {len(data)} rows")
        
        return aggregated_data

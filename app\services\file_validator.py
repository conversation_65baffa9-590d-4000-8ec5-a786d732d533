"""
File Validation Service - Clean Architecture Implementation

This service provides comprehensive file validation functionality including:
- File size validation with configurable limits
- File type validation with configurable allowed types
- File content validation for security
- Performance-optimized validation with early failure detection

The service follows clean code principles with:
- Single responsibility principle
- Proper error handling with custom exceptions
- Configurable validation rules
- Comprehensive logging for debugging and monitoring
"""

import os
from typing import Optional
from fastapi import UploadFile
from app.core.config import settings
from app.core.exceptions import FileSizeExceededError, InvalidFileTypeError, FileUploadError
from app.core.logging_config import configure_logging

# Configure logger
logger = configure_logging()


class FileValidator:
    """
    File validation service with configurable rules and comprehensive error handling.
    
    This service validates uploaded files against size and type constraints
    defined in the application configuration.
    """

    def __init__(self):
        """Initialize file validator with configuration settings."""
        self.max_file_size_bytes = settings.max_file_size_bytes
        self.max_file_size_mb = settings.MAX_FILE_SIZE_MB
        self.allowed_file_types = settings.allowed_file_types_list
        
        logger.debug(f"FileValidator initialized: max_size={self.max_file_size_mb}MB, allowed_types={self.allowed_file_types}")

    async def validate_file(self, file: UploadFile) -> None:
        """
        Comprehensive file validation including size, type, and basic security checks.
        
        Args:
            file: FastAPI UploadFile object to validate
            
        Raises:
            FileSizeExceededError: If file size exceeds configured limit
            InvalidFileTypeError: If file type is not in allowed list
            FileUploadError: If file is invalid or corrupted
        """
        try:
            # Validate file object
            if not file or not file.filename:
                raise FileUploadError("No file provided or filename is missing")
            
            logger.info(f"Validating file: {file.filename}")
            
            # Validate file type first (fast check)
            self._validate_file_type(file.filename)
            
            # Validate file size (requires reading file)
            await self._validate_file_size(file)
            
            # Additional security validation
            self._validate_file_security(file.filename)
            
            logger.info(f"File validation successful: {file.filename}")
            
        except (FileSizeExceededError, InvalidFileTypeError, FileUploadError):
            # Re-raise custom exceptions
            raise
        except Exception as e:
            logger.error(f"Unexpected error during file validation: {str(e)}")
            raise FileUploadError(f"File validation failed: {str(e)}")

    def _validate_file_type(self, filename: str) -> None:
        """
        Validate file type based on file extension.
        
        Args:
            filename: Name of the file to validate
            
        Raises:
            InvalidFileTypeError: If file type is not allowed
        """
        if not filename:
            raise InvalidFileTypeError("Filename is required for type validation")
        
        # Extract file extension
        file_extension = self._get_file_extension(filename)
        
        if not file_extension:
            raise InvalidFileTypeError("File must have a valid extension")
        
        # Check if file type is allowed
        if file_extension not in self.allowed_file_types:
            logger.warning(f"Invalid file type attempted: {file_extension} for file: {filename}")
            raise InvalidFileTypeError(
                file_type=file_extension,
                allowed_types=self.allowed_file_types
            )
        
        logger.debug(f"File type validation passed: {file_extension}")

    async def _validate_file_size(self, file: UploadFile) -> None:
        """
        Validate file size against configured limits.
        
        Args:
            file: FastAPI UploadFile object to validate
            
        Raises:
            FileSizeExceededError: If file size exceeds limit
        """
        try:
            # Read file content to get actual size
            file_content = await file.read()
            file_size_bytes = len(file_content)
            file_size_mb = file_size_bytes / (1024 * 1024)
            
            # Reset file pointer for subsequent reads
            await file.seek(0)
            
            logger.debug(f"File size: {file_size_mb:.2f}MB ({file_size_bytes} bytes)")
            
            # Check size limit
            if file_size_bytes > self.max_file_size_bytes:
                logger.warning(f"File size exceeded: {file_size_mb:.1f}MB > {self.max_file_size_mb}MB for file: {file.filename}")
                raise FileSizeExceededError(
                    max_size_mb=self.max_file_size_mb,
                    actual_size_mb=file_size_mb
                )
            
            logger.debug(f"File size validation passed: {file_size_mb:.2f}MB")
            
        except FileSizeExceededError:
            # Re-raise size exceeded error
            raise
        except Exception as e:
            logger.error(f"Error reading file for size validation: {str(e)}")
            raise FileUploadError(f"Unable to validate file size: {str(e)}")

    def _validate_file_security(self, filename: str) -> None:
        """
        Basic security validation to prevent malicious file uploads.
        
        Args:
            filename: Name of the file to validate
            
        Raises:
            FileUploadError: If file fails security checks
        """
        # Check for suspicious file patterns
        suspicious_patterns = [
            '..',  # Directory traversal
            '/',   # Path separator
            '\\',  # Windows path separator
            '\x00',  # Null byte
        ]
        
        for pattern in suspicious_patterns:
            if pattern in filename:
                logger.warning(f"Suspicious filename pattern detected: {filename}")
                raise FileUploadError(f"Filename contains invalid characters: {filename}")
        
        # Check filename length
        if len(filename) > 255:
            logger.warning(f"Filename too long: {len(filename)} characters")
            raise FileUploadError("Filename is too long (maximum 255 characters)")
        
        logger.debug(f"File security validation passed: {filename}")

    def _get_file_extension(self, filename: str) -> str:
        """
        Extract and normalize file extension from filename.
        
        Args:
            filename: Name of the file
            
        Returns:
            Lowercase file extension without the dot
        """
        if not filename or '.' not in filename:
            return ""
        
        extension = os.path.splitext(filename)[1].lower()
        # Remove the leading dot
        return extension[1:] if extension.startswith('.') else extension

    def get_validation_info(self) -> dict:
        """
        Get current validation configuration for debugging/info purposes.
        
        Returns:
            Dictionary with validation settings
        """
        return {
            "max_file_size_mb": self.max_file_size_mb,
            "max_file_size_bytes": self.max_file_size_bytes,
            "allowed_file_types": self.allowed_file_types,
            "validation_enabled": True
        }

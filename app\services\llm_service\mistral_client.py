from mistralai import Mistral
from app.core.config import settings
from app.core.logging_config import configure_logging

logger = configure_logging()

api_key = settings.MISTRAL_API_KEY
model = settings.MISTRAL_MODEL

client = Mistral(api_key=api_key)

def get_chat_response(system_prompt):
    chat_response = client.chat.complete(
        model=model,
        messages=[
            {
                "role": "system",
                "content": system_prompt,
            },
        ]
    )
    return chat_response.choices[0].message.content
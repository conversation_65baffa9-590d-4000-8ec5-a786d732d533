import requests
import yaml
import json

class OpenAIChatAPI:
    def __init__(self, config_path='config/config.yaml'):
        self.load_config(config_path)

    def load_config(self, config_path):
        """Load configuration from a YAML file."""
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)
            self.api_key = config['openai']['api_key']
            self.client_id = config['openai']['client_id']
            self.domain = config['openai']['domain']
            self.context_path = config['openai']['context_path']
            self.api_version = config['openai']['api_version']
            self.user_id = config['openai']['user_id']

    def call_api(self, system_prompt):
        """Makes a call to the OpenAI chat completions API."""
        url = f'{self.domain}/{self.context_path}/chat/completions?api-version={self.api_version}'
        headers = {
            'api-key': self.api_key,
            'client_id': self.client_id,
            'Content-Type': 'application/json',
            'user-id': self.user_id,
        }
        
        payload = {
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                }
            ]
        }

        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code == 200:
            return response.json()  # Return the entire JSON object
        else:
            response.raise_for_status()  # Raise an error for unsuccessful responses
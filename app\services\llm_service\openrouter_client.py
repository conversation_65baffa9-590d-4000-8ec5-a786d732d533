from openai import OpenAI
from app.core.config import settings
from app.core.logging_config import configure_logging

logger = configure_logging()

api_key = settings.OPENROUTER_API_KEY
model = settings.OPENROUTER_MODEL


client = OpenAI(
  base_url="https://openrouter.ai/api/v1",
  api_key=api_key,
)

def get_chat_completion(system_prompt):
    completion = client.chat.completions.create(
        extra_headers={
            "HTTP-Referer": "", # Optional. Site URL for rankings on openrouter.ai.
            "X-Title": "", # Optional. Site title for rankings on openrouter.ai.
        },
        model=model, #"google/gemini-2.5-pro-exp-03-25",
        messages=[
            {
                "role": "system",
                "content": system_prompt,
            }
        ]
    )
    return completion.choices[0].message.content


# app/utils/exception_handler.py
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
from app.core.exceptions import (
    CustomException, SecurityError, LLMRequestError,
    ChartInsightsError, InsufficientDataError, InvalidChartDataError,
    UnsupportedChartTypeError, ChartInsightsLLMError
)

def general_exception_handler(request: Request, exc: Exception):
    """Handles unexpected system errors (error_code: 1000)."""
    return JSONResponse(
        status_code=500,
        content={
            "error_code": 1000,
            "error_type": "TECHNICAL",
            "error_message": "An unexpected error occurred."
        }
    )

def custom_exception_handler(request: Request, exc: CustomException):
    """Handles business exceptions (error_code: 2000-8999)."""
    return JSONResponse(
        status_code=400,
        content={
            "error_code": exc.error_code,
            "error_type": "BUSINESS",
            "error_message": exc.message
        }
    )

def security_exception_handler(request: Request, exc: SecurityError):
    """Handles security-related errors (error_code: 9000)."""
    return JSONResponse(
        status_code=403,
        content={
            "error_code": exc.error_code,
            "error_type": "SECURITY",
            "error_message": exc.message
        }
    )

def llm_request_exception_handler(request: Request, exc: LLMRequestError):
    """Handles errors related to LLM API failures (error_code: 3000)."""
    return JSONResponse(
        status_code=502,  # Bad Gateway (external API failure)
        content={
            "error_code": exc.code,
            "error_type": exc.error_type,
            "error_message": str(exc)
        }
    )

def insufficient_data_exception_handler(request: Request, exc: InsufficientDataError):
    """Handles insufficient data errors (error_code: 4001)."""
    return JSONResponse(
        status_code=422,  # Unprocessable Entity
        content={
            "error_code": exc.code,
            "error_type": exc.error_type,
            "error_message": str(exc)
        }
    )

def invalid_chart_data_exception_handler(request: Request, exc: InvalidChartDataError):
    """Handles invalid chart data errors (error_code: 4002)."""
    return JSONResponse(
        status_code=400,  # Bad Request
        content={
            "error_code": exc.code,
            "error_type": exc.error_type,
            "error_message": str(exc)
        }
    )

def unsupported_chart_type_exception_handler(request: Request, exc: UnsupportedChartTypeError):
    """Handles unsupported chart type errors (error_code: 4003)."""
    return JSONResponse(
        status_code=400,  # Bad Request
        content={
            "error_code": exc.code,
            "error_type": exc.error_type,
            "error_message": str(exc)
        }
    )

def chart_insights_llm_exception_handler(request: Request, exc: ChartInsightsLLMError):
    """Handles chart insights LLM errors (error_code: 4004)."""
    return JSONResponse(
        status_code=503,  # Service Unavailable
        content={
            "error_code": exc.code,
            "error_type": exc.error_type,
            "error_message": str(exc)
        }
    )

def register_exception_handlers(app: FastAPI):
    """Registers all custom exception handlers."""
    app.add_exception_handler(Exception, general_exception_handler)
    app.add_exception_handler(CustomException, custom_exception_handler)
    app.add_exception_handler(SecurityError, security_exception_handler)
    app.add_exception_handler(LLMRequestError, llm_request_exception_handler)

    # Chart insights specific exception handlers
    app.add_exception_handler(InsufficientDataError, insufficient_data_exception_handler)
    app.add_exception_handler(InvalidChartDataError, invalid_chart_data_exception_handler)
    app.add_exception_handler(UnsupportedChartTypeError, unsupported_chart_type_exception_handler)
    app.add_exception_handler(ChartInsightsLLMError, chart_insights_llm_exception_handler)

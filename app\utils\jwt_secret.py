import secrets

def generate_jwt_keys():
    # Generate a random 32-byte JWT secret key
    jwt_secret_key = secrets.token_hex(32)
    # Generate a random 32-byte refresh token key
    refresh_token_key = secrets.token_hex(32)

    return jwt_secret_key, refresh_token_key

if __name__ == '__main__':
    jwt_secret, refresh_secret = generate_jwt_keys()
    print(f"JWT Secret Key: {jwt_secret}")
    print(f"Refresh Token Key: {refresh_secret}")
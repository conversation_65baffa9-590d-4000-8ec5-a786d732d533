# UI Integration Guide for Common Headers System

This guide provides detailed instructions for integrating the common headers system with your frontend applications (<PERSON>act, Vue, Angular, etc.).

## Overview

The backend now automatically processes standard HTTP headers from all requests. Your UI application should send these headers to enable:
- **Request tracking** across the entire user journey
- **Enhanced analytics** and user behavior insights
- **Better error debugging** and support
- **Consistent user experience** tracking

## Required Headers

### 1. Request Tracking Headers

```javascript
// Generate unique IDs for tracking
const headers = {
    'x-request-id': crypto.randomUUID(),           // Unique per request
    'x-correlation-id': getCorrelationId(),        // Links related requests
    'x-session-id': getSessionId(),                // User session identifier
    'x-device-id': getDeviceId()                   // Device/browser identifier
};
```

### 2. Client Information Headers

```javascript
const headers = {
    'x-client-version': '1.2.3',                  // Your app version
    'x-platform': 'web',                          // web, mobile, desktop
    'x-timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
    'accept-language': navigator.language || 'en-US'
};
```

### 3. Standard HTTP Headers

```javascript
const headers = {
    'user-agent': navigator.userAgent,             // Automatically sent by browser
    'content-type': 'application/json',
    'accept': 'application/json'
};
```

## Implementation Examples

### React Application

```javascript
// utils/apiClient.js
import axios from 'axios';

class ApiClient {
    constructor() {
        this.sessionId = this.getOrCreateSessionId();
        this.deviceId = this.getOrCreateDeviceId();
        this.correlationId = this.generateCorrelationId();
        
        // Create axios instance with interceptors
        this.client = axios.create({
            baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/v1',
            timeout: 30000
        });
        
        // Request interceptor to add common headers
        this.client.interceptors.request.use(this.addCommonHeaders.bind(this));
        
        // Response interceptor for error handling
        this.client.interceptors.response.use(
            response => response,
            error => this.handleError(error)
        );
    }
    
    addCommonHeaders(config) {
        const commonHeaders = {
            'x-request-id': crypto.randomUUID(),
            'x-correlation-id': this.correlationId,
            'x-session-id': this.sessionId,
            'x-device-id': this.deviceId,
            'x-client-version': process.env.REACT_APP_VERSION || '1.0.0',
            'x-platform': 'web',
            'x-timezone': Intl.DateTimeFormat().resolvedOptions().timeZone
        };
        
        config.headers = {
            ...commonHeaders,
            ...config.headers
        };
        
        return config;
    }
    
    getOrCreateSessionId() {
        let sessionId = sessionStorage.getItem('sessionId');
        if (!sessionId) {
            sessionId = 'session-' + crypto.randomUUID();
            sessionStorage.setItem('sessionId', sessionId);
        }
        return sessionId;
    }
    
    getOrCreateDeviceId() {
        let deviceId = localStorage.getItem('deviceId');
        if (!deviceId) {
            deviceId = 'device-' + crypto.randomUUID();
            localStorage.setItem('deviceId', deviceId);
        }
        return deviceId;
    }
    
    generateCorrelationId() {
        return 'corr-' + crypto.randomUUID();
    }
    
    handleError(error) {
        console.error('API Error:', {
            requestId: error.config?.headers?.['x-request-id'],
            url: error.config?.url,
            status: error.response?.status,
            message: error.message
        });
        return Promise.reject(error);
    }
    
    // Authentication methods
    setAuthToken(token) {
        if (token) {
            this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        } else {
            delete this.client.defaults.headers.common['Authorization'];
        }
    }
    
    // API methods
    async uploadFile(file, onProgress) {
        const formData = new FormData();
        formData.append('file', file);
        
        return this.client.post('/data/file-upload', formData, {
            headers: {
                'content-type': 'multipart/form-data'
            },
            onUploadProgress: onProgress
        });
    }
    
    async generateChartInsights(chartData) {
        return this.client.post('/chart/insights', {
            chart_data: chartData.data,
            chart_type: chartData.type,
            chart_title: chartData.title,
            include_executive_summary: true,
            include_data_insights: true,
            include_hidden_patterns: true,
            include_recommendations: true
        });
    }
    
    async trackAnalyticsEvent(eventData) {
        return this.client.post('/example/analytics', eventData);
    }
}

export default new ApiClient();
```

### Vue.js Application

```javascript
// plugins/api.js
import axios from 'axios';

class VueApiClient {
    constructor() {
        this.setupClient();
        this.setupHeaders();
    }
    
    setupClient() {
        this.client = axios.create({
            baseURL: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000/v1'
        });
        
        this.client.interceptors.request.use(config => {
            config.headers = {
                ...this.getCommonHeaders(),
                ...config.headers
            };
            return config;
        });
    }
    
    setupHeaders() {
        this.sessionId = this.getStoredValue('sessionId', 'session') || this.generateId('session');
        this.deviceId = this.getStoredValue('deviceId', 'device', true) || this.generateId('device');
        
        // Store in appropriate storage
        sessionStorage.setItem('sessionId', this.sessionId);
        localStorage.setItem('deviceId', this.deviceId);
    }
    
    getCommonHeaders() {
        return {
            'x-request-id': this.generateId('req'),
            'x-correlation-id': this.generateId('corr'),
            'x-session-id': this.sessionId,
            'x-device-id': this.deviceId,
            'x-client-version': process.env.VUE_APP_VERSION || '1.0.0',
            'x-platform': 'web',
            'x-timezone': Intl.DateTimeFormat().resolvedOptions().timeZone
        };
    }
    
    getStoredValue(key, prefix, persistent = false) {
        const storage = persistent ? localStorage : sessionStorage;
        return storage.getItem(key);
    }
    
    generateId(prefix) {
        return `${prefix}-${crypto.randomUUID()}`;
    }
}

// Vue plugin
export default {
    install(app) {
        const apiClient = new VueApiClient();
        app.config.globalProperties.$api = apiClient.client;
        app.provide('api', apiClient.client);
    }
};
```

### Angular Application

```typescript
// services/api.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpInterceptor, HttpRequest, HttpHandler } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class ApiService {
    private baseUrl = environment.apiBaseUrl || 'http://localhost:8000/v1';
    private sessionId: string;
    private deviceId: string;
    
    constructor(private http: HttpClient) {
        this.initializeIds();
    }
    
    private initializeIds() {
        this.sessionId = sessionStorage.getItem('sessionId') || this.generateId('session');
        this.deviceId = localStorage.getItem('deviceId') || this.generateId('device');
        
        sessionStorage.setItem('sessionId', this.sessionId);
        localStorage.setItem('deviceId', this.deviceId);
    }
    
    private generateId(prefix: string): string {
        return `${prefix}-${crypto.randomUUID()}`;
    }
    
    private getCommonHeaders(): HttpHeaders {
        return new HttpHeaders({
            'x-request-id': this.generateId('req'),
            'x-correlation-id': this.generateId('corr'),
            'x-session-id': this.sessionId,
            'x-device-id': this.deviceId,
            'x-client-version': environment.version || '1.0.0',
            'x-platform': 'web',
            'x-timezone': Intl.DateTimeFormat().resolvedOptions().timeZone
        });
    }
    
    uploadFile(file: File): Observable<any> {
        const formData = new FormData();
        formData.append('file', file);
        
        return this.http.post(`${this.baseUrl}/data/file-upload`, formData, {
            headers: this.getCommonHeaders()
        });
    }
    
    generateChartInsights(chartData: any): Observable<any> {
        return this.http.post(`${this.baseUrl}/chart/insights`, chartData, {
            headers: this.getCommonHeaders()
        });
    }
}

// HTTP Interceptor for automatic header injection
@Injectable()
export class CommonHeadersInterceptor implements HttpInterceptor {
    intercept(req: HttpRequest<any>, next: HttpHandler) {
        const sessionId = sessionStorage.getItem('sessionId') || 'session-' + crypto.randomUUID();
        const deviceId = localStorage.getItem('deviceId') || 'device-' + crypto.randomUUID();
        
        const headersReq = req.clone({
            setHeaders: {
                'x-request-id': 'req-' + crypto.randomUUID(),
                'x-session-id': sessionId,
                'x-device-id': deviceId,
                'x-client-version': environment.version || '1.0.0',
                'x-platform': 'web',
                'x-timezone': Intl.DateTimeFormat().resolvedOptions().timeZone
            }
        });
        
        return next.handle(headersReq);
    }
}
```

## Mobile App Integration

### React Native

```javascript
// utils/ApiClient.js
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';

class MobileApiClient {
    constructor() {
        this.baseURL = 'http://localhost:8000/v1';
        this.initializeIds();
    }
    
    async initializeIds() {
        this.deviceId = await this.getOrCreateDeviceId();
        this.sessionId = await this.getOrCreateSessionId();
    }
    
    async getOrCreateDeviceId() {
        let deviceId = await AsyncStorage.getItem('deviceId');
        if (!deviceId) {
            deviceId = await DeviceInfo.getUniqueId();
            await AsyncStorage.setItem('deviceId', deviceId);
        }
        return deviceId;
    }
    
    async getOrCreateSessionId() {
        let sessionId = await AsyncStorage.getItem('sessionId');
        if (!sessionId) {
            sessionId = 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            await AsyncStorage.setItem('sessionId', sessionId);
        }
        return sessionId;
    }
    
    async getCommonHeaders() {
        const version = await DeviceInfo.getVersion();
        const timezone = await DeviceInfo.getTimezone();
        
        return {
            'x-request-id': 'req-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
            'x-session-id': this.sessionId,
            'x-device-id': this.deviceId,
            'x-client-version': version,
            'x-platform': Platform.OS,
            'x-timezone': timezone,
            'content-type': 'application/json'
        };
    }
    
    async makeRequest(endpoint, options = {}) {
        const headers = await this.getCommonHeaders();
        
        const response = await fetch(`${this.baseURL}${endpoint}`, {
            ...options,
            headers: {
                ...headers,
                ...options.headers
            }
        });
        
        return response.json();
    }
}

export default new MobileApiClient();
```

## Testing Your Integration

### 1. Test Common Headers

```javascript
// Test that headers are being sent correctly
async function testHeaders() {
    try {
        const response = await apiClient.get('/example/headers-summary');
        console.log('Headers received by server:', response.data.available_headers);
    } catch (error) {
        console.error('Header test failed:', error);
    }
}
```

### 2. Test Authentication with Headers

```javascript
async function testAuthWithHeaders() {
    try {
        // Login first
        const loginResponse = await apiClient.post('/auth/google', {
            token: 'your-google-token'
        });
        
        // Set auth token
        apiClient.setAuthToken(loginResponse.data.access_token);
        
        // Test authenticated endpoint
        const protectedResponse = await apiClient.get('/example/authenticated');
        console.log('Authenticated request successful:', protectedResponse.data);
    } catch (error) {
        console.error('Auth test failed:', error);
    }
}
```

### 3. Test Analytics Integration

```javascript
async function testAnalytics() {
    const eventData = {
        event_type: 'page_view',
        page: '/dashboard',
        timestamp: new Date().toISOString(),
        metadata: {
            source: 'ui_test'
        }
    };
    
    try {
        const response = await apiClient.post('/example/analytics', eventData);
        console.log('Analytics event tracked:', response.data);
    } catch (error) {
        console.error('Analytics test failed:', error);
    }
}
```

## Best Practices

### 1. Header Management
- **Generate unique request IDs** for each API call
- **Persist device ID** across app sessions
- **Regenerate session ID** on app restart
- **Include app version** for debugging

### 2. Error Handling
- **Log request IDs** with errors for easier debugging
- **Include client context** in error reports
- **Handle network failures** gracefully

### 3. Performance
- **Cache device/session IDs** to avoid regeneration
- **Use interceptors** to avoid manual header management
- **Minimize header size** while including necessary information

### 4. Privacy
- **Don't include sensitive data** in headers
- **Use UUIDs** instead of personally identifiable information
- **Respect user privacy settings**

## Troubleshooting

### Common Issues

1. **Headers not appearing in server logs**
   - Check that interceptors are properly configured
   - Verify header names match expected format
   - Ensure headers are being sent with every request

2. **CORS issues with custom headers**
   - Add custom headers to CORS allowed headers
   - Ensure preflight requests include necessary headers

3. **Authentication failures**
   - Verify Authorization header format: `Bearer <token>`
   - Check token expiration and refresh logic
   - Ensure auth headers are included with protected requests

### Debug Endpoints

Use these endpoints to verify your integration:

- `GET /v1/example/public` - Test basic header processing
- `GET /v1/example/headers-summary` - See all processed headers
- `GET /v1/health` - Test health check with headers
- `GET /v1/docs` - Interactive API documentation

This integration will provide comprehensive request tracking, better analytics, and improved debugging capabilities across your entire application! 🚀

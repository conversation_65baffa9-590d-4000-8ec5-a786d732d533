# Analytics Implementation Guide

This document explains the Google Sheets-based analytics implementation for user tracking and event monitoring.

## Overview

The analytics system tracks user profiles and events using Google Sheets as the initial storage solution. This approach allows for quick implementation and easy data access before migrating to a database when user traffic increases.

## Architecture

### Components

1. **Google Sheets Service** (`app/services/analytics/google_sheets_service.py`)
   - Handles Google Sheets API integration
   - Manages authentication with service account credentials
   - Provides methods for reading/writing data to sheets

2. **User Profile Service** (`app/services/analytics/user_profile_service.py`)
   - Tracks user login information
   - Manages user profile data (email, login dates, counts, etc.)
   - Handles async user tracking

3. **Event Tracking Service** (`app/services/analytics/event_tracking_service.py`)
   - Tracks various user events (file uploads, chart interactions, etc.)
   - Provides async event tracking
   - Supports different event types with metadata

4. **Analytics API** (`app/api/v1/endpoints/analytics.py`)
   - REST endpoints for event tracking from UI
   - User profile retrieval
   - Health check for analytics services

## Google Sheets Setup

### 1. Create Google Sheets

Create two Google Sheets:

1. **User Profile Sheet** - Tracks user information
   - Columns: email, first_login_date, last_login_date, login_count, user_agent, ip_address, location, name, picture, updated_at

2. **Events Sheet** - Tracks user events
   - Columns: event_id, email, event_type, timestamp, metadata, user_agent, ip_address, session_id, page_url, referrer

### 2. Google Service Account Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google Sheets API
4. Create a Service Account:
   - Go to IAM & Admin > Service Accounts
   - Click "Create Service Account"
   - Fill in details and create
   - Generate JSON key file
5. Share your Google Sheets with the service account email (with Editor permissions)

### 3. Environment Configuration

Add the following to your `.env` file:

```env
# Google Sheets Analytics Configuration
GOOGLE_SHEETS_CREDENTIALS_FILE=path/to/your/service-account-credentials.json
GOOGLE_SHEETS_USER_PROFILE_SHEET_ID=your_user_profile_sheet_id_here
GOOGLE_SHEETS_EVENTS_SHEET_ID=your_events_sheet_id_here
ENABLE_ANALYTICS=true
```

## Event Types

The system supports the following event types:

- `USER_LOGIN` - User authentication events
- `FILE_UPLOAD` - CSV/Excel file uploads
- `CHART_SAVE` - Chart saving actions
- `CHART_DOWNLOAD` - Chart download actions
- `CHART_VIEW` - Chart viewing events
- `ADDITIONAL_CHARTS_VIEW` - Additional charts viewing
- `EXECUTIVE_SUMMARY_VIEW` - Executive summary viewing
- `CHART_UPDATE` - Chart modification events
- `PAGE_VIEW` - Page navigation events
- `SESSION_START` - Session initiation
- `SESSION_END` - Session termination

## API Endpoints

### Authentication Integration

The authentication endpoint automatically tracks user logins:

```http
POST /v1/auth/google
```

**Request Body:**
```json
{
  "token": "google_oauth_token",
  "user_agent": "Mozilla/5.0...",
  "ip_address": "***********",
  "location": "New York, US",
  "session_id": "session_123"
}
```

### Event Tracking

#### General Event Tracking
```http
POST /v1/analytics/events
Authorization: Bearer <access_token>
```

**Request Body:**
```json
{
  "event_type": "CHART_VIEW",
  "metadata": {
    "chart_type": "bar",
    "chart_id": "chart_123"
  },
  "session_id": "session_123",
  "page_url": "https://app.example.com/charts",
  "user_agent": "Mozilla/5.0...",
  "ip_address": "***********"
}
```

#### File Upload Event
```http
POST /v1/analytics/events/file-upload
Authorization: Bearer <access_token>
```

**Request Body:**
```json
{
  "file_name": "data.csv",
  "file_size": 1024000,
  "file_type": "csv",
  "session_id": "session_123",
  "user_agent": "Mozilla/5.0...",
  "ip_address": "***********"
}
```

#### Chart Event
```http
POST /v1/analytics/events/chart
Authorization: Bearer <access_token>
```

**Request Body:**
```json
{
  "event_type": "CHART_SAVE",
  "chart_type": "pie",
  "chart_id": "chart_456",
  "session_id": "session_123",
  "user_agent": "Mozilla/5.0...",
  "ip_address": "***********"
}
```

#### Page View Event
```http
POST /v1/analytics/events/page-view
Authorization: Bearer <access_token>
```

**Request Body:**
```json
{
  "page_url": "https://app.example.com/dashboard",
  "page_title": "Dashboard",
  "session_id": "session_123",
  "user_agent": "Mozilla/5.0...",
  "ip_address": "***********",
  "referrer": "https://google.com"
}
```

### User Profile

#### Get User Profile
```http
GET /v1/analytics/user-profile
Authorization: Bearer <access_token>
```

### Health Check

#### Analytics Health Check
```http
GET /v1/analytics/health
```

## Frontend Integration

### 1. Authentication Tracking

When users sign in, send additional data:

```javascript
const loginData = {
  token: googleToken,
  user_agent: navigator.userAgent,
  session_id: generateSessionId(),
  // location can be obtained from geolocation API or IP service
};

await fetch('/v1/auth/google', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(loginData)
});
```

### 2. Event Tracking

Create an analytics service in your React app:

```javascript
class AnalyticsService {
  constructor(accessToken) {
    this.accessToken = accessToken;
    this.sessionId = this.generateSessionId();
  }

  async trackEvent(eventType, metadata = {}) {
    try {
      await fetch('/v1/analytics/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.accessToken}`
        },
        body: JSON.stringify({
          event_type: eventType,
          metadata,
          session_id: this.sessionId,
          page_url: window.location.href,
          user_agent: navigator.userAgent
        })
      });
    } catch (error) {
      console.error('Analytics tracking failed:', error);
    }
  }

  async trackFileUpload(fileName, fileSize, fileType) {
    try {
      await fetch('/v1/analytics/events/file-upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.accessToken}`
        },
        body: JSON.stringify({
          file_name: fileName,
          file_size: fileSize,
          file_type: fileType,
          session_id: this.sessionId,
          user_agent: navigator.userAgent
        })
      });
    } catch (error) {
      console.error('File upload tracking failed:', error);
    }
  }

  async trackChartEvent(eventType, chartType, chartId = null) {
    try {
      await fetch('/v1/analytics/events/chart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.accessToken}`
        },
        body: JSON.stringify({
          event_type: eventType,
          chart_type: chartType,
          chart_id: chartId,
          session_id: this.sessionId,
          user_agent: navigator.userAgent
        })
      });
    } catch (error) {
      console.error('Chart event tracking failed:', error);
    }
  }

  generateSessionId() {
    return 'session_' + Math.random().toString(36).substr(2, 9);
  }
}
```

### 3. Usage Examples

```javascript
// Initialize analytics
const analytics = new AnalyticsService(accessToken);

// Track file upload
const handleFileUpload = async (file) => {
  // Upload file logic...
  await analytics.trackFileUpload(file.name, file.size, file.type);
};

// Track chart save
const handleChartSave = async (chartType, chartId) => {
  // Save chart logic...
  await analytics.trackChartEvent('CHART_SAVE', chartType, chartId);
};

// Track chart download
const handleChartDownload = async (chartType, chartId) => {
  // Download chart logic...
  await analytics.trackChartEvent('CHART_DOWNLOAD', chartType, chartId);
};

// Track page views
const trackPageView = async (pageTitle) => {
  await analytics.trackEvent('PAGE_VIEW', { page_title: pageTitle });
};
```

## Data Structure

### User Profile Sheet Structure
| Column | Description | Example |
|--------|-------------|---------|
| email | User's email address | <EMAIL> |
| first_login_date | Date of first login | 2024-01-15T10:30:00Z |
| last_login_date | Date of last login | 2024-01-20T14:45:00Z |
| login_count | Total number of logins | 5 |
| user_agent | Browser/device info | Mozilla/5.0... |
| ip_address | User's IP address | *********** |
| location | User's location | New York, US |
| name | User's full name | John Doe |
| picture | Profile picture URL | https://... |
| updated_at | Last update timestamp | 2024-01-20T14:45:00Z |

### Events Sheet Structure
| Column | Description | Example |
|--------|-------------|---------|
| event_id | Unique event identifier | uuid-string |
| email | User's email address | <EMAIL> |
| event_type | Type of event | CHART_SAVE |
| timestamp | When event occurred | 2024-01-20T14:45:00Z |
| metadata | Event-specific data (JSON) | {"chart_type": "bar"} |
| user_agent | Browser/device info | Mozilla/5.0... |
| ip_address | User's IP address | *********** |
| session_id | Session identifier | session_123 |
| page_url | URL where event occurred | https://app.com/charts |
| referrer | Referrer URL | https://google.com |

## Migration to Database

When ready to migrate from Google Sheets to a database:

1. Create database tables matching the sheet structures
2. Implement database service classes
3. Update service dependencies
4. Migrate existing data from sheets to database
5. Update configuration to use database instead of sheets

The current implementation is designed to make this migration straightforward by abstracting the storage layer through service classes.

# Chart Groups Feature

This document explains the new `chart_group` field that has been added to the chart recommendations API response.

## Overview

The `chart_group` field categorizes charts based on their specialized recommender type, making it easier for the UI to organize and display charts in logical groups.

## Chart Groups

The following chart groups are available:

1. **basic** - Basic categorical charts
2. **numerical** - Numerical distribution and scatter charts  
3. **time_series** - Time-based charts
4. **box_plot** - Distribution analysis charts
5. **heatmap** - Correlation and intensity charts
6. **stacked** - Stacked visualization charts
7. **multi** - Multi-series charts
8. **multi_group** - Multi-group comparison charts
9. **hierarchical** - Hierarchical data charts

## Chart Type Mapping

### Basic Charts (`basic`)
- `pie` - Pie chart
- `doughnut` - Doughnut chart
- `semi_circle` - Semi-circle chart
- `bar` - Bar chart

### Numerical Charts (`numerical`)
- `histogram` - Histogram
- `scatter` - Scatter plot
- `bubble` - Bubble chart

### Time Series Charts (`time_series`)
- `line` - Line chart
- `area` - Area chart
- `timeline` - Timeline chart

### Box Plot Charts (`box_plot`)
- `box` - Box plot
- `violin` - Violin plot

### Heatmap Charts (`heatmap`)
- `heatmap` - Heatmap
- `calendar_heatmap` - Calendar heatmap

### Stacked Charts (`stacked`)
- `stacked_bar` - Stacked bar chart
- `stacked_line` - Stacked line chart
- `stacked_area` - Stacked area chart

### Multi Charts (`multi`)
- `multi_line` - Multi-line chart
- `multi_area` - Multi-area chart
- `combo_bar_line` - Combination bar and line chart

### Multi Group Charts (`multi_group`)
- `grouped_bar` - Grouped bar chart

### Hierarchical Charts (`hierarchical`)
- `treemap` - Treemap
- `sunburst` - Sunburst chart
- `icicle` - Icicle chart

## API Response Structure

The chart recommendations now include the `chart_group` field in each chart object:

```json
{
  "charts": [
    {
      "chart_type": "pie",
      "chart_group": "basic",
      "library": "plotly",
      "data": {
        "values": [30, 25, 20, 15, 10],
        "labels": ["Category A", "Category B", "Category C", "Category D", "Category E"],
        "type": "pie"
      },
      "layout": {
        "title": "Distribution of Categories",
        "showlegend": true
      }
    },
    {
      "chart_type": "histogram",
      "chart_group": "numerical", 
      "library": "plotly",
      "data": {
        "x": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
        "type": "histogram"
      },
      "layout": {
        "title": "Distribution of Values",
        "xaxis": {"title": "Value"},
        "yaxis": {"title": "Frequency"}
      }
    },
    {
      "chart_type": "line",
      "chart_group": "time_series",
      "library": "plotly", 
      "data": {
        "x": ["Jan 2024", "Feb 2024", "Mar 2024"],
        "y": [100, 120, 110],
        "type": "scatter",
        "mode": "lines"
      },
      "layout": {
        "title": "Trend Over Time",
        "xaxis": {"title": "Date"},
        "yaxis": {"title": "Value"}
      }
    }
  ],
  "data_quality": {...},
  "analytics": {...}
}
```

## Usage in UI

The UI can now group charts by their `chart_group` field to provide a better user experience:

```javascript
// Group charts by chart_group
const chartsByGroup = charts.reduce((groups, chart) => {
  const group = chart.chart_group || 'basic';
  if (!groups[group]) {
    groups[group] = [];
  }
  groups[group].push(chart);
  return groups;
}, {});

// Display charts in organized sections
Object.keys(chartsByGroup).forEach(groupName => {
  const groupCharts = chartsByGroup[groupName];
  // Render group section with charts
});
```

## Implementation Details

The `chart_group` field is added at the recommendation level in the `ChartData.get_rule_based_recommendations()` and `ChartData.get_llm_chart_recommendations()` methods, and flows through to the final chart data via the `ChartData.process_chart_recommendations()` method.

The mapping is handled by the `get_chart_group()` function in `app/services/chart_recommender.py`, which uses the `CHART_TYPE_TO_GROUP` dictionary to map chart types to their respective groups.

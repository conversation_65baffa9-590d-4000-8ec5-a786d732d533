# Common Headers System Documentation

This document explains the new centralized header management system that provides consistent header handling across all APIs (auth, event, insights, etc.).

## Overview

The common headers system provides:
- **Centralized header extraction** from all HTTP requests
- **Automatic context setting** via middleware
- **Standardized authentication dependencies**
- **Consistent logging and analytics integration**
- **Easy addition of new common headers**

## Architecture

### Core Components

1. **`app/core/context.py`** - Context variables and RequestContext dataclass
2. **`app/core/headers.py`** - Header extraction and dependency injection
3. **`app/core/middleware.py`** - Automatic header processing middleware
4. **`app/core/auth_dependencies.py`** - Standardized authentication dependencies

### Supported Headers

The system automatically extracts and processes these standard headers:

#### Request Tracking
- `x-request-id` - Unique request identifier (auto-generated if missing)
- `x-correlation-id` - Request correlation ID (defaults to request-id)
- `x-session-id` - User session identifier
- `x-device-id` - Device/client identifier

#### Client Information
- `user-agent` - Browser/client user agent
- `x-client-version` - Application version
- `x-platform` - Client platform (web, mobile, etc.)
- `x-timezone` - Client timezone
- `accept-language` - Client language preference

#### Network Information
- `x-forwarded-for` / `x-real-ip` - Client IP address (proxy-aware)
- `referer` / `referrer` - HTTP referrer

## Usage

### 1. Basic Header Access

```python
from fastapi import APIRouter, Depends
from app.core.headers import CommonHeaders
from app.core.context import RequestContext

router = APIRouter()

@router.get("/my-endpoint")
async def my_endpoint(context: RequestContext = CommonHeaders):
    # Access any header value
    request_id = context.request_id
    user_agent = context.user_agent
    ip_address = context.ip_address
    
    return {"request_id": request_id}
```

### 2. Authentication with Headers

```python
from app.core.auth_dependencies import AuthenticatedUser, RequireAuth
from app.core.headers import CommonHeaders

@router.get("/protected")
async def protected_endpoint(
    user: AuthenticatedUser = RequireAuth,
    context: RequestContext = CommonHeaders
):
    # Both user info and headers are available
    return {
        "user": user.email,
        "request_id": context.request_id,
        "ip": context.ip_address
    }
```

### 3. Optional Authentication

```python
from app.core.auth_dependencies import OptionalAuth

@router.get("/flexible")
async def flexible_endpoint(
    user: Optional[AuthenticatedUser] = OptionalAuth,
    context: RequestContext = CommonHeaders
):
    if user:
        return {"message": f"Hello {user.email}"}
    else:
        return {"message": "Hello anonymous user"}
```

### 4. Analytics Integration

```python
from app.core.auth_dependencies import AnalyticsAuth
from app.core.headers import get_headers_for_analytics

@router.post("/analytics")
async def analytics_endpoint(
    event_data: dict,
    user_email: Optional[str] = AnalyticsAuth,
    context: RequestContext = CommonHeaders
):
    # Get analytics-friendly header format
    analytics_headers = get_headers_for_analytics(context)
    
    # Process analytics event
    return {"processed": True}
```

### 5. Context Access Anywhere

```python
from app.core.context import get_request_context

def some_service_function():
    # Access context from anywhere in the request lifecycle
    context = get_request_context()
    logger.info(f"Processing request {context.request_id}")
```

## Authentication Dependencies

The system provides several pre-built authentication dependencies:

### RequireAuth
```python
user: AuthenticatedUser = RequireAuth
```
- Requires valid JWT token
- Returns AuthenticatedUser object with email and context
- Raises 401 if authentication fails

### OptionalAuth
```python
user: Optional[AuthenticatedUser] = OptionalAuth
```
- Works with or without authentication
- Returns AuthenticatedUser if token provided and valid
- Returns None if no token or invalid token

### RequireAdmin
```python
user: AuthenticatedUser = RequireAdmin
```
- Requires admin privileges (currently all authenticated users)
- Future: Will implement role-based access control

### AnalyticsAuth
```python
user_email: Optional[str] = AnalyticsAuth
```
- Returns user email if authenticated, None otherwise
- Optimized for analytics endpoints

## Adding New Common Headers

To add a new common header parameter:

### 1. Update Context Definition

Edit `app/core/context.py`:

```python
# Add new context variable
new_header_ctx_var = ContextVar[str]("new_header", default="")

@dataclass
class RequestContext:
    # ... existing fields ...
    new_header: str = ""
    
    def to_dict(self) -> Dict[str, str]:
        return {
            # ... existing fields ...
            "new_header": self.new_header
        }

# Add getter function
def get_new_header() -> str:
    return new_header_ctx_var.get()

# Update set_request_context function
def set_request_context(context: RequestContext) -> None:
    # ... existing setters ...
    if context.new_header:
        new_header_ctx_var.set(context.new_header)
```

### 2. Update Header Extraction

Edit `app/core/headers.py`:

```python
async def get_common_headers(
    request: Request,
    # ... existing headers ...
    x_new_header: Optional[str] = Header(None, alias="x-new-header")
) -> RequestContext:
    
    context = RequestContext(
        # ... existing fields ...
        new_header=sanitize_header_value(x_new_header)
    )
    
    return context
```

### 3. Update Middleware

Edit `app/core/middleware.py`:

```python
def _extract_request_context(self, request: Request) -> RequestContext:
    headers = request.headers
    
    context = RequestContext(
        # ... existing fields ...
        new_header=sanitize_header_value(headers.get('x-new-header'))
    )
    
    return context
```

## Client Integration

### Frontend Header Setup

```javascript
// Set common headers in your HTTP client
const headers = {
    'x-request-id': generateUUID(),
    'x-session-id': getSessionId(),
    'x-device-id': getDeviceId(),
    'x-client-version': '1.0.0',
    'x-platform': 'web',
    'x-timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
    'Authorization': `Bearer ${accessToken}`
};

// Make API request
fetch('/v1/api/endpoint', {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(data)
});
```

### Mobile App Integration

```swift
// iOS example
let headers = [
    "x-request-id": UUID().uuidString,
    "x-session-id": sessionManager.sessionId,
    "x-device-id": UIDevice.current.identifierForVendor?.uuidString ?? "",
    "x-client-version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "",
    "x-platform": "ios",
    "x-timezone": TimeZone.current.identifier,
    "Authorization": "Bearer \(accessToken)"
]
```

## Benefits

1. **Consistency** - All APIs use the same header extraction logic
2. **Maintainability** - Add new headers in one place, available everywhere
3. **Observability** - Consistent request tracking and logging
4. **Security** - Centralized header sanitization and validation
5. **Analytics** - Standardized data collection across all endpoints
6. **Developer Experience** - Simple, intuitive API for header access

## Migration Guide

### Existing Endpoints

To migrate existing endpoints to use the new system:

1. **Replace manual header extraction**:
   ```python
   # Old way
   user_agent = request.headers.get('user-agent')
   
   # New way
   context: RequestContext = CommonHeaders
   user_agent = context.user_agent
   ```

2. **Replace authentication dependencies**:
   ```python
   # Old way
   current_user: str = Depends(get_current_user)
   
   # New way
   user: AuthenticatedUser = RequireAuth
   ```

3. **Update analytics calls**:
   ```python
   # Old way
   analytics_data = extract_request_info(request)
   
   # New way
   analytics_data = get_headers_for_analytics(context)
   ```

### Backward Compatibility

The new system is designed to be backward compatible. Existing endpoints will continue to work, but should be gradually migrated to use the new dependencies for consistency.

## Testing

See `app/api/v1/endpoints/example_common_headers.py` for comprehensive examples of how to use all features of the common headers system.

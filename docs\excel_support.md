# Excel File Support

This document explains the Excel file support that has been added to the InfoCharts application.

## Supported File Formats

The application now supports the following file formats:

1. **CSV files** (`.csv`) - Comma-separated values
2. **Excel files** (`.xlsx`) - Excel 2007+ format
3. **Excel files** (`.xls`) - Legacy Excel format

## Dependencies

To use Excel file support, the following Python packages are required:

```bash
pip install openpyxl xlrd
```

These dependencies are included in `requirements-gemini.txt`:
- `openpyxl>=3.1.0` - For reading/writing .xlsx files
- `xlrd>=2.0.0` - For reading .xls files

## Features

### Multi-Sheet Support

- **Automatic Sheet Detection**: The system automatically detects all sheets in an Excel file
- **First Sheet Default**: By default, the first sheet is used for data processing
- **Sheet Information Logging**: The system logs information about available sheets

### File Type Detection

The system automatically detects file types based on file extensions:
- `.csv` → Processed as CSV file
- `.xlsx` → Processed as Excel file (using openpyxl engine)
- `.xls` → Processed as Excel file (using xlrd engine)

### Error Handling

Comprehensive error handling for common Excel file issues:
- **Missing Dependencies**: Clear error messages if Excel engines are not installed
- **Corrupted Files**: Graceful handling of corrupted or invalid Excel files
- **Engine Fallbacks**: Automatic fallback to alternative reading methods
- **Encoding Issues**: Proper handling of different file encodings

## API Usage

### File Upload Endpoint

The existing `/v1/data/file-upload` endpoint now accepts Excel files:

```javascript
// JavaScript example
const formData = new FormData();
formData.append('file', excelFile); // Can be .xlsx or .xls file

const response = await fetch('/v1/data/file-upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_token_here',
    'X-Interaction-Id': 'unique_id',
    'X-Session-Id': 'session_id',
    'X-Device-Id': 'device_id'
  },
  body: formData
});
```

### Response Format

The response format is identical for both CSV and Excel files:

```json
{
  "charts": [...],
  "data_quality": {
    "total_rows": 100,
    "categorical_columns": ["Category", "Type"],
    "numerical_columns": ["Value", "Amount"],
    "datetime_columns": ["Date"]
  },
  "analytics": {...}
}
```

## DataLoader Service Methods

### Primary Method: `load_csv_data()`

Despite the name, this method now handles both CSV and Excel files:

```python
from app.services.data_loader import DataLoader

loader = DataLoader()
df = await loader.load_csv_data(uploaded_file)
```

### Excel-Specific Method: `load_excel_data()`

For more control over Excel file loading:

```python
# Load specific sheet
df = await loader.load_excel_data(uploaded_file, sheet_name="Sheet2")

# Load first sheet (default)
df = await loader.load_excel_data(uploaded_file)
```

### Utility Methods

```python
# Get supported file types
supported_types = loader.get_supported_file_types()
# Returns: ['.csv', '.xlsx', '.xls']

# Get Excel sheet names
sheet_names = await loader.get_excel_sheet_names(uploaded_file)
# Returns: ['Sheet1', 'Sheet2', 'Data']
```

## Implementation Details

### File Validation

```python
# File extension validation
filename = file.filename.lower()
file_ext = os.path.splitext(filename)[1]
supported_extensions = ['.csv', '.xlsx', '.xls']

if file_ext not in supported_extensions:
    raise FileUploadError("Only CSV or Excel files are supported")
```

### Excel Engine Selection

```python
# Automatic engine selection based on file extension
engine = 'openpyxl' if file_ext == '.xlsx' else 'xlrd'

# Reading with appropriate engine
df = pd.read_excel(excel_data, engine=engine)
```

### Error Recovery

```python
try:
    # Primary method with specific engine
    df = pd.read_excel(excel_data, engine=engine)
except Exception:
    # Fallback method without engine specification
    df = pd.read_excel(excel_data)
```

## Logging

The system provides detailed logging for Excel file processing:

```
DEBUG: Loading Excel file: data.xlsx
DEBUG: Excel file has 3 sheet(s): ['Sheet1', 'Data', 'Summary']
INFO: Excel file contains multiple sheets. Using first sheet: 'Sheet1'
INFO: Successfully loaded Excel file: data.xlsx (shape: (100, 5))
```

## Troubleshooting

### Common Issues

1. **"Excel support not available"**
   - Install required dependencies: `pip install openpyxl xlrd`

2. **"Unable to read Excel file"**
   - Check if the file is corrupted
   - Verify the file is a valid Excel format
   - Try opening the file in Excel to confirm it's readable

3. **"Only CSV or Excel files are supported"**
   - Check the file extension
   - Ensure the file has a proper extension (.csv, .xlsx, .xls)

### Performance Considerations

- **Large Files**: Excel files are loaded entirely into memory
- **Multiple Sheets**: Only the first sheet is processed by default
- **Memory Usage**: Large Excel files may require more memory than equivalent CSV files

## Future Enhancements

Potential future improvements:
- Sheet selection in the UI
- Support for multiple sheets processing
- Excel file preview before processing
- Custom data type detection for Excel files
- Support for Excel formulas and calculated fields

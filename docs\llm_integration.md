# LLM Integration Guide

This document explains how to set up and use different LLM providers in the InfoCharts application.

## Supported LLM Providers

The application currently supports the following LLM providers:

1. **Mistral AI** (default)
2. **Google Gemini**

## Configuration

### Environment Variables

To configure the LLM providers, add the following variables to your `.env` file:

```
# Default LLM provider (mistral or gemini)
DEFAULT_LLM_PROVIDER=mistral

# Mistral AI settings
MISTRAL_API_KEY=your_mistral_api_key
MISTRAL_MODEL=mistral-tiny  # or mistral-small, mistral-medium, mistral-large

# Google Gemini settings
GEMINI_API_KEY=your_gemini_api_key
GEMINI_MODEL=gemini-pro  # or other available Gemini models
```

### Installing Required Packages

#### Mistral AI

Mistral AI support is included in the base requirements.

#### Google Gemini

To use Google Gemini, install the additional required packages:

```bash
pip install -r requirements-gemini.txt
```

Or install the packages directly:

```bash
pip install langchain-google-genai google-generativeai
```

## Switching Between Providers

### Using Environment Variables

The simplest way to switch between providers is to set the `DEFAULT_LLM_PROVIDER` environment variable in your `.env` file:

```
DEFAULT_LLM_PROVIDER=gemini  # or mistral
```

### Using the API

You can also specify the provider when making API requests to endpoints that use the LLM client.

For example, to check the status of a specific LLM provider:

```
GET /api/v1/llm-status?provider=gemini
```

## Testing the Integration

You can test the LLM integration using the `/api/v1/llm-status` endpoint:

```
GET /api/v1/llm-status
```

This will return the status of the default LLM provider.

To test a specific provider:

```
GET /api/v1/llm-status?provider=mistral
GET /api/v1/llm-status?provider=gemini
```

## Fallback Behavior

If the selected provider is not available (e.g., required packages not installed or API key not configured), the system will automatically fall back to Mistral AI.

## Getting API Keys

### Mistral AI

1. Visit [https://console.mistral.ai/](https://console.mistral.ai/)
2. Create an account or log in
3. Navigate to the API Keys section
4. Create a new API key

### Google Gemini

1. Visit [https://ai.google.dev/](https://ai.google.dev/)
2. Create an account or log in
3. Navigate to the API Keys section in Google AI Studio
4. Create a new API key

## Troubleshooting

### Common Issues

1. **"Provider not supported" warning**: Check that you've specified either "mistral" or "gemini" as the provider.

2. **"Gemini packages not found" warning**: Install the required packages using `pip install langchain-google-genai google-generativeai`.

3. **"GEMINI_API_KEY not set" error**: Add your Gemini API key to the `.env` file.

4. **Invalid JSON response**: This usually indicates that the LLM didn't return a properly formatted JSON response. Check your prompt and response format.

### Logging

The LLM client logs detailed information about its operations. Check the application logs for more information about any issues.

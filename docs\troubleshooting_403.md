# Troubleshooting 403 Forbidden Error

This guide helps troubleshoot the 403 Forbidden error when accessing analytics endpoints.

## Common Causes and Solutions

### 1. **Missing or Invalid Authorization Header**

**Symptoms:**
- 403 Forbidden on `/v1/analytics/user-profile`
- Error occurs immediately without authentication attempt

**Solution:**
Ensure you're sending the Authorization header correctly:

```javascript
const headers = {
  'Authorization': `Bear<PERSON> ${accessToken}`,
  'Content-Type': 'application/json'
};

fetch('/v1/analytics/user-profile', {
  method: 'GET',
  headers: headers
});
```

### 2. **CORS Preflight Issues**

**Symptoms:**
- 403 Forbidden on OPTIONS requests
- <PERSON>rowser console shows CORS errors

**Solution:**
Check that your frontend origin is included in `ALLOWED_ORIGINS`:

```env
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:5173
```

### 3. **JWT Configuration Issues**

**Symptoms:**
- 403 Forbidden after successful login
- <PERSON>ken appears valid but requests fail

**Solution:**
Verify JWT configuration in `.env`:

```env
JWT_SECRET_KEY=your_secret_key_here
JWT_REFRESH_SECRET_KEY=your_refresh_secret_key_here
JWT_ALGORITHM=HS256
```

### 4. **Token Expiration**

**Symptoms:**
- 403 Forbidden after some time
- Previously working requests start failing

**Solution:**
Check token expiration and implement refresh logic:

```javascript
// Check if token is expired before making requests
const isTokenExpired = (token) => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return Date.now() >= payload.exp * 1000;
  } catch {
    return true;
  }
};

if (isTokenExpired(accessToken)) {
  // Refresh token or redirect to login
  await refreshToken();
}
```

### 5. **Security Exception Handler**

**Symptoms:**
- 403 Forbidden with custom error format
- Error response includes `error_code: 9000`

**Solution:**
Check if a `SecurityError` is being raised somewhere in the code. This would trigger the custom exception handler that returns 403.

## Debugging Steps

### Step 1: Test Authentication Flow

1. **Test debug login endpoint:**
```bash
curl -X POST "http://127.0.0.1:8000/v1/auth/debug-login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

2. **Use the returned token for analytics requests:**
```bash
curl -X GET "http://127.0.0.1:8000/v1/analytics/debug/auth" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Step 2: Check Server Logs

Look for these log messages:
- `Authentication failed: 401 - Invalid or expired token`
- `No credentials provided`
- `Token missing`
- `JWT decode error`

### Step 3: Verify Token Format

A valid JWT token should have three parts separated by dots:
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************.signature
```

### Step 4: Test with curl

```bash
# Get token
TOKEN=$(curl -s -X POST "http://127.0.0.1:8000/v1/auth/debug-login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}' | jq -r '.access_token')

# Test analytics endpoint
curl -X GET "http://127.0.0.1:8000/v1/analytics/user-profile" \
  -H "Authorization: Bearer $TOKEN" \
  -v
```

### Step 5: Use Test Script

Run the provided test script:
```bash
python scripts/test_analytics_auth.py
```

## Common Error Responses

### 403 Forbidden (Security Error)
```json
{
  "error_code": 9000,
  "error_type": "SECURITY",
  "error_message": "Unauthorized access or security violation detected"
}
```
**Solution:** Check for SecurityError exceptions in the code.

### 401 Unauthorized (Token Issues)
```json
{
  "detail": "Invalid or expired access token"
}
```
**Solution:** Check token validity and JWT configuration.

### 422 Unprocessable Entity (Validation Error)
```json
{
  "detail": [
    {
      "loc": ["header", "authorization"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```
**Solution:** Ensure Authorization header is included.

## Quick Fixes

### Fix 1: Update Frontend Request
```javascript
// Before (incorrect)
fetch('/v1/analytics/user-profile');

// After (correct)
fetch('/v1/analytics/user-profile', {
  headers: {
    'Authorization': `Bearer ${accessToken}`
  }
});
```

### Fix 2: Check Environment Variables
```bash
# Verify JWT configuration
echo $JWT_SECRET_KEY
echo $JWT_REFRESH_SECRET_KEY
```

### Fix 3: Restart Server
Sometimes configuration changes require a server restart:
```bash
# Stop the server (Ctrl+C)
# Then restart
uvicorn app.main:app --reload
```

### Fix 4: Clear Browser Cache
Clear browser cache and cookies, especially for localhost development.

## Still Having Issues?

If you're still experiencing 403 errors:

1. **Enable debug logging:**
   ```env
   VERBOSE_LOGGING=true
   ```

2. **Check FastAPI docs:**
   Visit `http://127.0.0.1:8000/docs` and test endpoints directly

3. **Verify CORS settings:**
   Ensure your frontend origin is in `ALLOWED_ORIGINS`

4. **Test with Postman:**
   Use Postman to isolate frontend vs backend issues

5. **Check middleware order:**
   Ensure CORS middleware is added before authentication

## Contact Support

If none of these solutions work, provide:
- Server logs
- Request headers
- Response body
- Environment configuration (without secrets)
- Steps to reproduce the issue

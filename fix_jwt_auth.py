#!/usr/bin/env python3
"""
JWT Authentication Fix Script
This script provides solutions for JWT authentication issues.
"""

import requests
import json
import base64
from datetime import datetime, timezone

def decode_jwt_without_verification(token):
    """Decode JWT payload without signature verification."""
    try:
        parts = token.split('.')
        if len(parts) != 3:
            return None
        
        # Decode payload (add padding if needed)
        payload_b64 = parts[1] + '=' * (4 - len(payload_b64) % 4)
        payload_bytes = base64.urlsafe_b64decode(payload_b64)
        return json.loads(payload_bytes.decode('utf-8'))
    except:
        return None

def get_fresh_tokens():
    """Get fresh tokens from the server."""
    print("🔄 Getting fresh tokens from server...")
    
    try:
        # Method 1: Debug token endpoint
        response = requests.post('http://127.0.0.1:8000/v1/debug-token', timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                return True, result.get('tokens', {})
        
        # Method 2: Debug login endpoint
        response = requests.post(
            'http://127.0.0.1:8000/v1/auth/debug-login',
            json={"email": "<EMAIL>"},
            timeout=10
        )
        if response.status_code == 200:
            return True, response.json()
        
        return False, f"Failed to get tokens. Status: {response.status_code}"
        
    except Exception as e:
        return False, str(e)

def test_token(access_token):
    """Test if a token works."""
    try:
        headers = {'Authorization': f'Bearer {access_token}'}
        response = requests.get('http://127.0.0.1:8000/v1/analytics/user-profile', headers=headers, timeout=5)
        return response.status_code == 200, response.status_code
    except Exception as e:
        return False, str(e)

def main():
    print("JWT Authentication Fix Tool")
    print("=" * 40)
    
    # Check server health
    try:
        health = requests.get('http://127.0.0.1:8000/v1/health', timeout=5)
        if health.status_code != 200:
            print("❌ Server is not healthy")
            return
        print("✅ Server is healthy")
    except:
        print("❌ Cannot connect to server")
        return
    
    # Get fresh tokens
    success, tokens = get_fresh_tokens()
    
    if not success:
        print(f"❌ Failed to get fresh tokens: {tokens}")
        return
    
    print("✅ Fresh tokens obtained!")
    
    access_token = tokens.get('access_token')
    refresh_token = tokens.get('refresh_token')
    
    if access_token:
        # Decode and display token info
        payload = decode_jwt_without_verification(access_token)
        if payload:
            exp_time = datetime.fromtimestamp(payload.get('exp', 0), tz=timezone.utc)
            print(f"\n📋 Token Information:")
            print(f"   Subject: {payload.get('sub')}")
            print(f"   Expires: {exp_time}")
            print(f"   Valid for: {(exp_time - datetime.now(timezone.utc)).total_seconds() / 60:.1f} minutes")
        
        # Test the token
        works, status = test_token(access_token)
        if works:
            print("✅ Token verification successful!")
        else:
            print(f"❌ Token verification failed: {status}")
            return
        
        # Provide the working tokens
        print(f"\n🎯 WORKING TOKENS FOR YOUR USE:")
        print("=" * 50)
        print(f"Access Token:")
        print(f"{access_token}")
        print(f"\nRefresh Token:")
        print(f"{refresh_token}")
        
        print(f"\n📝 Usage Examples:")
        print("=" * 30)
        print("1. cURL with Authorization header:")
        print(f'   curl -H "Authorization: Bearer {access_token[:50]}..." \\')
        print('        "http://127.0.0.1:8000/v1/analytics/user-profile"')
        
        print("\n2. JavaScript fetch:")
        print("   fetch('/v1/analytics/user-profile', {")
        print("     headers: {")
        print(f'       Authorization: "Bearer {access_token[:50]}..."')
        print("     }")
        print("   })")
        
        print(f"\n🔧 TROUBLESHOOTING:")
        print("=" * 30)
        print("If tokens stop working:")
        print("1. Check if JWT_SECRET_KEY changed in .env")
        print("2. Restart the server if environment changed")
        print("3. Run this script again to get fresh tokens")
        print("4. Use refresh token endpoint: POST /v1/auth/refresh-token")
        
        return True
    
    else:
        print("❌ No access token in response")
        return False

if __name__ == "__main__":
    main()

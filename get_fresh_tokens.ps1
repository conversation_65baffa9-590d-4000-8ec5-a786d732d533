# PowerShell script to get fresh JWT tokens
# Run with: powershell -ExecutionPolicy Bypass -File get_fresh_tokens.ps1

$baseUrl = "http://127.0.0.1:8000"

Write-Host "JWT Token Refresh Tool" -ForegroundColor Green
Write-Host "=" * 30 -ForegroundColor Gray

# Test server health
try {
    $health = Invoke-RestMethod -Uri "$baseUrl/v1/health" -Method Get -TimeoutSec 5
    Write-Host "✅ Server is healthy" -ForegroundColor Green
} catch {
    Write-Host "❌ Server is not responding" -ForegroundColor Red
    Write-Host "Make sure the server is running: uvicorn app.main:app --reload" -ForegroundColor Yellow
    exit 1
}

# Get fresh tokens using debug login
try {
    $body = @{
        email = "<EMAIL>"
    } | ConvertTo-Json

    $headers = @{
        "Content-Type" = "application/json"
    }

    Write-Host "`n🔄 Getting fresh tokens..." -ForegroundColor Yellow
    $response = Invoke-RestMethod -Uri "$baseUrl/v1/auth/debug-login" -Method Post -Body $body -Headers $headers -TimeoutSec 10

    if ($response.access_token) {
        Write-Host "✅ Fresh tokens obtained!" -ForegroundColor Green
        
        # Test the access token
        $testHeaders = @{
            "Authorization" = "Bearer $($response.access_token)"
        }
        
        try {
            $testResponse = Invoke-RestMethod -Uri "$baseUrl/v1/analytics/user-profile" -Method Get -Headers $testHeaders -TimeoutSec 5
            Write-Host "✅ Token verification successful!" -ForegroundColor Green
        } catch {
            Write-Host "❌ Token verification failed" -ForegroundColor Red
            Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Write-Host "`n🎯 YOUR FRESH WORKING TOKENS:" -ForegroundColor Cyan
        Write-Host "=" * 40 -ForegroundColor Gray
        Write-Host "`nAccess Token:" -ForegroundColor White
        Write-Host $response.access_token -ForegroundColor Yellow
        
        Write-Host "`nRefresh Token:" -ForegroundColor White
        Write-Host $response.refresh_token -ForegroundColor Yellow
        
        Write-Host "`n📋 Token Details:" -ForegroundColor Cyan
        Write-Host "Token Type: $($response.token_type)" -ForegroundColor White
        
        # Decode token payload (basic decode without verification)
        $tokenParts = $response.access_token.Split('.')
        if ($tokenParts.Length -eq 3) {
            try {
                $payload = $tokenParts[1]
                # Add padding if needed
                while ($payload.Length % 4 -ne 0) {
                    $payload += "="
                }
                $payloadBytes = [System.Convert]::FromBase64String($payload)
                $payloadJson = [System.Text.Encoding]::UTF8.GetString($payloadBytes)
                $payloadObj = $payloadJson | ConvertFrom-Json
                
                $expTime = [DateTimeOffset]::FromUnixTimeSeconds($payloadObj.exp).DateTime
                Write-Host "Subject: $($payloadObj.sub)" -ForegroundColor White
                Write-Host "Expires: $expTime UTC" -ForegroundColor White
                
                $timeLeft = $expTime - (Get-Date).ToUniversalTime()
                Write-Host "Valid for: $([math]::Round($timeLeft.TotalMinutes, 1)) minutes" -ForegroundColor White
            } catch {
                Write-Host "Could not decode token payload" -ForegroundColor Yellow
            }
        }
        
        Write-Host "`n📝 Usage Examples:" -ForegroundColor Cyan
        Write-Host "=" * 20 -ForegroundColor Gray
        Write-Host "`n1. PowerShell Invoke-RestMethod:" -ForegroundColor White
        Write-Host '$headers = @{ "Authorization" = "Bearer YOUR_TOKEN_HERE" }' -ForegroundColor Gray
        Write-Host 'Invoke-RestMethod -Uri "http://127.0.0.1:8000/v1/analytics/user-profile" -Headers $headers' -ForegroundColor Gray
        
        Write-Host "`n2. cURL command:" -ForegroundColor White
        Write-Host 'curl -H "Authorization: Bearer YOUR_TOKEN_HERE" "http://127.0.0.1:8000/v1/analytics/user-profile"' -ForegroundColor Gray
        
        Write-Host "`n🔧 If tokens stop working:" -ForegroundColor Cyan
        Write-Host "1. Run this script again to get fresh tokens" -ForegroundColor White
        Write-Host "2. Check if server restarted or configuration changed" -ForegroundColor White
        Write-Host "3. Use refresh token endpoint: POST /v1/auth/refresh-token" -ForegroundColor White
        
    } else {
        Write-Host "❌ No access token in response" -ForegroundColor Red
        Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Yellow
    }

} catch {
    Write-Host "❌ Failed to get fresh tokens" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`nDone!" -ForegroundColor Green

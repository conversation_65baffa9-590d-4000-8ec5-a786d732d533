import pandas as pd
import random
import os
import math
import csv
from datetime import datetime, timedelta

# We'll use random names instead of the Faker library to avoid dependencies
def generate_random_name():
    first_names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
    last_names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
    return f"{random.choice(first_names)} {random.choice(last_names)}"

# Function to generate company names
def generate_company_name():
    prefixes = ['Tech', 'Global', 'Advanced', 'Modern', 'Future', 'Smart', 'Innovative', 'Digital', 'Creative', 'Prime']
    suffixes = ['Systems', 'Solutions', 'Technologies', 'Enterprises', 'Industries', 'Group', 'Corp', 'Inc', 'Partners', 'Associates']
    return f"{random.choice(prefixes)} {random.choice(suffixes)}"

# Create the /sample_data directory if it doesn't exist
if not os.path.exists("sample_data"):
    os.makedirs("sample_data")

# Function to generate random dates within a given range
def generate_dates(start_date, num_days, num_records):
    return [start_date + timedelta(days=random.randint(0, num_days)) for _ in range(num_records)]

# Function to generate sample data for customer_transactions
def generate_customer_transactions(num_records=1000):
    products = ['IPhone', 'Macbook', 'Playstation', 'Smartwatch', 'Laptop', 'Tshirt', 'Perfume', 'Headphones', 'Shoes', 'Lunchbox']

    # List of sample first and last names
    first_names = ['John Smith', 'Jane Johnson', 'Alex', 'Emily', 'Chris', 'Katie', 'Michael', 'Sarah', 'David', 'Laura']
    last_names = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Martinez', 'Hernandez']
    # Generate random customer names
    customers = [f"{random.choice(first_names)} {random.choice(last_names)}" for _ in range(50)]
    transaction_dates = generate_dates(datetime(2024, 1, 1), 365, num_records)
    quantities = [random.randint(1, 5) for _ in range(num_records)]
    prices = [random.choice([10.00,12.25,26.50,33.00,47.75, 53.00,65.50, 75.00,81.25,95.50]) for _ in range(num_records)]

    data = {
        'transaction_id': [i for i in range(1, num_records + 1)],
        'customer_name': [random.choice(customers) for _ in range(num_records)],
        'transaction_date': transaction_dates,
        'product': [random.choice(products) for _ in range(num_records)],
        'quantity': quantities,
        'price': prices,
    }

    df = pd.DataFrame(data)
    df.to_csv("customer_transactions.csv", index=False)

# Function to generate sample data for employee_timesheets
def generate_employee_timesheets(num_records=1000):
     # List of sample first and last names
    first_names = ['John Smith', 'Jane Johnson', 'Alex', 'Emily', 'Chris', 'Katie', 'Michael', 'Sarah', 'David', 'Laura']
    last_names = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Martinez', 'Hernandez']

    employees = [f"{random.choice(first_names)} {random.choice(last_names)}" for _ in range(50)]
    departments = ['Group1', 'Group2', 'Group3', 'Group4', 'Group5']
    project_names = ['Onboarding', 'Instant Loan', 'Branding', 'Marketing Campaign','Global Payment','Credit Card','Debit Card','BNPL']
    work_dates = generate_dates(datetime(2024, 1, 1), 365, num_records)
    hours_worked = [random.randint(4, 12) for _ in range(num_records)]

    data = {
        'employee_id': [i for i in range(1, num_records + 1)],
        'employee_name': [random.choice(employees) for _ in range(num_records)],
        'date': work_dates,
        'hours_worked': hours_worked,
        'department': [random.choice(departments) for _ in range(num_records)],
        'project': [random.choice(project_names) for _ in range(num_records)],
    }

    df = pd.DataFrame(data)
    df.to_csv("employee_timesheets.csv", index=False)

# Function to generate sample data for production_incidents
def generate_production_incidents(num_records=1000):
    incident_types = ['System Failure', 'Process Issue', 'Bug', 'Outage']
    severity_levels = ['Critical', 'High', 'Medium', 'Low']
    departments = ['IT', 'HR', 'Operations', 'Customer Support']
    incident_dates = generate_dates(datetime(2024, 1, 1), 365, num_records)
    hours_to_fix = [random.randint(1, 48) for _ in range(num_records)]

    data = {
        'incident_id': [i for i in range(1, num_records + 1)],
        'incident_date': incident_dates,
        'severity': [random.choice(severity_levels) for _ in range(num_records)],
        'department': [random.choice(departments) for _ in range(num_records)],
        'hours_to_fix': hours_to_fix,
        'incident_type': [random.choice(incident_types) for _ in range(num_records)],
    }

    df = pd.DataFrame(data)
    df.to_csv("production_incidents.csv", index=False)

# Function to generate sample data for project_status_timeline
def generate_project_status_timeline(num_records=1000):

    # Generate random project names
    project_names = [generate_company_name() for _ in range(10)]  # Generate 10 random project names
    statuses = ['In Progress', 'Completed', 'Pending']
    progress = [random.randint(1, 100) for _ in range(num_records)]
    start_dates = generate_dates(datetime(2023, 1, 1), 365, num_records)
    end_dates = [start + timedelta(days=random.randint(30, 180)) for start in start_dates]

    data = {
        'project_id': [i for i in range(1, num_records + 1)],
        'project_name': [random.choice(project_names) for _ in range(num_records)],
        'start_date': start_dates,
        'end_date': end_dates,
        'progress': progress,
        'status': [random.choice(statuses) for _ in range(num_records)],
    }

    df = pd.DataFrame(data)
    df.to_csv("project_status_timeline.csv", index=False)

# Function to generate sample data for sales_order
def generate_sales_order(num_records=1000):
    products = ['IPhone', 'Macbook', 'Playstation', 'Smartwatch', 'Laptop', 'Tshirt', 'Perfume', 'Headphones', 'Shoes', 'Lunchbox']
    regions = ['North', 'South', 'East', 'West']
    # List of sample first and last names
    first_names = ['John Smith', 'Jane Johnson', 'Alex', 'Emily', 'Chris', 'Katie', 'Michael', 'Sarah', 'David', 'Laura']
    last_names = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Martinez', 'Hernandez']
    # Generate random customer names
    customers = [f"{random.choice(first_names)} {random.choice(last_names)}" for _ in range(50)]
    order_dates = generate_dates(datetime(2024, 1, 1), 365, num_records)
    quantities = [random.randint(1, 5) for _ in range(num_records)]
    total_amounts = [quantity * random.choice([30, 50, 75,110,140,170,200,230,260,290,320,350,380,410,440,470,500]) for quantity in quantities]

    data = {
        'order_id': [i for i in range(1, num_records + 1)],
        'customer_id': [random.choice(customers) for _ in range(num_records)],
        'order_date': order_dates,
        'product': [random.choice(products) for _ in range(num_records)],
        'quantity': quantities,
        'total_amount': total_amounts,
        'sales_region': [random.choice(regions) for _ in range(num_records)],
    }

    df = pd.DataFrame(data)
    df.to_csv("sales_order.csv", index=False)

# Function to generate sample data for advanced chart types
def generate_advanced_chart_data(num_records=1000):
    """
    Generates sample data suitable for:
    - Stacked bar charts
    - Multi-line charts
    - Stacked area charts
    - Scatter charts
    - Bubble charts
    - Calendar heatmaps
    """
    # Common categories and metrics
    categories = ['Category A', 'Category B', 'Category C', 'Category D', 'Category E']
    subcategories = ['Subcategory 1', 'Subcategory 2', 'Subcategory 3', 'Subcategory 4']
    regions = ['North', 'South', 'East', 'West', 'Central']
    products = ['Product X', 'Product Y', 'Product Z', 'Product W', 'Product V']

    # Generate dates with higher density (daily data for a year)
    start_date = datetime(2023, 1, 1)
    dates = []
    for i in range(365):  # Daily data for a year
        # Create multiple entries per day to ensure good calendar heatmap data
        day_count = random.randint(1, 5)  # 1-5 events per day
        for _ in range(day_count):
            dates.append(start_date + timedelta(days=i))

    # Ensure we have exactly num_records
    if len(dates) > num_records:
        dates = random.sample(dates, num_records)
    while len(dates) < num_records:
        random_day = random.randint(0, 364)
        dates.append(start_date + timedelta(days=random_day))

    # Generate metrics with correlations for scatter and bubble charts
    base_values = [random.uniform(10, 100) for _ in range(num_records)]

    # Create correlated metrics (for scatter plots)
    metric1 = base_values
    # Metric2 is correlated with metric1 but with some noise
    metric2 = [value * random.uniform(0.8, 1.2) for value in base_values]
    # Metric3 is somewhat correlated with metric1 (for bubble size)
    metric3 = [value * random.uniform(0.5, 1.5) for value in base_values]

    # Generate time series data with trends for line and area charts
    time_series_values = {}
    for category in categories:
        # Start with a base value
        base = random.uniform(50, 200)
        # Generate a trend with some seasonality and noise
        values = []
        for i in range(365):  # Daily for a year
            # Add trend (gradual increase)
            trend = base + (i * 0.1)
            # Add seasonality (sine wave)
            seasonality = 20 * math.sin(i * 2 * math.pi / 90)  # 90-day cycle
            # Add noise
            noise = random.uniform(-10, 10)
            values.append(max(0, trend + seasonality + noise))  # Ensure non-negative
        time_series_values[category] = values

    # Create the dataframe
    data = {
        'date': dates,
        'category': [random.choice(categories) for _ in range(num_records)],
        'subcategory': [random.choice(subcategories) for _ in range(num_records)],
        'region': [random.choice(regions) for _ in range(num_records)],
        'product': [random.choice(products) for _ in range(num_records)],
        'metric1': metric1,  # For scatter x-axis
        'metric2': metric2,  # For scatter y-axis
        'metric3': metric3,  # For bubble size
        'count': [random.randint(1, 100) for _ in range(num_records)],  # For heatmaps
        'value': [random.uniform(100, 1000) for _ in range(num_records)],  # For bar/area charts
    }

    # Add month and quarter for easier grouping
    df = pd.DataFrame(data)
    df['month'] = df['date'].dt.strftime('%Y-%m')
    df['quarter'] = df['date'].dt.to_period('Q').astype(str)
    df['day_of_week'] = df['date'].dt.day_name()

    # Add some calculated fields for more interesting visualizations
    df['growth'] = df.groupby(['category', 'month'])['value'].transform(
        lambda x: (x - x.min()) / (x.max() - x.min() + 0.01) * 100
    )

    # Save to CSV
    df.to_csv("advanced_chart_data.csv", index=False)
    print("Advanced chart data generated and saved as 'advanced_chart_data.csv'")

    # Create a second dataset specifically optimized for stacked charts
    stacked_data = []
    months = pd.date_range(start=start_date, periods=12, freq='M')

    for month in months:
        month_str = month.strftime('%Y-%m')
        for category in categories:
            for region in regions:
                # Create consistent but varied values for each combination
                base_value = random.uniform(50, 200)
                stacked_data.append({
                    'month': month_str,
                    'category': category,
                    'region': region,
                    'sales': base_value * (1 + categories.index(category) * 0.1) * (1 + regions.index(region) * 0.1),
                    'profit': base_value * 0.3 * (1 + categories.index(category) * 0.05) * (1 + regions.index(region) * 0.05),
                    'units': int(base_value / 10) * (1 + categories.index(category) * 0.2)
                })

    stacked_df = pd.DataFrame(stacked_data)
    stacked_df.to_csv("stacked_chart_data.csv", index=False)
    print("Stacked chart data generated and saved as 'stacked_chart_data.csv'")

# Generate all the data
generate_customer_transactions()
generate_employee_timesheets()
generate_production_incidents()
generate_project_status_timeline()
generate_sales_order()
generate_advanced_chart_data()

print("Sample data generated and saved in the /sample_data directory.")

#!/usr/bin/env python3
"""
Setup script for Google Sheets Analytics

This script helps set up the Google Sheets for analytics tracking.
It creates the necessary headers and validates the configuration.
"""

import os
import sys
import gspread
from google.oauth2.service_account import Credentials

def setup_google_sheets_analytics():
    """Setup Google Sheets for analytics tracking"""
    
    print("🚀 Setting up Google Sheets Analytics...")
    
    # Check for required environment variables
    credentials_file = os.getenv('GOOGLE_SHEETS_CREDENTIALS_FILE')
    user_profile_sheet_id = os.getenv('GOOGLE_SHEETS_USER_PROFILE_SHEET_ID')
    events_sheet_id = os.getenv('GOOGLE_SHEETS_EVENTS_SHEET_ID')
    
    if not credentials_file:
        print("❌ GOOGLE_SHEETS_CREDENTIALS_FILE environment variable not set")
        return False
    
    if not user_profile_sheet_id:
        print("❌ GOOGLE_SHEETS_USER_PROFILE_SHEET_ID environment variable not set")
        return False
    
    if not events_sheet_id:
        print("❌ GOOGLE_SHEETS_EVENTS_SHEET_ID environment variable not set")
        return False
    
    if not os.path.exists(credentials_file):
        print(f"❌ Credentials file not found: {credentials_file}")
        return False
    
    try:
        # Initialize Google Sheets client
        scope = [
            'https://spreadsheets.google.com/feeds',
            'https://www.googleapis.com/auth/drive'
        ]
        
        credentials = Credentials.from_service_account_file(credentials_file, scopes=scope)
        client = gspread.authorize(credentials)
        
        print("✅ Google Sheets client initialized successfully")
        
        # Setup User Profile Sheet
        print("\n📊 Setting up User Profile Sheet...")
        try:
            user_profile_sheet = client.open_by_key(user_profile_sheet_id).sheet1
            print("✅ User Profile Sheet Opened")

            # Define headers for user profile sheet
            user_profile_headers = [
                'email', 'first_login_date', 'last_login_date', 'login_count',
                'user_agent', 'ip_address', 'location', 'name', 'picture', 'updated_at'
            ]

            # Check if headers already exist
            existing_headers = user_profile_sheet.row_values(1)

            print(f"Current headers in the sheet: {existing_headers}")

            if not existing_headers or existing_headers != user_profile_headers:
                user_profile_sheet.clear()
                user_profile_sheet.insert_row(user_profile_headers, 1)
                print("✅ User Profile Sheet headers setup completed")
            else:
                print("✅ User Profile Sheet headers already configured")

        except Exception as e:
            print(f"❌ Failed to setup User Profile Sheet: {str(e)}")

            # Optionally, you can log the type of error
            import traceback
            traceback.print_exc()

            return False
        
        # Setup Events Sheet
        print("\n📈 Setting up Events Sheet...")
        try:
            events_sheet = client.open_by_key(events_sheet_id).sheet1
            
            # Define headers for events sheet
            events_headers = [
                'event_id', 'email', 'event_type', 'timestamp', 'metadata',
                'user_agent', 'ip_address', 'session_id', 'page_url', 'referrer'
            ]
            
            # Check if headers already exist
            existing_headers = events_sheet.row_values(1)
            if not existing_headers or existing_headers != events_headers:
                events_sheet.clear()
                events_sheet.insert_row(events_headers, 1)
                print("✅ Events Sheet headers setup completed")
            else:
                print("✅ Events Sheet headers already configured")
                
        except Exception as e:
            print(f"❌ Failed to setup Events Sheet: {str(e)}")
            return False
        
        print("\n🎉 Google Sheets Analytics setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Make sure your .env file has all required variables")
        print("2. Install dependencies: pip install gspread google-auth-oauthlib google-auth-httplib2")
        print("3. Start your FastAPI application")
        print("4. Test the analytics endpoints")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to setup Google Sheets Analytics: {str(e)}")
        return False

def validate_configuration():
    """Validate the analytics configuration"""
    
    print("\n🔍 Validating Analytics Configuration...")
    
    required_vars = [
        'GOOGLE_SHEETS_CREDENTIALS_FILE',
        'GOOGLE_SHEETS_USER_PROFILE_SHEET_ID',
        'GOOGLE_SHEETS_EVENTS_SHEET_ID'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        return False
    
    # Check if credentials file exists
    credentials_file = os.getenv('GOOGLE_SHEETS_CREDENTIALS_FILE')
    if not os.path.exists(credentials_file):
        print(f"❌ Credentials file not found: {credentials_file}")
        return False
    
    print("✅ All required environment variables are set")
    print("✅ Credentials file exists")
    
    return True

def print_usage():
    """Print usage instructions"""
    print("""
📚 Google Sheets Analytics Setup

Usage:
    python scripts/setup_analytics.py [command]

Commands:
    setup     - Setup Google Sheets with proper headers
    validate  - Validate configuration
    help      - Show this help message

Before running:
1. Set up your Google Service Account and download credentials JSON
2. Create two Google Sheets (User Profile and Events)
3. Share both sheets with your service account email
4. Set environment variables in your .env file:
   - GOOGLE_SHEETS_CREDENTIALS_FILE
   - GOOGLE_SHEETS_USER_PROFILE_SHEET_ID
   - GOOGLE_SHEETS_EVENTS_SHEET_ID

Example .env configuration:
    GOOGLE_SHEETS_CREDENTIALS_FILE=./config/service-account-credentials.json
    GOOGLE_SHEETS_USER_PROFILE_SHEET_ID=1ABC123DEF456GHI789JKL
    GOOGLE_SHEETS_EVENTS_SHEET_ID=1XYZ987UVW654TSR321PON
    ENABLE_ANALYTICS=true
""")

if __name__ == "__main__":
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    if len(sys.argv) < 2:
        print_usage()
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "setup":
        success = setup_google_sheets_analytics()
        sys.exit(0 if success else 1)
    elif command == "validate":
        success = validate_configuration()
        sys.exit(0 if success else 1)
    elif command == "help":
        print_usage()
        sys.exit(0)
    else:
        print(f"❌ Unknown command: {command}")
        print_usage()
        sys.exit(1)

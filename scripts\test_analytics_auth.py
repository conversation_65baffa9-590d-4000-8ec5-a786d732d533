#!/usr/bin/env python3
"""
Test script for Analytics Authentication

This script helps debug authentication issues with analytics endpoints.
"""

import requests
import json
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_authentication():
    """Test authentication flow and analytics endpoints"""
    
    base_url = "http://127.0.0.1:8000/v1"
    
    print("🔐 Testing Analytics Authentication...")
    
    # Step 1: Test debug login to get a token
    print("\n1. Getting authentication token...")
    
    try:
        login_response = requests.post(
            f"{base_url}/auth/debug-login",
            json={"email": "<EMAIL>"},
            headers={"Content-Type": "application/json"}
        )
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data.get("access_token")
            print(f"✅ Authentication successful")
            print(f"   Token: {access_token[:20]}...")
        else:
            print(f"❌ Authentication failed: {login_response.status_code}")
            print(f"   Response: {login_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Authentication error: {str(e)}")
        return False
    
    # Step 2: Test analytics health endpoint (no auth required)
    print("\n2. Testing analytics health endpoint...")
    
    try:
        health_response = requests.get(f"{base_url}/analytics/health")
        
        if health_response.status_code == 200:
            print("✅ Analytics health check successful")
            print(f"   Response: {health_response.json()}")
        else:
            print(f"❌ Analytics health check failed: {health_response.status_code}")
            print(f"   Response: {health_response.text}")
            
    except Exception as e:
        print(f"❌ Analytics health check error: {str(e)}")
    
    # Step 3: Test debug auth endpoint
    print("\n3. Testing debug auth endpoint...")
    
    try:
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        debug_response = requests.get(
            f"{base_url}/analytics/debug/auth",
            headers=headers
        )
        
        if debug_response.status_code == 200:
            print("✅ Debug auth endpoint successful")
            print(f"   Response: {debug_response.json()}")
        else:
            print(f"❌ Debug auth endpoint failed: {debug_response.status_code}")
            print(f"   Response: {debug_response.text}")
            
    except Exception as e:
        print(f"❌ Debug auth endpoint error: {str(e)}")
    
    # Step 4: Test user profile endpoint
    print("\n4. Testing user profile endpoint...")
    
    try:
        profile_response = requests.get(
            f"{base_url}/analytics/user-profile",
            headers=headers
        )
        
        if profile_response.status_code == 200:
            print("✅ User profile endpoint successful")
            print(f"   Response: {profile_response.json()}")
        elif profile_response.status_code == 403:
            print("❌ User profile endpoint returned 403 Forbidden")
            print(f"   Response: {profile_response.text}")
            print("   This might be a CORS or middleware issue")
        else:
            print(f"❌ User profile endpoint failed: {profile_response.status_code}")
            print(f"   Response: {profile_response.text}")
            
    except Exception as e:
        print(f"❌ User profile endpoint error: {str(e)}")
    
    # Step 5: Test event tracking endpoint
    print("\n5. Testing event tracking endpoint...")
    
    try:
        event_data = {
            "event_type": "CHART_VIEW",
            "metadata": {"test": "data"},
            "session_id": "test-session"
        }
        
        event_response = requests.post(
            f"{base_url}/analytics/events",
            json=event_data,
            headers=headers
        )
        
        if event_response.status_code == 200:
            print("✅ Event tracking endpoint successful")
            print(f"   Response: {event_response.json()}")
        else:
            print(f"❌ Event tracking endpoint failed: {event_response.status_code}")
            print(f"   Response: {event_response.text}")
            
    except Exception as e:
        print(f"❌ Event tracking endpoint error: {str(e)}")
    
    # Step 6: Test file upload event endpoint
    print("\n6. Testing file upload event endpoint...")
    
    try:
        file_upload_data = {
            "file_name": "test.csv",
            "file_size": 1024,
            "file_type": "csv",
            "session_id": "test-session"
        }
        
        file_upload_response = requests.post(
            f"{base_url}/analytics/events/file-upload",
            json=file_upload_data,
            headers=headers
        )
        
        if file_upload_response.status_code == 200:
            print("✅ File upload event endpoint successful")
            print(f"   Response: {file_upload_response.json()}")
        else:
            print(f"❌ File upload event endpoint failed: {file_upload_response.status_code}")
            print(f"   Response: {file_upload_response.text}")
            
    except Exception as e:
        print(f"❌ File upload event endpoint error: {str(e)}")
    
    # Step 7: Test chart event endpoint
    print("\n7. Testing chart event endpoint...")
    
    try:
        chart_event_data = {
            "event_type": "CHART_SAVE",
            "chart_type": "bar",
            "chart_id": "test-chart-123",
            "session_id": "test-session"
        }
        
        chart_event_response = requests.post(
            f"{base_url}/analytics/events/chart",
            json=chart_event_data,
            headers=headers
        )
        
        if chart_event_response.status_code == 200:
            print("✅ Chart event endpoint successful")
            print(f"   Response: {chart_event_response.json()}")
        else:
            print(f"❌ Chart event endpoint failed: {chart_event_response.status_code}")
            print(f"   Response: {chart_event_response.text}")
            
    except Exception as e:
        print(f"❌ Chart event endpoint error: {str(e)}")
    
    # Step 8: Test page view event endpoint
    print("\n8. Testing page view event endpoint...")
    
    try:
        page_view_data = {
            "page_url": "https://test.com/dashboard",
            "page_title": "Test Dashboard",
            "session_id": "test-session"
        }
        
        page_view_response = requests.post(
            f"{base_url}/analytics/events/page-view",
            json=page_view_data,
            headers=headers
        )
        
        if page_view_response.status_code == 200:
            print("✅ Page view event endpoint successful")
            print(f"   Response: {page_view_response.json()}")
        else:
            print(f"❌ Page view event endpoint failed: {page_view_response.status_code}")
            print(f"   Response: {page_view_response.text}")
            
    except Exception as e:
        print(f"❌ Page view event endpoint error: {str(e)}")
    
    print("\n🎯 Test Summary:")
    print("If you see 403 Forbidden errors, check:")
    print("1. CORS configuration in main.py")
    print("2. JWT_SECRET_KEY in environment variables")
    print("3. Token format and validity")
    print("4. FastAPI middleware configuration")
    
    return True

if __name__ == "__main__":
    print("🧪 Analytics Authentication Test Script")
    print("Make sure your FastAPI server is running on http://127.0.0.1:8000")
    
    input("Press Enter to continue...")
    
    success = test_authentication()
    sys.exit(0 if success else 1)

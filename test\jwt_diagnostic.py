#!/usr/bin/env python3
"""
JWT Diagnostic Script
This script helps diagnose JWT token issues by testing token creation, verification, and configuration.
"""

import requests
import json
import base64
from datetime import datetime, timezone
import sys

def decode_jwt_payload(token):
    """Decode JWT payload without verification to inspect contents."""
    try:
        # Split the token into parts
        parts = token.split('.')
        if len(parts) != 3:
            return None, "Invalid JWT format - should have 3 parts"
        
        # Decode the payload (second part)
        payload_b64 = parts[1]
        # Add padding if needed
        payload_b64 += '=' * (4 - len(payload_b64) % 4)
        payload_bytes = base64.urlsafe_b64decode(payload_b64)
        payload = json.loads(payload_bytes.decode('utf-8'))
        
        return payload, None
    except Exception as e:
        return None, f"Error decoding JWT: {str(e)}"

def test_server_health():
    """Test if the server is running and healthy."""
    try:
        response = requests.get('http://127.0.0.1:8000/v1/health', timeout=5)
        return response.status_code == 200, response.status_code
    except Exception as e:
        return False, str(e)

def test_debug_token_creation():
    """Test creating fresh tokens using the debug endpoint."""
    try:
        response = requests.post('http://127.0.0.1:8000/v1/debug-token', timeout=10)
        if response.status_code == 200:
            result = response.json()
            return True, result
        else:
            return False, f"Status {response.status_code}: {response.text}"
    except Exception as e:
        return False, str(e)

def test_token_verification(access_token):
    """Test token verification by making an authenticated request."""
    try:
        headers = {'Authorization': f'Bearer {access_token}'}
        response = requests.get('http://127.0.0.1:8000/v1/analytics/user-profile', headers=headers, timeout=10)
        return response.status_code == 200, response.status_code, response.text
    except Exception as e:
        return False, None, str(e)

def analyze_your_tokens():
    """Analyze the tokens you provided."""
    print("\n" + "="*60)
    print("ANALYZING YOUR PROVIDED TOKENS")
    print("="*60)
    
    # Your tokens (you mentioned these in the issue)
    access_token_payload = {
        "sub": "<EMAIL>",
        "exp": 1749889818
    }
    
    refresh_token_payload = {
        "sub": "<EMAIL>", 
        "exp": 1750491018
    }
    
    current_timestamp = datetime.now(timezone.utc).timestamp()
    
    print(f"Current timestamp: {current_timestamp}")
    print(f"Current time: {datetime.now(timezone.utc)}")
    
    print(f"\nAccess Token Analysis:")
    print(f"  Subject: {access_token_payload['sub']}")
    print(f"  Expiry timestamp: {access_token_payload['exp']}")
    print(f"  Expiry time: {datetime.fromtimestamp(access_token_payload['exp'], tz=timezone.utc)}")
    print(f"  Is expired: {access_token_payload['exp'] < current_timestamp}")
    
    print(f"\nRefresh Token Analysis:")
    print(f"  Subject: {refresh_token_payload['sub']}")
    print(f"  Expiry timestamp: {refresh_token_payload['exp']}")
    print(f"  Expiry time: {datetime.fromtimestamp(refresh_token_payload['exp'], tz=timezone.utc)}")
    print(f"  Is expired: {refresh_token_payload['exp'] < current_timestamp}")

def main():
    """Main diagnostic function."""
    print("JWT Authentication Diagnostic Tool")
    print("=" * 50)
    
    # Test 1: Server Health
    print("\n1. Testing Server Health...")
    is_healthy, status = test_server_health()
    if is_healthy:
        print("✅ Server is healthy and responding")
    else:
        print(f"❌ Server health check failed: {status}")
        return False
    
    # Test 2: Analyze your provided tokens
    analyze_your_tokens()
    
    # Test 3: Create fresh tokens
    print("\n" + "="*60)
    print("TESTING FRESH TOKEN CREATION")
    print("="*60)
    
    print("\n2. Testing Fresh Token Creation...")
    success, result = test_debug_token_creation()
    
    if not success:
        print(f"❌ Failed to create fresh tokens: {result}")
        return False
    
    print("✅ Fresh tokens created successfully!")
    
    if result.get('status') == 'success':
        tokens = result.get('tokens', {})
        jwt_config = result.get('jwt_config', {})
        
        print(f"\nJWT Configuration:")
        for key, value in jwt_config.items():
            print(f"  {key}: {value}")
        
        access_token = tokens.get('access_token')
        refresh_token = tokens.get('refresh_token')
        
        if access_token:
            print(f"\nFresh Access Token (first 50 chars): {access_token[:50]}...")
            
            # Decode and analyze the fresh token
            payload, error = decode_jwt_payload(access_token)
            if payload:
                print(f"Fresh Token Payload:")
                print(f"  Subject: {payload.get('sub')}")
                print(f"  Expiry: {datetime.fromtimestamp(payload.get('exp'), tz=timezone.utc)}")
                print(f"  Algorithm: {payload.get('alg', 'Not specified')}")
            else:
                print(f"❌ Error decoding fresh token: {error}")
        
        # Test 4: Verify fresh token
        print("\n3. Testing Fresh Token Verification...")
        if access_token:
            is_valid, status_code, response_text = test_token_verification(access_token)
            if is_valid:
                print("✅ Fresh token verification successful!")
                print("✅ Authentication is working correctly with fresh tokens")
            else:
                print(f"❌ Fresh token verification failed!")
                print(f"Status Code: {status_code}")
                print(f"Response: {response_text[:200]}...")
        
        # Test 5: Compare configurations
        print("\n" + "="*60)
        print("ROOT CAUSE ANALYSIS")
        print("="*60)
        
        print("\nPossible causes of 'Signature verification failed':")
        print("1. ❓ Tokens were created with different JWT secret keys")
        print("2. ❓ Environment variables changed between token creation and verification")
        print("3. ❓ Server restart with different configuration")
        print("4. ❓ Tokens were created on a different environment/server")
        
        print(f"\nRecommendations:")
        print("1. 🔄 Use fresh tokens created by this server instance")
        print("2. 🔍 Check if JWT_SECRET_KEY and JWT_REFRESH_SECRET_KEY changed")
        print("3. 🔄 Clear browser storage and re-authenticate")
        print("4. 📝 Use the debug login endpoint for testing: POST /v1/auth/debug-login")
        
        if is_valid:
            print("\n✅ CONCLUSION: JWT authentication is working correctly!")
            print("   The issue is likely that your tokens were created with different secret keys.")
            print("   Solution: Re-authenticate to get fresh tokens.")
        else:
            print("\n❌ CONCLUSION: There's a configuration issue with JWT authentication.")
            print("   Check the server logs and environment variables.")
    
    else:
        print(f"❌ Token creation failed: {result}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    
    print("\n" + "="*60)
    print("QUICK FIX COMMANDS")
    print("="*60)
    print("\nTo get fresh working tokens, use one of these:")
    print("\n1. Debug Login (for testing):")
    print('   curl -X POST "http://127.0.0.1:8000/v1/auth/debug-login" \\')
    print('     -H "Content-Type: application/json" \\')
    print('     -d \'{"email": "<EMAIL>"}\'')
    
    print("\n2. Google OAuth Login:")
    print('   Use your frontend to authenticate with Google OAuth')
    
    print("\n3. Debug Token Creation:")
    print('   curl -X POST "http://127.0.0.1:8000/v1/debug-token"')
    
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Test script for Analytics functionality

This script tests the analytics services and API endpoints.
"""

import asyncio
import os
import sys
from datetime import datetime
from unittest.mock import Mock

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.analytics.google_sheets_service import GoogleSheetsService
from app.services.analytics.user_profile_service import UserProfileService
from app.services.analytics.event_tracking_service import EventTrackingService
from app.models.analytics import EventType

async def test_google_sheets_service():
    """Test Google Sheets service functionality"""
    print("🧪 Testing Google Sheets Service...")
    
    sheets_service = GoogleSheetsService()
    
    if not sheets_service.is_available():
        print("⚠️  Google Sheets service not available (this is expected if not configured)")
        return True
    
    print("✅ Google Sheets service is available")
    
    # Test user profile save
    test_user_data = {
        'email': '<EMAIL>',
        'first_login_date': datetime.now().isoformat(),
        'last_login_date': datetime.now().isoformat(),
        'login_count': 1,
        'user_agent': 'Test User Agent',
        'ip_address': '127.0.0.1',
        'location': 'Test Location',
        'name': 'Test User',
        'picture': 'https://example.com/picture.jpg'
    }
    
    try:
        success = await sheets_service.save_user_profile(test_user_data)
        if success:
            print("✅ User profile save test passed")
        else:
            print("❌ User profile save test failed")
            return False
    except Exception as e:
        print(f"❌ User profile save test error: {str(e)}")
        return False
    
    # Test event save
    test_event_data = {
        'event_id': 'test-event-123',
        'email': '<EMAIL>',
        'event_type': 'TEST_EVENT',
        'timestamp': datetime.now().isoformat(),
        'metadata': {'test': 'data'},
        'user_agent': 'Test User Agent',
        'ip_address': '127.0.0.1',
        'session_id': 'test-session-123',
        'page_url': 'https://test.com',
        'referrer': 'https://google.com'
    }
    
    try:
        success = await sheets_service.save_event(test_event_data)
        if success:
            print("✅ Event save test passed")
        else:
            print("❌ Event save test failed")
            return False
    except Exception as e:
        print(f"❌ Event save test error: {str(e)}")
        return False
    
    return True

async def test_user_profile_service():
    """Test User Profile service functionality"""
    print("\n👤 Testing User Profile Service...")
    
    user_profile_service = UserProfileService()
    
    # Mock Google user data
    google_user_data = {
        'email': '<EMAIL>',
        'name': 'Test User',
        'picture': 'https://example.com/picture.jpg'
    }
    
    try:
        # Test user login tracking
        success = await user_profile_service.track_user_login(
            email='<EMAIL>',
            google_user_data=google_user_data,
            user_agent='Test User Agent',
            ip_address='127.0.0.1',
            location='Test Location'
        )
        
        if success or not user_profile_service.sheets_service.is_available():
            print("✅ User login tracking test passed")
        else:
            print("❌ User login tracking test failed")
            return False
            
    except Exception as e:
        print(f"❌ User login tracking test error: {str(e)}")
        return False
    
    # Test request info extraction
    mock_request = Mock()
    mock_request.headers = {
        'user-agent': 'Test User Agent',
        'x-forwarded-for': '127.0.0.1',
        'referer': 'https://google.com'
    }
    mock_request.client.host = '127.0.0.1'
    
    try:
        request_info = user_profile_service.extract_user_info_from_request(mock_request)
        expected_keys = ['user_agent', 'ip_address', 'location']
        
        if all(key in request_info for key in expected_keys):
            print("✅ Request info extraction test passed")
        else:
            print("❌ Request info extraction test failed")
            return False
            
    except Exception as e:
        print(f"❌ Request info extraction test error: {str(e)}")
        return False
    
    return True

async def test_event_tracking_service():
    """Test Event Tracking service functionality"""
    print("\n📊 Testing Event Tracking Service...")
    
    event_tracking_service = EventTrackingService()
    
    try:
        # Test event tracking
        event_id = await event_tracking_service.track_event(
            email='<EMAIL>',
            event_type=EventType.CHART_VIEW,
            metadata={'chart_type': 'bar', 'chart_id': 'test-chart-123'},
            user_agent='Test User Agent',
            ip_address='127.0.0.1',
            session_id='test-session-123',
            page_url='https://test.com/charts',
            referrer='https://google.com'
        )
        
        if event_id or not event_tracking_service.sheets_service.is_available():
            print("✅ Event tracking test passed")
        else:
            print("❌ Event tracking test failed")
            return False
            
    except Exception as e:
        print(f"❌ Event tracking test error: {str(e)}")
        return False
    
    try:
        # Test login event tracking
        event_id = await event_tracking_service.track_login_event(
            email='<EMAIL>',
            user_agent='Test User Agent',
            ip_address='127.0.0.1',
            session_id='test-session-123'
        )
        
        if event_id or not event_tracking_service.sheets_service.is_available():
            print("✅ Login event tracking test passed")
        else:
            print("❌ Login event tracking test failed")
            return False
            
    except Exception as e:
        print(f"❌ Login event tracking test error: {str(e)}")
        return False
    
    try:
        # Test file upload event tracking
        event_id = await event_tracking_service.track_file_upload_event(
            email='<EMAIL>',
            file_name='test.csv',
            file_size=1024,
            file_type='csv',
            user_agent='Test User Agent',
            ip_address='127.0.0.1',
            session_id='test-session-123'
        )
        
        if event_id or not event_tracking_service.sheets_service.is_available():
            print("✅ File upload event tracking test passed")
        else:
            print("❌ File upload event tracking test failed")
            return False
            
    except Exception as e:
        print(f"❌ File upload event tracking test error: {str(e)}")
        return False
    
    # Test request info extraction
    mock_request = Mock()
    mock_request.headers = {
        'user-agent': 'Test User Agent',
        'x-forwarded-for': '127.0.0.1',
        'referer': 'https://google.com'
    }
    mock_request.client.host = '127.0.0.1'
    mock_request.url = 'https://test.com/charts'
    
    try:
        request_info = event_tracking_service.extract_request_info(mock_request)
        expected_keys = ['user_agent', 'ip_address', 'referrer', 'page_url']
        
        if all(key in request_info for key in expected_keys):
            print("✅ Request info extraction test passed")
        else:
            print("❌ Request info extraction test failed")
            return False
            
    except Exception as e:
        print(f"❌ Request info extraction test error: {str(e)}")
        return False
    
    return True

async def run_all_tests():
    """Run all analytics tests"""
    print("🚀 Running Analytics Tests...\n")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    tests = [
        test_google_sheets_service,
        test_user_profile_service,
        test_event_tracking_service
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append(False)
    
    print(f"\n📊 Test Results:")
    print(f"✅ Passed: {sum(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}")
    print(f"📈 Success Rate: {(sum(results) / len(results)) * 100:.1f}%")
    
    if all(results):
        print("\n🎉 All tests passed!")
        return True
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)

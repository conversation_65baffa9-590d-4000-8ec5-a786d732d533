#!/usr/bin/env python3
"""
Test analytics tracking after Google Sheets fix
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://127.0.0.1:8000"
ANALYTICS_ENDPOINT = f"{BASE_URL}/v1/analytics/events"

def test_analytics_tracking():
    """Test that analytics events are now being tracked properly."""
    print("Testing Analytics Tracking After Fix")
    print("=" * 50)
    
    # Check if server is running
    try:
        health_response = requests.get(f"{BASE_URL}/v1/health", timeout=5)
        if health_response.status_code != 200:
            print(f"❌ Server health check failed. Status: {health_response.status_code}")
            return False
        print("✅ Server is running and healthy")
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to server. Make sure the FastAPI server is running.")
        return False
    
    # Test event tracking
    test_event = {
        "event_type": "NAVIGATION",
        "metadata": {
            "page": "test_page",
            "action": "analytics_test"
        },
        "session_id": "test_session_123",
        "page_url": "http://localhost:3000/test",
        "referrer": "http://localhost:3000/"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": "Bearer test_token"  # You might need a real token
    }
    
    print(f"\nSending analytics event to: {ANALYTICS_ENDPOINT}")
    print("Event data:")
    print(json.dumps(test_event, indent=2))
    
    try:
        response = requests.post(
            ANALYTICS_ENDPOINT,
            json=test_event,
            headers=headers,
            timeout=10
        )
        
        print(f"\nResponse Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ Analytics event tracked successfully!")
                print(f"   Message: {result.get('message')}")
                return True
            else:
                print(f"❌ Analytics tracking failed: {result.get('message')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def test_google_sheets_service_directly():
    """Test the Google Sheets service directly."""
    print("\nTesting Google Sheets Service Directly")
    print("=" * 50)
    
    try:
        from app.services.analytics.google_sheets_service import GoogleSheetsService
        
        service = GoogleSheetsService()
        
        print(f"Service available: {service.is_available()}")
        print(f"Events sheet available: {service.events_sheet is not None}")
        
        if service.events_sheet:
            print("✅ Google Sheets service is working correctly")
            return True
        else:
            print("❌ Events sheet not available")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Google Sheets service: {str(e)}")
        return False

def main():
    """Main test function."""
    print("Analytics Fix Verification Test")
    print("=" * 60)
    
    # Test 1: Google Sheets Service
    sheets_ok = test_google_sheets_service_directly()
    
    # Test 2: Analytics API (if sheets are working)
    if sheets_ok:
        analytics_ok = test_analytics_tracking()
    else:
        print("\nSkipping analytics API test due to Google Sheets issues")
        analytics_ok = False
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    if sheets_ok and analytics_ok:
        print("🎉 All tests passed! Google Sheets analytics is working correctly.")
    elif sheets_ok:
        print("⚠️  Google Sheets is working, but analytics API test failed.")
        print("   This might be due to authentication requirements.")
    else:
        print("❌ Google Sheets service is still not working.")
        print("   Check the configuration and permissions.")
    
    return sheets_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

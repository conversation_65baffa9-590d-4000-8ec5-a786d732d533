#!/usr/bin/env python3
"""
Test script to verify the fix for box chart and multi-group bar chart data processing.
This script tests the data extraction logic to ensure it properly handles these chart types.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.chart_insights.data_processor import ChartDataProcessor
from app.services.chart_insights.models import InsightGenerationContext
from app.services.chart_insights.unified_insights_service import UnifiedInsightsService

def test_box_chart_data_processing():
    """Test box chart data processing."""
    print("Testing Box Chart Data Processing...")
    
    # Single box chart data structure (as generated by distribution_charts.py)
    single_box_data = {
        "chart_type": "box",
        "library": "plotly",
        "data": {
            "y": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            "type": "box"
        },
        "layout": {
            "title": "Test Box Chart",
            "yaxis": {"title": "Values"}
        }
    }
    
    # Grouped box chart data structure
    grouped_box_data = {
        "chart_type": "box",
        "library": "plotly",
        "data": [
            {
                "y": [1, 2, 3, 4, 5],
                "name": "Group A",
                "type": "box"
            },
            {
                "y": [6, 7, 8, 9, 10],
                "name": "Group B", 
                "type": "box"
            }
        ],
        "layout": {
            "title": "Test Grouped Box Chart",
            "xaxis": {"title": "Groups"},
            "yaxis": {"title": "Values"}
        }
    }
    
    processor = ChartDataProcessor()
    
    # Test single box chart
    print("\n1. Testing single box chart...")
    processed_single = processor.process_chart_data(single_box_data)
    print(f"   Data points extracted: {len(processed_single.data_points)}")
    print(f"   Numeric values: {len(processed_single.numeric_values)}")
    print(f"   Categories: {len(processed_single.categories)}")
    print(f"   Sample data point: {processed_single.data_points[0] if processed_single.data_points else 'None'}")
    
    # Test grouped box chart
    print("\n2. Testing grouped box chart...")
    processed_grouped = processor.process_chart_data(grouped_box_data)
    print(f"   Data points extracted: {len(processed_grouped.data_points)}")
    print(f"   Numeric values: {len(processed_grouped.numeric_values)}")
    print(f"   Categories: {len(processed_grouped.categories)}")
    print(f"   Sample data point: {processed_grouped.data_points[0] if processed_grouped.data_points else 'None'}")
    
    return processed_single.data_points and processed_grouped.data_points

def test_multi_group_bar_chart_data_processing():
    """Test multi-group bar chart data processing."""
    print("\nTesting Multi-Group Bar Chart Data Processing...")
    
    # Multi-group bar chart data structure (as generated by multi_group_charts.py)
    multi_group_bar_data = {
        "chart_type": "grouped_bar",
        "library": "plotly",
        "data": [
            {
                "x": ["Q1", "Q2", "Q3", "Q4"],
                "y": [100, 120, 140, 160],
                "name": "2023",
                "type": "bar"
            },
            {
                "x": ["Q1", "Q2", "Q3", "Q4"],
                "y": [110, 130, 150, 170],
                "name": "2024",
                "type": "bar"
            }
        ],
        "layout": {
            "barmode": "group",
            "title": "Quarterly Sales by Year",
            "xaxis": {"title": "Quarter"},
            "yaxis": {"title": "Sales"}
        }
    }
    
    processor = ChartDataProcessor()
    
    print("\n1. Testing multi-group bar chart...")
    processed = processor.process_chart_data(multi_group_bar_data)
    print(f"   Data points extracted: {len(processed.data_points)}")
    print(f"   Numeric values: {len(processed.numeric_values)}")
    print(f"   Categories: {len(processed.categories)}")
    print(f"   Sample data point: {processed.data_points[0] if processed.data_points else 'None'}")
    
    return bool(processed.data_points)

async def test_insights_generation():
    """Test that insights can be generated with the fixed data processing."""
    print("\nTesting Insights Generation...")
    
    # Test data that should work now
    test_data = {
        "chart_type": "box",
        "library": "plotly", 
        "data": {
            "y": [10, 15, 12, 18, 20, 25, 22, 30, 28, 35],
            "type": "box"
        },
        "layout": {
            "title": "Test Box Chart for Insights",
            "yaxis": {"title": "Values"}
        }
    }
    
    try:
        processor = ChartDataProcessor()
        processed_data = processor.process_chart_data(test_data)
        
        context = InsightGenerationContext(
            processed_data=processed_data,
            chart_title="Test Box Chart for Insights",
            chart_type="box",
            llm_provider="openai"  # Add required field
        )
        
        # This should not raise InsufficientDataError anymore
        insights_service = UnifiedInsightsService()
        insights = await insights_service.generate_unified_insights(context)
        
        print("   ✓ Insights generation successful!")
        print(f"   Executive summary items: {len(insights.get('executive_summary', []))}")
        return True
        
    except Exception as e:
        print(f"   ✗ Insights generation failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("TESTING CHART DATA PROCESSING FIX")
    print("=" * 60)
    
    # Test data processing
    box_test_passed = test_box_chart_data_processing()
    bar_test_passed = test_multi_group_bar_chart_data_processing()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS")
    print("=" * 60)
    print(f"Box Chart Processing: {'✓ PASSED' if box_test_passed else '✗ FAILED'}")
    print(f"Multi-Group Bar Chart Processing: {'✓ PASSED' if bar_test_passed else '✗ FAILED'}")
    
    # Test insights generation (async)
    import asyncio
    insights_test_passed = asyncio.run(test_insights_generation())
    print(f"Insights Generation: {'✓ PASSED' if insights_test_passed else '✗ FAILED'}")
    
    overall_success = box_test_passed and bar_test_passed and insights_test_passed
    print(f"\nOverall: {'✓ ALL TESTS PASSED' if overall_success else '✗ SOME TESTS FAILED'}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

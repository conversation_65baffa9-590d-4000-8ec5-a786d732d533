#!/usr/bin/env python3
"""
Test script for the optimized Chart Insights API
This script demonstrates how to test the new comprehensive chart insights endpoint.
"""

import requests
import json
import sys
from typing import Dict, Any

# Configuration
BASE_URL = "http://127.0.0.1:8000"  # Adjust if your server runs on a different port
API_ENDPOINT = f"{BASE_URL}/v1/chart/insights"

def create_sample_bar_chart_data() -> Dict[str, Any]:
    """Create sample bar chart data for testing."""
    return {
        "chart_data": {
            "type": "bar",
            "title": "Monthly Sales Performance",
            "x": ["January", "February", "March", "April", "May", "June"],
            "y": [12000, 15000, 18000, 14000, 22000, 25000],
            "layout": {
                "title": "Monthly Sales Performance Q1-Q2 2024",
                "xaxis": {"title": "Month"},
                "yaxis": {"title": "Sales ($)"}
            }
        },
        "chart_type": "bar",
        "chart_title": "Monthly Sales Performance Q1-Q2 2024",
        "include_executive_summary": True,
        "include_data_insights": True,
        "include_hidden_patterns": True,
        "include_recommendations": True
    }

def create_sample_line_chart_data() -> Dict[str, Any]:
    """Create sample line chart data for testing."""
    return {
        "chart_data": {
            "type": "line",
            "title": "Website Traffic Trends",
            "x": ["Week 1", "Week 2", "Week 3", "Week 4", "Week 5", "Week 6", "Week 7", "Week 8"],
            "y": [1200, 1350, 1100, 1450, 1600, 1750, 1900, 2100],
            "layout": {
                "title": "Website Traffic Trends - 8 Week Period",
                "xaxis": {"title": "Time Period"},
                "yaxis": {"title": "Unique Visitors"}
            }
        },
        "chart_type": "line",
        "chart_title": "Website Traffic Trends - 8 Week Period",
        "include_executive_summary": True,
        "include_data_insights": True,
        "include_hidden_patterns": True,
        "include_recommendations": True
    }

def create_sample_pie_chart_data() -> Dict[str, Any]:
    """Create sample pie chart data for testing."""
    return {
        "chart_data": {
            "type": "pie",
            "title": "Market Share Distribution",
            "labels": ["Product A", "Product B", "Product C", "Product D", "Others"],
            "values": [35, 25, 20, 15, 5],
            "layout": {
                "title": "Market Share Distribution by Product"
            }
        },
        "chart_type": "pie",
        "chart_title": "Market Share Distribution by Product",
        "include_executive_summary": True,
        "include_data_insights": True,
        "include_hidden_patterns": True,
        "include_recommendations": True
    }

def test_chart_insights_api(test_data: Dict[str, Any], test_name: str) -> bool:
    """
    Test the chart insights API with the provided data.
    
    Args:
        test_data: The test data to send to the API
        test_name: Name of the test for logging
    
    Returns:
        bool: True if test passed, False otherwise
    """
    try:
        print(f"\n{'='*60}")
        print(f"Testing: {test_name}")
        print(f"{'='*60}")
        
        # Make the API request
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        print(f"Sending request to: {API_ENDPOINT}")
        print(f"Request payload preview: {json.dumps(test_data, indent=2)[:500]}...")
        
        response = requests.post(
            API_ENDPOINT,
            json=test_data,
            headers=headers,
            timeout=30
        )
        
        print(f"Response Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("\n✅ SUCCESS - API Response:")
            print("-" * 40)
            
            # Display executive summary
            if result.get("executive_summary"):
                print("\n📊 EXECUTIVE SUMMARY:")
                for i, point in enumerate(result["executive_summary"], 1):
                    print(f"  {i}. {point}")
            
            # Display data insights
            if result.get("data_insights"):
                print("\n🔍 DATA INSIGHTS:")
                for i, insight in enumerate(result["data_insights"], 1):
                    print(f"  {i}. {insight}")
            
            # Display hidden patterns
            if result.get("hidden_patterns"):
                print("\n🕵️ HIDDEN PATTERNS:")
                for i, pattern in enumerate(result["hidden_patterns"], 1):
                    print(f"  {i}. {pattern}")
            
            # Display recommendations
            if result.get("recommendations"):
                print("\n💡 RECOMMENDATIONS:")
                for i, rec in enumerate(result["recommendations"], 1):
                    print(f"  {i}. {rec}")
            
            # Display key metrics
            if result.get("key_metrics"):
                print("\n📈 KEY METRICS:")
                for key, value in result["key_metrics"].items():
                    print(f"  • {key}: {value}")
            
            # Display metadata
            if result.get("chart_metadata"):
                print("\n📋 METADATA:")
                for key, value in result["chart_metadata"].items():
                    print(f"  • {key}: {value}")
            
            return True
            
        else:
            print(f"\n❌ FAILED - Status Code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"\n❌ REQUEST ERROR: {str(e)}")
        return False
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {str(e)}")
        return False

def generate_curl_commands():
    """Generate sample cURL commands for testing."""
    print("\n" + "="*80)
    print("SAMPLE cURL COMMANDS FOR TESTING")
    print("="*80)
    
    # Bar chart cURL command
    bar_data = create_sample_bar_chart_data()
    print("\n1. Bar Chart Analysis:")
    print("-" * 40)
    print(f"""curl -X POST "{API_ENDPOINT}" \\
  -H "Content-Type: application/json" \\
  -H "Accept: application/json" \\
  -d '{json.dumps(bar_data, indent=2)}'""")
    
    # Line chart cURL command
    line_data = create_sample_line_chart_data()
    print("\n2. Line Chart Analysis:")
    print("-" * 40)
    print(f"""curl -X POST "{API_ENDPOINT}" \\
  -H "Content-Type: application/json" \\
  -H "Accept: application/json" \\
  -d '{json.dumps(line_data, indent=2)}'""")
    
    # Pie chart cURL command
    pie_data = create_sample_pie_chart_data()
    print("\n3. Pie Chart Analysis:")
    print("-" * 40)
    print(f"""curl -X POST "{API_ENDPOINT}" \\
  -H "Content-Type: application/json" \\
  -H "Accept: application/json" \\
  -d '{json.dumps(pie_data, indent=2)}'""")

def main():
    """Main test function."""
    print("Chart Insights API Test Suite")
    print("=" * 50)
    
    # Check if server is running
    try:
        health_response = requests.get(f"{BASE_URL}/v1/health", timeout=5)
        if health_response.status_code != 200:
            print(f"❌ Server health check failed. Status: {health_response.status_code}")
            sys.exit(1)
        print("✅ Server is running and healthy")
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to server. Make sure the FastAPI server is running.")
        print(f"Expected server URL: {BASE_URL}")
        sys.exit(1)
    
    # Run tests
    test_results = []
    
    # Test 1: Bar Chart
    test_results.append(
        test_chart_insights_api(
            create_sample_bar_chart_data(),
            "Bar Chart - Monthly Sales Performance"
        )
    )
    
    # Test 2: Line Chart
    test_results.append(
        test_chart_insights_api(
            create_sample_line_chart_data(),
            "Line Chart - Website Traffic Trends"
        )
    )
    
    # Test 3: Pie Chart
    test_results.append(
        test_chart_insights_api(
            create_sample_pie_chart_data(),
            "Pie Chart - Market Share Distribution"
        )
    )
    
    # Generate cURL commands
    generate_curl_commands()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    passed = sum(test_results)
    total = len(test_results)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Integration test for new chart types with the full recommendation and data processing pipeline
"""

import pandas as pd
from app.services.chart_recommender import ChartData
from app.services.data_aggregator.base import DataAggregator

def create_comprehensive_test_data():
    """Create comprehensive test data that would trigger all new chart types."""
    return pd.DataFrame({
        'category': ['Product A', 'Product B', 'Product C', 'Product D', 'Product E'],
        'region': ['North', 'South', 'East', 'West', 'Central'],
        'sales': [10000, 25000, 30000, 45000, 20000],
        'profit': [1500, 3750, 4500, 6750, 3000],
        'units_sold': [100, 250, 300, 450, 200],
        'customer_satisfaction': [4.2, 4.5, 4.8, 4.1, 4.6],
        'market_share': [15.5, 22.3, 18.7, 25.1, 18.4]
    })

def test_chart_recommendations():
    """Test that new chart types are being recommended."""
    print("Testing Chart Recommendations")
    print("=" * 50)
    
    # Create test data and metadata
    df = create_comprehensive_test_data()
    
    # Create metadata that would be generated by the system
    metadata = {
        "numerical_columns": ["sales", "profit", "units_sold", "customer_satisfaction", "market_share"],
        "categorical_columns": ["category", "region"],
        "datetime_columns": [],
        "unique_counts": {
            "category": 5,
            "region": 5
        },
        "column_types": {
            "category": "object",
            "region": "object", 
            "sales": "int64",
            "profit": "int64",
            "units_sold": "int64",
            "customer_satisfaction": "float64",
            "market_share": "float64"
        }
    }
    
    # Get recommendations
    chart_data_service = ChartData()
    recommendations = chart_data_service.get_rule_based_recommendations(metadata)
    
    # Check for new chart types in recommendations
    new_chart_types = ["horizontal_bar", "column", "polar_area", "funnel", "scatter_matrix"]
    found_chart_types = []
    
    print(f"Total recommendations: {len(recommendations)}")
    print("\nRecommended chart types:")
    
    for rec in recommendations:
        chart_type = rec.get("chart_type")
        print(f"  - {chart_type}")
        if chart_type in new_chart_types:
            found_chart_types.append(chart_type)
    
    print(f"\nNew chart types found in recommendations: {found_chart_types}")
    
    return recommendations, found_chart_types

def test_end_to_end_processing():
    """Test end-to-end processing of new chart types."""
    print("\nTesting End-to-End Processing")
    print("=" * 50)
    
    df = create_comprehensive_test_data()
    aggregator = DataAggregator()
    
    # Test specific chart configurations
    test_configs = [
        {
            "chart_type": "horizontal_bar",
            "fields": {
                "x": "category",
                "x_type": "categorical",
                "numeric": "sales",
                "chart_title": "Sales by Product (Horizontal)"
            }
        },
        {
            "chart_type": "column", 
            "fields": {
                "x": "region",
                "x_type": "categorical",
                "numeric": "profit",
                "chart_title": "Profit by Region (Column)"
            }
        },
        {
            "chart_type": "polar_area",
            "fields": {
                "x": "category",
                "x_type": "categorical", 
                "numeric": "market_share",
                "chart_title": "Market Share Distribution (Polar)"
            }
        },
        {
            "chart_type": "funnel",
            "fields": {
                "x": "category",
                "x_type": "categorical",
                "numeric": "units_sold", 
                "chart_title": "Sales Funnel by Product"
            }
        },
        {
            "chart_type": "scatter_matrix",
            "fields": {
                "chart_title": "Correlation Matrix of Metrics"
            }
        }
    ]
    
    results = []
    for config in test_configs:
        chart_type = config["chart_type"]
        print(f"\nProcessing {chart_type}...")
        
        try:
            result = aggregator.prepare_chart_data(df, chart_type, config["fields"])
            if result:
                print(f"✅ {chart_type} - Successfully processed")
                print(f"   Title: {result.get('layout', {}).get('title', 'N/A')}")
                print(f"   Data keys: {list(result.get('data', {}).keys())}")
                results.append((chart_type, True, result))
            else:
                print(f"❌ {chart_type} - Failed (returned None)")
                results.append((chart_type, False, None))
        except Exception as e:
            print(f"❌ {chart_type} - Error: {str(e)}")
            results.append((chart_type, False, None))
    
    return results

def main():
    """Main test function."""
    print("New Chart Types Integration Test")
    print("=" * 60)
    
    # Test 1: Chart Recommendations
    recommendations, found_new_types = test_chart_recommendations()
    
    # Test 2: End-to-End Processing
    processing_results = test_end_to_end_processing()
    
    # Summary
    print("\n" + "=" * 60)
    print("INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    print(f"Total chart recommendations: {len(recommendations)}")
    print(f"New chart types in recommendations: {len(found_new_types)}")
    print(f"New chart types found: {found_new_types}")
    
    successful_processing = sum(1 for _, success, _ in processing_results if success)
    total_processing = len(processing_results)
    
    print(f"\nEnd-to-end processing results: {successful_processing}/{total_processing}")
    for chart_type, success, _ in processing_results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status} - {chart_type}")
    
    # Overall assessment
    recommendation_success = len(found_new_types) > 0
    processing_success = successful_processing == total_processing
    
    if recommendation_success and processing_success:
        print("\n🎉 All integration tests passed!")
        print("   - New chart types are being recommended")
        print("   - All new chart types process data correctly")
        return True
    else:
        print("\n⚠️  Some integration tests failed:")
        if not recommendation_success:
            print("   - New chart types not found in recommendations")
        if not processing_success:
            print("   - Some chart types failed data processing")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

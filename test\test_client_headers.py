#!/usr/bin/env python3
"""
Example client script showing how to send common headers to the API.

This script demonstrates the proper way to send standard headers
that will be automatically processed by the common headers system.
"""

import requests
import uuid
import json
from datetime import datetime

# Base URL for the API (adjust as needed)
BASE_URL = "http://localhost:8000/v1"

def generate_common_headers():
    """Generate standard headers that the API expects."""
    return {
        # Request tracking headers
        "x-request-id": str(uuid.uuid4()),
        "x-correlation-id": str(uuid.uuid4()),
        "x-session-id": "session-" + str(uuid.uuid4())[:8],
        "x-device-id": "device-" + str(uuid.uuid4())[:8],
        
        # Client information headers
        "x-client-version": "1.0.0",
        "x-platform": "python-client",
        "x-timezone": "America/New_York",
        "accept-language": "en-US,en;q=0.9",
        
        # Standard HTTP headers
        "user-agent": "CommonHeadersTestClient/1.0",
        "content-type": "application/json",
        "accept": "application/json"
    }

def test_public_endpoint():
    """Test the public endpoint that doesn't require authentication."""
    print("Testing public endpoint...")
    
    headers = generate_common_headers()
    
    try:
        response = requests.get(f"{BASE_URL}/example/public", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("   ✓ Public endpoint responded successfully")
            print(f"   ✓ Request ID: {data['request_context']['request_id']}")
            print(f"   ✓ Platform: {data['request_context']['platform']}")
            print(f"   ✓ Client Version: {data['request_context']['client_version']}")
            return True
        else:
            print(f"   ✗ Public endpoint failed: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ⚠️  Server not running. Start with: uvicorn app.main:app --reload")
        return False
    except Exception as e:
        print(f"   ✗ Error testing public endpoint: {e}")
        return False

def test_headers_summary():
    """Test the headers summary endpoint."""
    print("\nTesting headers summary endpoint...")
    
    headers = generate_common_headers()
    
    try:
        response = requests.get(f"{BASE_URL}/example/headers-summary", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("   ✓ Headers summary endpoint responded successfully")
            
            # Check that our headers were processed
            available_headers = data['available_headers']
            
            tracking = available_headers['tracking']
            client_info = available_headers['client_info']
            
            print(f"   ✓ Tracking headers processed:")
            print(f"      - Request ID: {tracking['request_id']}")
            print(f"      - Session ID: {tracking['session_id']}")
            
            print(f"   ✓ Client info processed:")
            print(f"      - Platform: {client_info['platform']}")
            print(f"      - Client Version: {client_info['client_version']}")
            print(f"      - Language: {client_info['language']}")
            
            return True
        else:
            print(f"   ✗ Headers summary failed: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ⚠️  Server not running. Start with: uvicorn app.main:app --reload")
        return False
    except Exception as e:
        print(f"   ✗ Error testing headers summary: {e}")
        return False

def test_optional_auth_endpoint():
    """Test the optional auth endpoint without authentication."""
    print("\nTesting optional auth endpoint (no auth)...")
    
    headers = generate_common_headers()
    
    try:
        response = requests.get(f"{BASE_URL}/example/optional-auth", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("   ✓ Optional auth endpoint responded successfully")
            print(f"   ✓ Message: {data['message']}")
            
            if data['user'] is None:
                print("   ✓ Correctly handled anonymous access")
            else:
                print("   ⚠️  Expected anonymous access but got user data")
            
            return True
        else:
            print(f"   ✗ Optional auth endpoint failed: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ⚠️  Server not running. Start with: uvicorn app.main:app --reload")
        return False
    except Exception as e:
        print(f"   ✗ Error testing optional auth endpoint: {e}")
        return False

def test_analytics_endpoint():
    """Test the analytics endpoint."""
    print("\nTesting analytics endpoint...")
    
    headers = generate_common_headers()
    
    # Sample analytics event data
    event_data = {
        "event_type": "page_view",
        "page": "/dashboard",
        "timestamp": datetime.now().isoformat(),
        "metadata": {
            "source": "test_client"
        }
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/example/analytics", 
            headers=headers,
            json=event_data
        )
        
        if response.status_code == 200:
            data = response.json()
            print("   ✓ Analytics endpoint responded successfully")
            print(f"   ✓ Event processed: {data['analytics_data']['event']['event_type']}")
            print(f"   ✓ User authenticated: {data['user_authenticated']}")
            return True
        else:
            print(f"   ✗ Analytics endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ⚠️  Server not running. Start with: uvicorn app.main:app --reload")
        return False
    except Exception as e:
        print(f"   ✗ Error testing analytics endpoint: {e}")
        return False

def demonstrate_header_variations():
    """Demonstrate different header combinations."""
    print("\nDemonstrating header variations...")
    
    # Test with minimal headers
    minimal_headers = {
        "user-agent": "MinimalClient/1.0"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/example/public", headers=minimal_headers)
        if response.status_code == 200:
            data = response.json()
            context = data['request_context']
            print("   ✓ Minimal headers work:")
            print(f"      - Auto-generated request ID: {context['request_id']}")
            print(f"      - User agent: {context['user_agent']}")
            print(f"      - Missing fields handled gracefully")
        
        # Test with custom headers
        custom_headers = generate_common_headers()
        custom_headers.update({
            "x-custom-header": "custom-value",  # This won't be processed but won't break anything
            "x-timezone": "Europe/London",      # This will override the default
            "accept-language": "fr-FR,fr;q=0.9"  # This will be parsed to 'fr'
        })
        
        response = requests.get(f"{BASE_URL}/example/public", headers=custom_headers)
        if response.status_code == 200:
            data = response.json()
            context = data['request_context']
            print("   ✓ Custom headers work:")
            print(f"      - Timezone: {context['timezone']}")
            print(f"      - Language: {context['language']}")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Error demonstrating header variations: {e}")
        return False

def main():
    """Run all client tests."""
    print("=" * 60)
    print("TESTING CLIENT HEADER INTEGRATION")
    print("=" * 60)
    print("This script tests how to properly send headers to the API.")
    print("Make sure the server is running: uvicorn app.main:app --reload")
    print("=" * 60)
    
    tests = [
        test_public_endpoint,
        test_headers_summary,
        test_optional_auth_endpoint,
        test_analytics_endpoint,
        demonstrate_header_variations
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"   ✗ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("CLIENT TEST RESULTS")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    test_names = [
        "Public endpoint",
        "Headers summary",
        "Optional auth endpoint",
        "Analytics endpoint",
        "Header variations"
    ]
    
    for name, result in zip(test_names, results):
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{status} - {name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All client tests passed!")
        print("\nThe common headers system is working correctly.")
        print("Your frontend/mobile apps should send similar headers.")
    else:
        print(f"\n⚠️  {total - passed} tests failed.")
        print("Make sure the server is running and accessible.")
    
    print("\n" + "=" * 60)
    print("EXAMPLE FRONTEND INTEGRATION")
    print("=" * 60)
    print("JavaScript example:")
    print("""
const headers = {
    'x-request-id': crypto.randomUUID(),
    'x-session-id': sessionStorage.getItem('sessionId'),
    'x-device-id': localStorage.getItem('deviceId'),
    'x-client-version': '1.0.0',
    'x-platform': 'web',
    'x-timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
    'Authorization': `Bearer ${accessToken}`
};

fetch('/v1/api/endpoint', {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(data)
});
""")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

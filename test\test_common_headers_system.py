#!/usr/bin/env python3
"""
Test script for the common headers system.

This script tests the new centralized header management system
to ensure it works correctly across different scenarios.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.context import RequestContext, set_request_context, get_request_context
from app.core.headers import sanitize_header_value, extract_language, generate_request_id

def test_request_context():
    """Test RequestContext functionality."""
    print("Testing RequestContext...")
    
    # Create a test context
    context = RequestContext(
        request_id="test-123",
        session_id="session-456",
        device_id="device-789",
        user_agent="Mozilla/5.0 Test Browser",
        ip_address="*************",
        client_version="1.0.0",
        platform="web",
        timezone="America/New_York",
        language="en"
    )
    
    # Test to_dict conversion
    context_dict = context.to_dict()
    
    print(f"   ✓ Context created with {len(context_dict)} fields")
    print(f"   ✓ Request ID: {context.request_id}")
    print(f"   ✓ User Agent: {context.user_agent}")
    print(f"   ✓ IP Address: {context.ip_address}")
    
    return True

def test_context_variables():
    """Test context variable setting and getting."""
    print("\nTesting context variables...")
    
    # Create test context
    test_context = RequestContext(
        request_id="ctx-test-123",
        session_id="ctx-session-456",
        user_agent="Test Agent",
        ip_address="********"
    )
    
    # Set context
    set_request_context(test_context)
    
    # Get context back
    retrieved_context = get_request_context()
    
    # Verify values
    assert retrieved_context.request_id == "ctx-test-123"
    assert retrieved_context.session_id == "ctx-session-456"
    assert retrieved_context.user_agent == "Test Agent"
    assert retrieved_context.ip_address == "********"
    
    print("   ✓ Context variables set and retrieved correctly")
    return True

def test_header_utilities():
    """Test header utility functions."""
    print("\nTesting header utilities...")
    
    # Test sanitize_header_value
    test_cases = [
        ("normal value", "normal value"),
        ("value\x00with\x1fcontrol", "valuewithcontrol"),
        ("very " + "long " * 100 + "value", "very " + "long " * 49 + "long v"),  # Truncated
        (None, ""),
        ("", "")
    ]
    
    for input_val, expected in test_cases:
        result = sanitize_header_value(input_val)
        assert len(result) <= 255, f"Result too long: {len(result)}"
        print(f"   ✓ Sanitized '{str(input_val)[:20]}...' correctly")
    
    # Test extract_language
    language_tests = [
        ("en-US,en;q=0.9,es;q=0.8", "en"),
        ("fr-FR,fr;q=0.9", "fr"),
        ("de", "de"),
        ("", ""),
        (None, "")
    ]
    
    for input_val, expected in language_tests:
        result = extract_language(input_val)
        assert result == expected, f"Expected {expected}, got {result}"
        print(f"   ✓ Language extraction: '{input_val}' -> '{result}'")
    
    # Test generate_request_id
    request_id = generate_request_id()
    assert len(request_id) > 0, "Request ID should not be empty"
    assert "-" in request_id, "Request ID should be UUID format"
    print(f"   ✓ Generated request ID: {request_id}")
    
    return True

def test_auth_dependencies_import():
    """Test that auth dependencies can be imported."""
    print("\nTesting auth dependencies import...")
    
    try:
        from app.core.auth_dependencies import (
            AuthenticatedUser,
            RequireAuth,
            OptionalAuth,
            RequireAdmin,
            AnalyticsAuth
        )
        print("   ✓ All auth dependencies imported successfully")
        return True
    except ImportError as e:
        print(f"   ✗ Import failed: {e}")
        return False

def test_middleware_import():
    """Test that middleware can be imported."""
    print("\nTesting middleware import...")
    
    try:
        from app.core.middleware import (
            CommonHeadersMiddleware,
            SecurityHeadersMiddleware
        )
        print("   ✓ Middleware classes imported successfully")
        return True
    except ImportError as e:
        print(f"   ✗ Import failed: {e}")
        return False

def test_example_endpoint_import():
    """Test that example endpoints can be imported."""
    print("\nTesting example endpoint import...")
    
    try:
        from app.api.v1.endpoints.example_common_headers import router
        print("   ✓ Example endpoints imported successfully")
        print(f"   ✓ Router has {len(router.routes)} routes")
        return True
    except ImportError as e:
        print(f"   ✗ Import failed: {e}")
        return False

def test_integration():
    """Test integration between components."""
    print("\nTesting component integration...")
    
    try:
        # Test that headers module can create context
        from app.core.headers import get_headers_for_analytics, get_headers_for_logging
        
        test_context = RequestContext(
            request_id="integration-test",
            session_id="session-123",
            user_agent="Integration Test Agent",
            ip_address="127.0.0.1",
            client_version="test-1.0",
            platform="test"
        )
        
        # Test analytics format
        analytics_data = get_headers_for_analytics(test_context)
        assert "user_agent" in analytics_data
        assert "ip_address" in analytics_data
        assert "session_id" in analytics_data
        print("   ✓ Analytics header format works")
        
        # Test logging format
        logging_data = get_headers_for_logging(test_context)
        assert "request_id" in logging_data
        assert "user_agent" in logging_data
        assert len(logging_data["user_agent"]) <= 100  # Should be truncated
        print("   ✓ Logging header format works")
        
        return True
    except Exception as e:
        print(f"   ✗ Integration test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("TESTING COMMON HEADERS SYSTEM")
    print("=" * 60)
    
    tests = [
        test_request_context,
        test_context_variables,
        test_header_utilities,
        test_auth_dependencies_import,
        test_middleware_import,
        test_example_endpoint_import,
        test_integration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"   ✗ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("TEST RESULTS")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    test_names = [
        "RequestContext functionality",
        "Context variables",
        "Header utilities",
        "Auth dependencies import",
        "Middleware import",
        "Example endpoints import",
        "Component integration"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{status} - {name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The common headers system is working correctly.")
        print("\nNext steps:")
        print("1. Add the example router to main.py to test the endpoints")
        print("2. Update existing APIs to use the new dependencies")
        print("3. Configure frontend to send the standard headers")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

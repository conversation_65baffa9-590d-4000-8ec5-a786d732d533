#!/usr/bin/env python3
"""
Comprehensive test for all chart types to ensure data processing works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.chart_insights.data_processor import ChartDataProcessor

def test_all_chart_types():
    """Test data processing for all supported chart types."""
    print("Testing All Chart Types Data Processing...")
    
    test_cases = [
        # Box charts
        {
            "name": "Single Box Chart",
            "data": {
                "chart_type": "box",
                "data": {"y": [1, 2, 3, 4, 5], "type": "box"}
            },
            "expected_points": 5
        },
        {
            "name": "Grouped Box Chart", 
            "data": {
                "chart_type": "box",
                "data": [
                    {"y": [1, 2, 3], "name": "A", "type": "box"},
                    {"y": [4, 5, 6], "name": "B", "type": "box"}
                ]
            },
            "expected_points": 6
        },
        
        # Multi-group bar charts
        {
            "name": "Multi-Group Bar Chart",
            "data": {
                "chart_type": "grouped_bar",
                "data": [
                    {"x": ["Q1", "Q2"], "y": [100, 120], "name": "2023", "type": "bar"},
                    {"x": ["Q1", "Q2"], "y": [110, 130], "name": "2024", "type": "bar"}
                ]
            },
            "expected_points": 4
        },
        
        # Funnel charts
        {
            "name": "Funnel Chart",
            "data": {
                "chart_type": "funnel",
                "data": {"y": ["Stage1", "Stage2", "Stage3"], "x": [100, 80, 60], "type": "funnel"}
            },
            "expected_points": 3
        },
        
        # Hierarchical charts (treemap/sunburst)
        {
            "name": "Treemap Chart",
            "data": {
                "chart_type": "treemap",
                "data": {
                    "labels": ["Root", "A", "B", "A1", "A2"],
                    "parents": ["", "Root", "Root", "A", "A"],
                    "values": [100, 50, 30, 25, 25],
                    "type": "treemap"
                }
            },
            "expected_points": 5
        },
        
        # Multi-line charts
        {
            "name": "Multi-Line Chart",
            "data": {
                "chart_type": "multi_line",
                "data": [
                    {"x": [1, 2, 3], "y": [10, 20, 30], "name": "Series1", "type": "scatter"},
                    {"x": [1, 2, 3], "y": [15, 25, 35], "name": "Series2", "type": "scatter"}
                ]
            },
            "expected_points": 6
        },
        
        # Heatmap
        {
            "name": "Heatmap Chart",
            "data": {
                "chart_type": "heatmap",
                "z": [[1, 2, 3], [4, 5, 6]],
                "x": ["A", "B", "C"],
                "y": ["X", "Y"]
            },
            "expected_points": 6
        },
        
        # Standard formats
        {
            "name": "Standard X/Y Chart",
            "data": {
                "chart_type": "line",
                "x": [1, 2, 3, 4],
                "y": [10, 20, 15, 25]
            },
            "expected_points": 4
        },
        
        {
            "name": "Pie Chart",
            "data": {
                "chart_type": "pie",
                "labels": ["A", "B", "C"],
                "values": [30, 40, 30]
            },
            "expected_points": 3
        }
    ]
    
    processor = ChartDataProcessor()
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing {test_case['name']}...")
        
        try:
            processed = processor.process_chart_data(test_case['data'])
            actual_points = len(processed.data_points)
            expected_points = test_case['expected_points']
            
            success = actual_points >= expected_points  # Allow for more points than expected
            status = "✓ PASSED" if success else "✗ FAILED"
            
            print(f"   {status} - Expected: {expected_points}, Got: {actual_points}")
            print(f"   Numeric values: {len(processed.numeric_values)}")
            print(f"   Categories: {len(processed.categories)}")
            
            if processed.data_points:
                print(f"   Sample: {processed.data_points[0]}")
            
            results.append({
                'name': test_case['name'],
                'success': success,
                'expected': expected_points,
                'actual': actual_points
            })
            
        except Exception as e:
            print(f"   ✗ FAILED - Exception: {str(e)}")
            results.append({
                'name': test_case['name'],
                'success': False,
                'expected': test_case['expected_points'],
                'actual': 0,
                'error': str(e)
            })
    
    return results

def main():
    """Run comprehensive tests."""
    print("=" * 70)
    print("COMPREHENSIVE CHART DATA PROCESSING TEST")
    print("=" * 70)
    
    results = test_all_chart_types()
    
    print("\n" + "=" * 70)
    print("SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for r in results if r['success'])
    total = len(results)
    
    for result in results:
        status = "✓" if result['success'] else "✗"
        error_info = f" ({result.get('error', '')})" if 'error' in result else ""
        print(f"{status} {result['name']}: {result['actual']}/{result['expected']} points{error_info}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All chart types are now properly supported!")
        print("   The 'No data points found' issue should be resolved for all chart types.")
    else:
        print(f"\n⚠️  {total - passed} chart types still need attention.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

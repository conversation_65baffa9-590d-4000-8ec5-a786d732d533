#!/usr/bin/env python3
"""
Comprehensive test script to verify all chart recommendation fixes.
Tests for used_combinations validation, proper error handling, and unique chart generation.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.chart_recommender import ChartData

def test_comprehensive_chart_recommendations():
    """Test comprehensive chart recommendations with various edge cases."""
    
    print("Testing comprehensive chart recommendations...")
    
    # Test cases that might trigger the original 'used_combinations' error
    test_cases = [
        {
            "name": "Rich dataset (normal case)",
            "metadata": {
                "datetime_columns": ["date", "created_at"],
                "categorical_columns": ["category", "status", "type", "region", "priority"],
                "numerical_columns": ["amount", "quantity", "price", "score"],
                "unique_counts": {
                    "category": 5, "status": 3, "type": 4, "region": 6, "priority": 3,
                    "date": 100, "created_at": 80,
                    "amount": 200, "quantity": 150, "price": 180, "score": 90
                },
                "date_metadata": {"date_range_days": 365, "has_time": False}
            }
        },
        {
            "name": "Limited categorical data (edge case)",
            "metadata": {
                "datetime_columns": [],
                "categorical_columns": ["status"],
                "numerical_columns": ["amount"],
                "unique_counts": {"status": 2, "amount": 50},
                "date_metadata": {}
            }
        },
        {
            "name": "No valid combinations (extreme edge case)",
            "metadata": {
                "datetime_columns": [],
                "categorical_columns": ["single_value_col"],
                "numerical_columns": [],
                "unique_counts": {"single_value_col": 1},
                "date_metadata": {}
            }
        },
        {
            "name": "Multi-group scenario (potential error trigger)",
            "metadata": {
                "datetime_columns": [],
                "categorical_columns": ["cat1", "cat2", "cat3", "cat4"],
                "numerical_columns": ["num1", "num2"],
                "unique_counts": {
                    "cat1": 8, "cat2": 6, "cat3": 4, "cat4": 10,
                    "num1": 100, "num2": 80
                },
                "date_metadata": {}
            }
        },
        {
            "name": "Time series with stacking (potential error trigger)",
            "metadata": {
                "datetime_columns": ["date"],
                "categorical_columns": ["category", "status"],
                "numerical_columns": ["value"],
                "unique_counts": {
                    "date": 365, "category": 5, "status": 3, "value": 200
                },
                "date_metadata": {"date_range_days": 365, "has_time": False}
            }
        }
    ]
    
    chart_recommender = ChartData()
    
    for test_case in test_cases:
        test_name = test_case["name"]
        metadata = test_case["metadata"]
        
        print(f"\n--- Testing {test_name} ---")
        try:
            recommendations = chart_recommender.get_rule_based_recommendations(metadata)
            print(f"✓ {test_name}: Generated {len(recommendations)} recommendations successfully")
            
            # Validate unique combinations
            validate_unique_combinations(recommendations, test_name)
            
            # Check for proper error handling (no exceptions should be raised)
            if recommendations:
                print(f"  ✓ Generated chart types: {set(r.get('chart_type') for r in recommendations)}")
            else:
                print(f"  ✓ No recommendations generated (expected for edge cases)")
                
        except Exception as e:
            print(f"  ❌ {test_name}: ERROR - {str(e)}")
            import traceback
            print(f"  Traceback: {traceback.format_exc()}")
            return False
    
    return True

def validate_unique_combinations(recommendations, test_name):
    """Validate that chart recommendations have unique field combinations."""
    
    # Track combinations by chart type
    stacked_combinations = set()
    multi_group_combinations = set()
    basic_combinations = set()
    
    for rec in recommendations:
        chart_type = rec.get("chart_type")
        fields = rec.get("fields", {})
        
        if chart_type in ["stacked_line", "stacked_area", "stacked_bar"]:
            combination = (fields.get("x"), fields.get("group"), fields.get("numeric"))
            if combination in stacked_combinations:
                print(f"  ⚠️  Duplicate stacked combination in {test_name}: {combination}")
                return False
            stacked_combinations.add(combination)
            
        elif chart_type == "grouped_bar":
            combination = (fields.get("x"), fields.get("group"), fields.get("numeric"))
            if combination in multi_group_combinations:
                print(f"  ⚠️  Duplicate multi-group combination in {test_name}: {combination}")
                return False
            multi_group_combinations.add(combination)
            
        elif chart_type in ["pie", "doughnut", "bar"]:
            combination = (fields.get("x"), fields.get("numeric"))
            if combination in basic_combinations:
                print(f"  ⚠️  Duplicate basic combination in {test_name}: {combination}")
                return False
            basic_combinations.add(combination)
    
    if stacked_combinations:
        print(f"  ✓ Stacked charts: {len(stacked_combinations)} unique combinations")
    if multi_group_combinations:
        print(f"  ✓ Multi-group charts: {len(multi_group_combinations)} unique combinations")
    if basic_combinations:
        print(f"  ✓ Basic charts: {len(basic_combinations)} unique combinations")
    
    return True

def test_individual_error_handling():
    """Test that individual chart recommenders handle errors gracefully."""
    
    print("\n" + "="*60)
    print("Testing individual chart recommender error handling...")
    
    from app.services.chart_recommendations.multi_group_charts import MultiGroupChartRecommender
    from app.services.chart_recommendations.stacked_charts import StackedChartRecommender
    from app.services.chart_recommendations.basic_charts import BasicChartRecommender
    from app.services.chart_recommendations.multi_charts import MultiChartRecommender
    from app.services.chart_recommendations.numerical_charts import NumericalChartRecommender
    from app.services.chart_recommendations.box_plots import BoxPlotRecommender
    from app.services.chart_recommendations.heatmap_charts import HeatmapChartRecommender
    from app.services.chart_recommendations.hierarchical_charts import HierarchicalChartRecommender
    
    # Test with problematic metadata that might cause issues
    problematic_metadata = {
        "categorical_columns": ["cat1"],
        "numerical_columns": ["num1"],
        "unique_counts": {"cat1": 1, "num1": 50},  # cat1 has only 1 unique value
        "datetime_columns": [],
        "date_metadata": {}
    }
    
    recommenders = [
        ("MultiGroupChartRecommender", MultiGroupChartRecommender()),
        ("StackedChartRecommender", StackedChartRecommender()),
        ("BasicChartRecommender", BasicChartRecommender()),
        ("MultiChartRecommender", MultiChartRecommender()),
        ("NumericalChartRecommender", NumericalChartRecommender()),
        ("BoxPlotRecommender", BoxPlotRecommender()),
        ("HeatmapChartRecommender", HeatmapChartRecommender()),
        ("HierarchicalChartRecommender", HierarchicalChartRecommender())
    ]
    
    for name, recommender in recommenders:
        try:
            recommendations = recommender.get_recommendations(problematic_metadata)
            print(f"✓ {name}: Generated {len(recommendations)} recommendations (no errors)")
        except Exception as e:
            print(f"❌ {name}: ERROR - {str(e)}")
            return False
    
    print("✓ All individual recommenders handled edge cases correctly")
    return True

if __name__ == "__main__":
    print("🧪 Running comprehensive chart recommendation tests...")
    
    success1 = test_comprehensive_chart_recommendations()
    success2 = test_individual_error_handling()
    
    if success1 and success2:
        print("\n🎉 All comprehensive tests passed!")
        print("✅ Fixed 'used_combinations' reference errors")
        print("✅ Added proper error handling with detailed logging")
        print("✅ Ensured unique chart combinations")
        print("✅ All chart recommenders work correctly")
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)

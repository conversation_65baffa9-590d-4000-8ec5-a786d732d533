#!/usr/bin/env python3
"""
Test configuration loading
"""

from app.core.config import settings
from app.services.chart_insights.llm_client import LLMClientFactory

print("Configuration Test")
print("=" * 30)
print(f"DEFAULT_LLM_PROVIDER: {settings.DEFAULT_LLM_PROVIDER}")
print(f"USE_UNIFIED_INSIGHTS: {settings.USE_UNIFIED_INSIGHTS}")
print(f"OPENROUTER_API_KEY: {'***' + settings.OPENROUTER_API_KEY[-10:] if settings.OPENROUTER_API_KEY else 'None'}")
print(f"OPENROUTER_MODEL: {settings.OPENROUTER_MODEL}")

print("\nLLM Client Test")
print("=" * 30)
client = LLMClientFactory.create_client()
print(f"Client provider: {client.get_provider_name()}")

print("\nUnified Insights Service Test")
print("=" * 30)
from app.services.chart_insights.unified_insights_service import UnifiedInsightsService
unified_service = UnifiedInsightsService()
print(f"Unified service provider: {unified_service.get_llm_provider()}")

#!/usr/bin/env python3
"""
Test script to verify the data validation fix without requiring LLM API calls.
This tests that the data processing and validation logic works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.chart_insights.data_processor import ChartDataProcessor
from app.services.chart_insights.models import InsightGenerationContext
from app.core.exceptions import InsufficientDataError, InvalidChartDataError

def test_data_validation_fix():
    """Test that data validation no longer fails for box and multi-group bar charts."""
    print("Testing Data Validation Fix...")
    
    # Box chart data that was previously failing
    box_chart_data = {
        "chart_type": "box",
        "library": "plotly",
        "data": {
            "y": [10, 15, 12, 18, 20, 25, 22, 30, 28, 35],
            "type": "box"
        },
        "layout": {
            "title": "Test Box Chart",
            "yaxis": {"title": "Values"}
        }
    }
    
    # Multi-group bar chart data that was previously failing
    multi_group_data = {
        "chart_type": "grouped_bar",
        "library": "plotly",
        "data": [
            {
                "x": ["Q1", "Q2", "Q3", "Q4"],
                "y": [100, 120, 140, 160],
                "name": "2023",
                "type": "bar"
            },
            {
                "x": ["Q1", "Q2", "Q3", "Q4"],
                "y": [110, 130, 150, 170],
                "name": "2024",
                "type": "bar"
            }
        ],
        "layout": {
            "barmode": "group",
            "title": "Quarterly Sales",
            "xaxis": {"title": "Quarter"},
            "yaxis": {"title": "Sales"}
        }
    }
    
    processor = ChartDataProcessor()
    
    # Test box chart validation
    print("\n1. Testing box chart data validation...")
    try:
        processed_box = processor.process_chart_data(box_chart_data)
        context_box = InsightGenerationContext(
            processed_data=processed_box,
            chart_title="Test Box Chart",
            chart_type="box",
            llm_provider="openai"
        )
        
        # Check validation conditions that were previously failing
        if not context_box.processed_data.data_points:
            raise InsufficientDataError("No valid data points found for analysis", "box")
        
        if len(context_box.processed_data.data_points) < 2:
            raise InsufficientDataError("Insufficient data points for analysis", "box")
            
        if not context_box.processed_data.numeric_values:
            raise InvalidChartDataError("No valid numeric values found for analysis")
        
        print(f"   ✓ Box chart validation passed!")
        print(f"   - Data points: {len(context_box.processed_data.data_points)}")
        print(f"   - Numeric values: {len(context_box.processed_data.numeric_values)}")
        print(f"   - Categories: {len(context_box.processed_data.categories)}")
        box_success = True
        
    except (InsufficientDataError, InvalidChartDataError) as e:
        print(f"   ✗ Box chart validation failed: {str(e)}")
        box_success = False
    
    # Test multi-group bar chart validation
    print("\n2. Testing multi-group bar chart data validation...")
    try:
        processed_multi = processor.process_chart_data(multi_group_data)
        context_multi = InsightGenerationContext(
            processed_data=processed_multi,
            chart_title="Quarterly Sales",
            chart_type="grouped_bar",
            llm_provider="openai"
        )
        
        # Check validation conditions that were previously failing
        if not context_multi.processed_data.data_points:
            raise InsufficientDataError("No valid data points found for analysis", "grouped_bar")
        
        if len(context_multi.processed_data.data_points) < 2:
            raise InsufficientDataError("Insufficient data points for analysis", "grouped_bar")
            
        if not context_multi.processed_data.numeric_values:
            raise InvalidChartDataError("No valid numeric values found for analysis")
        
        print(f"   ✓ Multi-group bar chart validation passed!")
        print(f"   - Data points: {len(context_multi.processed_data.data_points)}")
        print(f"   - Numeric values: {len(context_multi.processed_data.numeric_values)}")
        print(f"   - Categories: {len(context_multi.processed_data.categories)}")
        multi_success = True
        
    except (InsufficientDataError, InvalidChartDataError) as e:
        print(f"   ✗ Multi-group bar chart validation failed: {str(e)}")
        multi_success = False
    
    return box_success and multi_success

def test_edge_cases():
    """Test edge cases to ensure robustness."""
    print("\nTesting Edge Cases...")
    
    processor = ChartDataProcessor()
    
    # Test empty data
    print("\n1. Testing empty data handling...")
    empty_data = {"chart_type": "box", "data": {"y": [], "type": "box"}}
    processed_empty = processor.process_chart_data(empty_data)
    print(f"   Empty data points: {len(processed_empty.data_points)} (expected: 0)")
    
    # Test single data point
    print("\n2. Testing single data point...")
    single_data = {"chart_type": "box", "data": {"y": [42], "type": "box"}}
    processed_single = processor.process_chart_data(single_data)
    print(f"   Single data points: {len(processed_single.data_points)} (expected: 1)")
    
    # Test malformed data
    print("\n3. Testing malformed data handling...")
    malformed_data = {"chart_type": "box", "data": "invalid"}
    processed_malformed = processor.process_chart_data(malformed_data)
    print(f"   Malformed data points: {len(processed_malformed.data_points)} (expected: 0)")
    
    return True

def main():
    """Run all tests."""
    print("=" * 60)
    print("TESTING DATA VALIDATION FIX")
    print("=" * 60)
    
    validation_passed = test_data_validation_fix()
    edge_cases_passed = test_edge_cases()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS")
    print("=" * 60)
    print(f"Data Validation Fix: {'✓ PASSED' if validation_passed else '✗ FAILED'}")
    print(f"Edge Cases: {'✓ PASSED' if edge_cases_passed else '✗ FAILED'}")
    
    overall_success = validation_passed and edge_cases_passed
    print(f"\nOverall: {'✓ ALL TESTS PASSED' if overall_success else '✗ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 The fix successfully resolves the 'No data points found' issue!")
        print("   Box charts and multi-group bar charts should now generate insights properly.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""
Test enhanced metadata identification and chart recommendation logic.

This test validates:
1. Enhanced metadata identification with more categorical columns
2. Unique chart recommendations without repetition
3. Allowed chart types field in responses
4. Datetime format handling including 'dd/MM/yyyy HH:mm'
"""

import pandas as pd
import pytest
from datetime import datetime, timedelta
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.data_processor import DataProcessor
from app.services.chart_recommender import ChartData as ChartRecommender, get_allowed_chart_types_for_chart_type
from app.services.chart_data import ChartData
from app.services.chart_recommendations.basic_charts import BasicChartRecommender


class TestEnhancedMetadataAndCharts:
    """Test class for enhanced metadata and chart recommendations."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.data_processor = DataProcessor()
        self.chart_recommender = ChartRecommender()
        self.chart_data_service = ChartData()
        self.basic_recommender = BasicChartRecommender()
    
    def create_test_dataframe(self):
        """Create a test DataFrame with various data types and formats."""
        # Create sample data with multiple categorical columns and datetime formats
        data = {
            'product_category': ['Electronics', 'Clothing', 'Books', 'Electronics', 'Clothing', 'Books'] * 10,
            'region': ['North', 'South', 'East', 'West', 'North', 'South'] * 10,
            'status': ['Active', 'Inactive', 'Pending', 'Active', 'Inactive', 'Pending'] * 10,
            'priority': ['High', 'Medium', 'Low', 'High', 'Medium', 'Low'] * 10,
            'department': ['Sales', 'Marketing', 'IT', 'HR', 'Sales', 'Marketing'] * 10,
            'sales_amount': [100.5, 200.3, 150.7, 300.2, 250.8, 180.4] * 10,
            'quantity': [1, 2, 3, 4, 5, 6] * 10,
            'created_date': ['17/06/2025 17:12', '18/06/2025 09:30', '19/06/2025 14:45'] * 20,
            'order_id': [f'ORD-{i:04d}' for i in range(60)],  # High unique ratio - should be ID
            'customer_name': [f'Customer {i}' for i in range(60)]  # High unique ratio - should be text/ID
        }
        
        return pd.DataFrame(data)
    
    def test_enhanced_metadata_identification(self):
        """Test that enhanced metadata identification captures more categorical columns."""
        df = self.create_test_dataframe()
        
        # Process the data
        processed_data = self.data_processor.process_data(df)
        metadata = processed_data['metadata']
        
        # Validate enhanced metadata structure
        assert 'column_characteristics' in metadata
        assert 'categorical_combinations' in metadata
        assert 'data_quality' in metadata
        
        # Check that we have more categorical columns than before (should be > 3)
        categorical_columns = metadata['categorical_columns']
        assert len(categorical_columns) >= 5, f"Expected at least 5 categorical columns, got {len(categorical_columns)}"
        
        # Verify specific columns are identified as categorical
        expected_categorical = ['product_category', 'region', 'status', 'priority', 'department']
        for col in expected_categorical:
            assert col in categorical_columns, f"Column {col} should be identified as categorical"
        
        # Check column characteristics
        for col in categorical_columns:
            assert col in metadata['column_characteristics']
            char = metadata['column_characteristics'][col]
            assert 'type' in char
            assert 'unique_count' in char
            assert 'unique_ratio' in char
            
        print(f"✓ Enhanced metadata identification: {len(categorical_columns)} categorical columns identified")
    
    def test_datetime_format_handling(self):
        """Test that 'dd/MM/yyyy HH:mm' format is properly handled."""
        df = self.create_test_dataframe()
        
        # Process the data
        processed_data = self.data_processor.process_data(df)
        processed_df = processed_data['df']
        metadata = processed_data['metadata']
        
        # Check if created_date was converted to datetime
        datetime_columns = metadata['datetime_columns']
        
        # The created_date should be identified as datetime
        assert len(datetime_columns) > 0, "Should have at least one datetime column"
        
        # Check if any datetime column was properly converted
        for col in datetime_columns:
            if 'created_date' in col:
                assert pd.api.types.is_datetime64_any_dtype(processed_df[col]), f"Column {col} should be datetime type"
                print(f"✓ Datetime format handling: {col} properly converted")
                break
    
    def test_unique_chart_recommendations(self):
        """Test that chart recommendations use unique column combinations."""
        df = self.create_test_dataframe()
        
        # Process the data
        processed_data = self.data_processor.process_data(df)
        metadata = processed_data['metadata']
        
        # Get basic chart recommendations
        basic_recommendations = self.basic_recommender.get_recommendations(metadata)
        
        # Check that we have multiple recommendations
        assert len(basic_recommendations) > 1, "Should have multiple chart recommendations"
        
        # Check that each recommendation uses a different column
        used_columns = set()
        for rec in basic_recommendations:
            x_field = rec['fields']['x']
            assert x_field not in used_columns, f"Column {x_field} is used multiple times"
            used_columns.add(x_field)
        
        print(f"✓ Unique chart recommendations: {len(basic_recommendations)} charts with unique columns")
        
        # Log the recommendations for verification
        for i, rec in enumerate(basic_recommendations):
            print(f"  Chart {i+1}: {rec['chart_type']} using column '{rec['fields']['x']}'")
    
    def test_allowed_chart_types_field(self):
        """Test that allowed_chart_types field is included in chart responses."""
        df = self.create_test_dataframe()
        
        # Process the data
        processed_data = self.data_processor.process_data(df)
        
        # Get chart recommendations
        recommended_charts = self.chart_recommender.get_rule_based_recommendations(processed_data['metadata'])
        
        # Process chart recommendations to get final chart data
        chart_data = self.chart_data_service.process_chart_recommendations(
            df=processed_data['df'],
            recommended_charts=recommended_charts
        )
        
        # Validate that each chart has allowed_chart_types field
        assert len(chart_data) > 0, "Should have chart data"
        
        for chart in chart_data:
            assert 'allowed_chart_types' in chart, "Chart should have allowed_chart_types field"
            assert isinstance(chart['allowed_chart_types'], str), "allowed_chart_types should be a string"
            assert len(chart['allowed_chart_types']) > 0, "allowed_chart_types should not be empty"
            
            # Validate format (comma-separated)
            chart_types = chart['allowed_chart_types'].split(',')
            assert len(chart_types) > 0, "Should have at least one allowed chart type"
            
        print(f"✓ Allowed chart types field: Present in all {len(chart_data)} charts")
        
        # Log some examples
        for i, chart in enumerate(chart_data[:3]):
            print(f"  Chart {i+1} ({chart.get('chart_type', 'unknown')}): {chart['allowed_chart_types']}")
    
    def test_chart_group_functionality(self):
        """Test that chart group functionality works correctly."""
        # Test the helper function
        basic_types = get_allowed_chart_types_for_chart_type('pie')
        assert 'pie' in basic_types
        assert 'doughnut' in basic_types
        assert 'bar' in basic_types
        
        numerical_types = get_allowed_chart_types_for_chart_type('histogram')
        assert 'histogram' in numerical_types
        assert 'scatter' in numerical_types
        
        print("✓ Chart group functionality: Helper functions work correctly")
    
    def test_categorical_combinations(self):
        """Test that categorical combinations are properly generated."""
        df = self.create_test_dataframe()
        
        # Process the data
        processed_data = self.data_processor.process_data(df)
        metadata = processed_data['metadata']
        
        # Check categorical combinations
        combinations = metadata.get('categorical_combinations', [])
        
        if len(metadata['categorical_columns']) >= 2:
            assert len(combinations) > 0, "Should have categorical combinations when multiple categorical columns exist"
            
            for combo in combinations:
                assert 'col1' in combo
                assert 'col2' in combo
                assert 'combined_cardinality' in combo
                assert 'suitable_for' in combo
                
        print(f"✓ Categorical combinations: {len(combinations)} combinations generated")


if __name__ == "__main__":
    # Run the tests
    test_instance = TestEnhancedMetadataAndCharts()
    test_instance.setup_method()
    
    print("Running Enhanced Metadata and Chart Recommendations Tests...")
    print("=" * 60)
    
    try:
        test_instance.test_enhanced_metadata_identification()
        test_instance.test_datetime_format_handling()
        test_instance.test_unique_chart_recommendations()
        test_instance.test_allowed_chart_types_field()
        test_instance.test_chart_group_functionality()
        test_instance.test_categorical_combinations()
        
        print("=" * 60)
        print("✅ All tests passed! Enhanced metadata and chart recommendations are working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

#!/usr/bin/env python3
"""
Test Google Sheets connection and diagnose issues
"""

import os
import json
import gspread
from google.oauth2.service_account import Credentials
from app.core.config import settings
from app.services.analytics.google_sheets_service import GoogleSheetsService

def test_credentials_file():
    """Test if credentials file exists and is valid"""
    print("Testing Credentials File")
    print("=" * 40)
    
    creds_file = settings.GOOGLE_SHEETS_CREDENTIALS_FILE
    print(f"Credentials file path: {creds_file}")
    
    if not creds_file:
        print("❌ GOOGLE_SHEETS_CREDENTIALS_FILE not configured")
        return False
    
    if not os.path.exists(creds_file):
        print(f"❌ Credentials file not found: {creds_file}")
        return False
    
    try:
        with open(creds_file, 'r') as f:
            creds_data = json.load(f)
        
        print("✅ Credentials file exists and is valid JSON")
        print(f"   Service account email: {creds_data.get('client_email', 'Not found')}")
        print(f"   Project ID: {creds_data.get('project_id', 'Not found')}")
        return True
        
    except json.JSONDecodeError:
        print("❌ Credentials file is not valid JSON")
        return False
    except Exception as e:
        print(f"❌ Error reading credentials file: {str(e)}")
        return False

def test_google_sheets_auth():
    """Test Google Sheets authentication"""
    print("\nTesting Google Sheets Authentication")
    print("=" * 40)
    
    try:
        scope = [
            'https://spreadsheets.google.com/feeds',
            'https://www.googleapis.com/auth/drive'
        ]
        
        credentials = Credentials.from_service_account_file(
            settings.GOOGLE_SHEETS_CREDENTIALS_FILE,
            scopes=scope
        )
        
        client = gspread.authorize(credentials)
        print("✅ Google Sheets authentication successful")
        return client
        
    except Exception as e:
        print(f"❌ Google Sheets authentication failed: {str(e)}")
        return None

def test_sheet_access(client, sheet_id, sheet_name):
    """Test access to a specific sheet"""
    print(f"\nTesting {sheet_name} Sheet Access")
    print("=" * 40)
    
    if not sheet_id:
        print(f"❌ {sheet_name} sheet ID not configured")
        return False
    
    print(f"Sheet ID: {sheet_id}")
    
    try:
        # Try to open the sheet
        spreadsheet = client.open_by_key(sheet_id)
        print(f"✅ Successfully opened {sheet_name} spreadsheet")
        print(f"   Title: {spreadsheet.title}")
        
        # Try to access the first worksheet
        worksheet = spreadsheet.sheet1
        print(f"✅ Successfully accessed first worksheet")
        print(f"   Worksheet title: {worksheet.title}")
        print(f"   Rows: {worksheet.row_count}")
        print(f"   Columns: {worksheet.col_count}")
        
        # Try to read the first row (headers)
        headers = worksheet.row_values(1)
        print(f"✅ Successfully read headers: {headers}")
        
        # Try to write a test value (and then remove it)
        try:
            test_cell = worksheet.cell(1, 1)
            print(f"✅ Successfully read cell A1: '{test_cell.value}'")
            
            # Test write permission by updating a cell temporarily
            original_value = test_cell.value
            worksheet.update_cell(1, 1, original_value)  # Write the same value back
            print("✅ Write permission confirmed")
            
        except Exception as e:
            print(f"⚠️  Write permission test failed: {str(e)}")
        
        return True
        
    except gspread.exceptions.SpreadsheetNotFound:
        print(f"❌ {sheet_name} spreadsheet not found (ID: {sheet_id})")
        print("   Check if the sheet ID is correct and the service account has access")
        return False
    except gspread.exceptions.APIError as e:
        print(f"❌ API error accessing {sheet_name} sheet: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Error accessing {sheet_name} sheet: {str(e)}")
        return False

def test_google_sheets_service():
    """Test the GoogleSheetsService class"""
    print("\nTesting GoogleSheetsService Class")
    print("=" * 40)
    
    try:
        service = GoogleSheetsService()
        
        print(f"Service available: {service.is_available()}")
        print(f"Client initialized: {service.client is not None}")
        print(f"User profile sheet: {service.user_profile_sheet is not None}")
        print(f"Events sheet: {service.events_sheet is not None}")
        
        if service.events_sheet is None:
            print("❌ Events sheet not initialized - this is the source of the error")
        else:
            print("✅ Events sheet initialized successfully")
        
        return service
        
    except Exception as e:
        print(f"❌ Error initializing GoogleSheetsService: {str(e)}")
        return None

def main():
    """Main diagnostic function"""
    print("Google Sheets Connection Diagnostic")
    print("=" * 50)
    
    # Test 1: Credentials file
    if not test_credentials_file():
        return
    
    # Test 2: Authentication
    client = test_google_sheets_auth()
    if not client:
        return
    
    # Test 3: User Profile Sheet Access
    test_sheet_access(
        client, 
        settings.GOOGLE_SHEETS_USER_PROFILE_SHEET_ID, 
        "User Profile"
    )
    
    # Test 4: Events Sheet Access
    test_sheet_access(
        client, 
        settings.GOOGLE_SHEETS_EVENTS_SHEET_ID, 
        "Events"
    )
    
    # Test 5: GoogleSheetsService
    service = test_google_sheets_service()
    
    print("\n" + "=" * 50)
    print("DIAGNOSTIC SUMMARY")
    print("=" * 50)
    
    if service and service.events_sheet:
        print("✅ All tests passed - Google Sheets should be working")
    else:
        print("❌ Issues found - check the errors above")
        print("\nCommon solutions:")
        print("1. Verify the service account email has Editor access to both sheets")
        print("2. Check that the sheet IDs are correct")
        print("3. Ensure the credentials file is valid and not expired")
        print("4. Verify the sheets exist and are accessible")

if __name__ == "__main__":
    main()

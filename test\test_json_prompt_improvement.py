#!/usr/bin/env python3
"""
Test the improved JSON prompt and parsing
"""

from app.services.chart_insights.unified_insights_service import UnifiedInsightsService

def test_prompt_generation():
    """Test that the new prompt is more focused on JSON."""
    print("Testing Improved JSON Prompt")
    print("=" * 40)
    
    service = UnifiedInsightsService()
    
    # Create test context
    from app.services.chart_insights.data_processor import ChartDataProcessor
    from app.services.chart_insights.models import InsightGenerationContext
    
    test_data = {
        "type": "bar",
        "title": "Test Chart",
        "x": ["A", "B", "C"],
        "y": [10, 20, 30]
    }
    
    processor = ChartDataProcessor()
    processed_data = processor.process_chart_data(test_data)
    
    context = InsightGenerationContext(
        processed_data=processed_data,
        chart_title="Test Chart",
        chart_type="bar",
        llm_provider="openrouter"
    )
    
    # Generate the prompt
    prompt = service._create_unified_prompt(context)
    
    print("Generated prompt preview:")
    print("-" * 40)
    print(prompt[:500] + "...")
    print("-" * 40)
    
    # Check if prompt contains key JSON enforcement elements
    json_indicators = [
        "ONLY valid JSON",
        "JSON ONLY",
        "START WITH {",
        "END WITH }",
        "example",
        "executive_summary",
        "data_insights",
        "hidden_patterns",
        "recommendations"
    ]
    
    found_indicators = []
    for indicator in json_indicators:
        if indicator.lower() in prompt.lower():
            found_indicators.append(indicator)
    
    print(f"\nJSON enforcement indicators found: {len(found_indicators)}/{len(json_indicators)}")
    for indicator in found_indicators:
        print(f"  ✅ {indicator}")
    
    missing = [ind for ind in json_indicators if ind not in found_indicators]
    for indicator in missing:
        print(f"  ❌ {indicator}")
    
    return len(found_indicators) >= 6  # At least 6 out of 9 indicators

def test_parsing_methods():
    """Test the parsing methods work correctly."""
    print("\nTesting Parsing Methods")
    print("=" * 40)
    
    service = UnifiedInsightsService()
    
    # Test 1: JSON extraction
    mixed_text = '''
    Here's the analysis:
    {"executive_summary":["Test 1","Test 2"],"data_insights":["Insight 1","Insight 2"],"hidden_patterns":["Pattern 1","Pattern 2"],"recommendations":["Rec 1","Rec 2"]}
    Hope this helps!
    '''
    
    extracted = service._extract_json_from_text(mixed_text)
    json_extraction_works = extracted is not None
    print(f"JSON extraction: {'✅ PASS' if json_extraction_works else '❌ FAIL'}")
    
    # Test 2: Natural language parsing
    natural_text = "Sales increased by 25%. Customer satisfaction improved. Market share grew. We should focus on digital channels. Mobile optimization is needed. Expand to new markets."
    
    nl_result = service._parse_natural_language_response(natural_text)
    nl_parsing_works = (nl_result is not None and 
                       len(nl_result.get('executive_summary', [])) > 0 and
                       len(nl_result.get('data_insights', [])) > 0)
    print(f"Natural language parsing: {'✅ PASS' if nl_parsing_works else '❌ FAIL'}")
    
    # Test 3: Fallback flag tracking
    service._used_fallback_parsing = False
    test_content = ["This is not JSON content at all"]
    _ = service._parse_unified_response(test_content)  # We only care about the side effect
    fallback_tracking_works = service._used_fallback_parsing
    print(f"Fallback tracking: {'✅ PASS' if fallback_tracking_works else '❌ FAIL'}")
    
    return json_extraction_works and nl_parsing_works and fallback_tracking_works

def main():
    """Main test function."""
    print("JSON Prompt Improvement Test")
    print("=" * 50)
    
    # Test prompt improvements
    prompt_test = test_prompt_generation()
    
    # Test parsing improvements
    parsing_test = test_parsing_methods()
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    print(f"Prompt improvements: {'✅ PASS' if prompt_test else '❌ FAIL'}")
    print(f"Parsing improvements: {'✅ PASS' if parsing_test else '❌ FAIL'}")
    
    if prompt_test and parsing_test:
        print("\n🎉 All improvements are working!")
        print("The unified insights service should now:")
        print("  - Generate more JSON-focused prompts")
        print("  - Handle various response formats gracefully")
        print("  - Track when fallback parsing is used")
        print("  - Provide better error logging")
    else:
        print("\n⚠️  Some improvements need attention")
    
    return prompt_test and parsing_test

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

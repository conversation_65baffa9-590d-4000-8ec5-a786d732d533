#!/usr/bin/env python3
"""
Test script for new chart types implementation
"""

import pandas as pd
from app.services.data_aggregator.base import DataAggregator

def create_test_data():
    """Create test data for chart types."""
    return pd.DataFrame({
        'category': ['A', 'B', 'C', 'D', 'E', 'F'],
        'value1': [10, 25, 30, 45, 20, 35],
        'value2': [15, 20, 35, 40, 25, 30],
        'value3': [5, 15, 25, 35, 45, 55],
        'size': [100, 200, 150, 300, 250, 180]
    })

def test_chart_type(aggregator, df, chart_type, fields):
    """Test a specific chart type."""
    print(f"\nTesting {chart_type}:")
    print("-" * 40)
    
    try:
        result = aggregator.prepare_chart_data(df, chart_type, fields)
        if result:
            print(f"✅ {chart_type} - Success")
            print(f"   Chart type: {result.get('chart_type')}")
            print(f"   Library: {result.get('library')}")
            print(f"   Data keys: {list(result.get('data', {}).keys())}")
            return True
        else:
            print(f"❌ {chart_type} - Failed (returned None)")
            return False
    except Exception as e:
        print(f"❌ {chart_type} - Error: {str(e)}")
        return False

def main():
    """Test all new chart types."""
    print("Testing New Chart Types Implementation")
    print("=" * 60)
    
    # Create test data
    df = create_test_data()
    aggregator = DataAggregator()
    
    # Test cases for each new chart type
    test_cases = [
        {
            "chart_type": "horizontal_bar",
            "fields": {
                "x": "category",
                "x_type": "categorical",
                "numeric": "value1",
                "chart_title": "Horizontal Bar Chart Test"
            }
        },
        {
            "chart_type": "column",
            "fields": {
                "x": "category",
                "x_type": "categorical", 
                "numeric": "value1",
                "chart_title": "Column Chart Test"
            }
        },
        {
            "chart_type": "polar_area",
            "fields": {
                "x": "category",
                "x_type": "categorical",
                "numeric": "value1",
                "chart_title": "Polar Area Chart Test"
            }
        },
        {
            "chart_type": "funnel",
            "fields": {
                "x": "category",
                "x_type": "categorical",
                "numeric": "value1",
                "chart_title": "Funnel Chart Test"
            }
        },
        {
            "chart_type": "scatter_matrix",
            "fields": {
                "chart_title": "Scatter Matrix Test"
            }
        }
    ]
    
    # Test each chart type
    results = []
    for test_case in test_cases:
        success = test_chart_type(
            aggregator, 
            df, 
            test_case["chart_type"], 
            test_case["fields"]
        )
        results.append((test_case["chart_type"], success))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    for chart_type, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status} - {chart_type}")
    
    if passed == total:
        print("\n🎉 All new chart types implemented successfully!")
    else:
        print(f"\n⚠️  {total - passed} chart type(s) need attention.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

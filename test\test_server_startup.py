#!/usr/bin/env python3
"""
Test script to verify the server can start and the common headers system works.
"""

import sys
import os
import requests
import time
import subprocess
import signal
from threading import Thread

def test_server_startup():
    """Test that the server can start successfully."""
    print("Testing server startup...")
    
    try:
        # Import the app to check for import errors
        from app.main import app
        print("   ✓ App imports successfully")
        
        # Check that all routers are included
        routes = [route.path for route in app.routes]
        expected_routes = [
            "/v1/example/public",
            "/v1/example/authenticated", 
            "/v1/example/headers-summary"
        ]
        
        for route in expected_routes:
            if any(route in r for r in routes):
                print(f"   ✓ Route {route} is registered")
            else:
                print(f"   ✗ Route {route} is missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ✗ Server startup test failed: {e}")
        return False

def test_middleware_import():
    """Test that middleware can be imported and used."""
    print("\nTesting middleware...")
    
    try:
        from app.core.middleware import CommonHeadersMiddleware, SecurityHeadersMiddleware
        print("   ✓ Middleware classes imported successfully")
        
        # Test that middleware can be instantiated
        from fastapi import FastAPI
        test_app = FastAPI()
        
        # This should not raise an error
        test_app.add_middleware(SecurityHeadersMiddleware)
        test_app.add_middleware(CommonHeadersMiddleware)
        print("   ✓ Middleware can be added to FastAPI app")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Middleware test failed: {e}")
        return False

def test_dependencies():
    """Test that dependencies work correctly."""
    print("\nTesting dependencies...")
    
    try:
        from app.core.auth_dependencies import RequireAuth, OptionalAuth, RequireAdmin, AnalyticsAuth
        from app.core.headers import CommonHeaders
        print("   ✓ All dependencies imported successfully")
        
        # Test that dependencies are callable
        assert callable(RequireAuth.dependency)
        assert callable(OptionalAuth.dependency)
        assert callable(CommonHeaders.dependency)
        print("   ✓ Dependencies are properly configured")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Dependencies test failed: {e}")
        return False

def test_context_system():
    """Test the context system."""
    print("\nTesting context system...")
    
    try:
        from app.core.context import RequestContext, set_request_context, get_request_context
        
        # Create test context
        test_context = RequestContext(
            request_id="test-123",
            session_id="session-456",
            user_agent="Test Agent",
            ip_address="127.0.0.1"
        )
        
        # Set and get context
        set_request_context(test_context)
        retrieved = get_request_context()
        
        assert retrieved.request_id == "test-123"
        assert retrieved.session_id == "session-456"
        print("   ✓ Context system works correctly")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Context system test failed: {e}")
        return False

def test_header_utilities():
    """Test header utility functions."""
    print("\nTesting header utilities...")
    
    try:
        from app.core.headers import sanitize_header_value, extract_language, generate_request_id
        
        # Test sanitization
        result = sanitize_header_value("test\x00value")
        assert result == "testvalue"
        print("   ✓ Header sanitization works")
        
        # Test language extraction
        result = extract_language("en-US,en;q=0.9")
        assert result == "en"
        print("   ✓ Language extraction works")
        
        # Test request ID generation
        request_id = generate_request_id()
        assert len(request_id) > 0
        assert "-" in request_id
        print("   ✓ Request ID generation works")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Header utilities test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("TESTING SERVER STARTUP AND COMMON HEADERS SYSTEM")
    print("=" * 60)
    
    tests = [
        ("Server Startup", test_server_startup),
        ("Middleware", test_middleware_import),
        ("Dependencies", test_dependencies),
        ("Context System", test_context_system),
        ("Header Utilities", test_header_utilities)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The server should start successfully.")
        print("\nTo start the server, run:")
        print("   uvicorn app.main:app --reload")
        print("\nThen test the example endpoints:")
        print("   GET http://localhost:8000/v1/example/public")
        print("   GET http://localhost:8000/v1/example/headers-summary")
        print("   GET http://localhost:8000/docs  # For interactive API docs")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

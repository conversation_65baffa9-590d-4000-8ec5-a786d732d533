# PowerShell script to test the Chart Insights API
# Run this with: powershell -ExecutionPolicy Bypass -File test_simple_curl.ps1

$uri = "http://127.0.0.1:8000/v1/chart/insights"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

$body = @{
    chart_data = @{
        type = "bar"
        title = "Monthly Sales Performance"
        x = @("January", "February", "March", "April", "May", "June")
        y = @(12000, 15000, 18000, 14000, 22000, 25000)
        layout = @{
            title = "Monthly Sales Performance Q1-Q2 2024"
            xaxis = @{ title = "Month" }
            yaxis = @{ title = "Sales ($)" }
        }
    }
    chart_type = "bar"
    chart_title = "Monthly Sales Performance Q1-Q2 2024"
    include_executive_summary = $true
    include_data_insights = $true
    include_hidden_patterns = $true
    include_recommendations = $true
} | ConvertTo-Json -Depth 10

Write-Host "Testing Chart Insights API..." -ForegroundColor Green
Write-Host "URL: $uri" -ForegroundColor Yellow

try {
    $response = Invoke-RestMethod -Uri $uri -Method Post -Headers $headers -Body $body -TimeoutSec 60
    
    Write-Host "`n✅ SUCCESS! API Response:" -ForegroundColor Green
    Write-Host "=" * 50 -ForegroundColor Gray
    
    if ($response.executive_summary) {
        Write-Host "`n📊 EXECUTIVE SUMMARY:" -ForegroundColor Cyan
        $response.executive_summary | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    }
    
    if ($response.data_insights) {
        Write-Host "`n🔍 DATA INSIGHTS:" -ForegroundColor Cyan
        $response.data_insights | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    }
    
    if ($response.hidden_patterns) {
        Write-Host "`n🕵️ HIDDEN PATTERNS:" -ForegroundColor Cyan
        $response.hidden_patterns | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    }
    
    if ($response.recommendations) {
        Write-Host "`n💡 RECOMMENDATIONS:" -ForegroundColor Cyan
        $response.recommendations | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    }
    
    if ($response.key_metrics) {
        Write-Host "`n📈 KEY METRICS:" -ForegroundColor Cyan
        $response.key_metrics.PSObject.Properties | ForEach-Object {
            Write-Host "  • $($_.Name): $($_.Value)" -ForegroundColor White
        }
    }
    
} catch {
    Write-Host "`n❌ ERROR:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`nTest completed!" -ForegroundColor Green

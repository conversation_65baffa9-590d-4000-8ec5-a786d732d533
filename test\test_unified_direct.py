#!/usr/bin/env python3
"""
Direct test of unified insights service without API
"""

import asyncio
from app.services.chart_insights.unified_insights_service import UnifiedInsightsService
from app.services.chart_insights.data_processor import ChartDataProcessor
from app.services.chart_insights.models import InsightGenerationContext

async def test_unified_service_directly():
    """Test the unified insights service directly."""
    print("Direct Unified Insights Service Test")
    print("=" * 50)
    
    # Create test data
    test_chart_data = {
        "type": "bar",
        "title": "Simple Sales Test",
        "x": ["Jan", "Feb", "Mar"],
        "y": [100, 150, 200],
        "layout": {
            "title": "Simple Sales Test",
            "xaxis": {"title": "Month"},
            "yaxis": {"title": "Sales"}
        }
    }
    
    try:
        # Initialize services
        unified_service = UnifiedInsightsService()
        data_processor = ChartDataProcessor()
        
        print(f"Unified service provider: {unified_service.get_llm_provider()}")
        
        # Process data
        processed_data = data_processor.process_chart_data(test_chart_data)
        print(f"Processed data points: {len(processed_data.data_points)}")
        
        # Create context
        context = InsightGenerationContext(
            processed_data=processed_data,
            chart_title="Simple Sales Test",
            chart_type="bar",
            llm_provider="openrouter"
        )
        
        # Generate unified insights
        print("\nGenerating unified insights...")
        insights = await unified_service.generate_unified_insights(context)
        
        print("\nResults:")
        print("-" * 30)
        
        for insight_type, insight_list in insights.items():
            print(f"\n{insight_type.upper().replace('_', ' ')}:")
            for i, insight in enumerate(insight_list, 1):
                print(f"  {i}. {insight}")
        
        return True
        
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_unified_service_directly())
    if success:
        print("\n🎉 Direct unified insights test passed!")
    else:
        print("\n⚠️ Direct unified insights test failed!")

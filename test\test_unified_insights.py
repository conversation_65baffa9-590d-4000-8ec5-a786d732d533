#!/usr/bin/env python3
"""
Simple test script for the unified insights functionality
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://127.0.0.1:8000"
API_ENDPOINT = f"{BASE_URL}/v1/chart/insights"

def create_simple_test_data():
    """Create simple test data for unified insights testing."""
    return {
        "chart_data": {
            "type": "bar",
            "title": "Simple Sales Test",
            "x": ["Jan", "Feb", "Mar"],
            "y": [100, 150, 200],
            "layout": {
                "title": "Simple Sales Test",
                "xaxis": {"title": "Month"},
                "yaxis": {"title": "Sales"}
            }
        },
        "chart_type": "bar",
        "chart_title": "Simple Sales Test",
        "include_executive_summary": True,
        "include_data_insights": True,
        "include_hidden_patterns": True,
        "include_recommendations": True
    }

def test_unified_insights():
    """Test the unified insights functionality."""
    try:
        print("Testing Unified Insights Functionality")
        print("=" * 50)
        
        # Check if server is running
        try:
            health_response = requests.get(f"{BASE_URL}/v1/health", timeout=5)
            if health_response.status_code != 200:
                print(f"❌ Server health check failed. Status: {health_response.status_code}")
                return False
            print("✅ Server is running and healthy")
        except requests.exceptions.RequestException:
            print("❌ Cannot connect to server. Make sure the FastAPI server is running.")
            return False
        
        # Prepare test data
        test_data = create_simple_test_data()
        
        # Make the API request
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        print(f"\nSending request to: {API_ENDPOINT}")
        print("Request payload:")
        print(json.dumps(test_data, indent=2))
        
        response = requests.post(
            API_ENDPOINT,
            json=test_data,
            headers=headers,
            timeout=30
        )
        
        print(f"\nResponse Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("\n✅ SUCCESS - API Response:")
            print("-" * 40)
            
            # Check insights mode
            insights_mode = result.get("chart_metadata", {}).get("insights_mode", "unknown")
            print(f"\n🔧 INSIGHTS MODE: {insights_mode}")
            
            # Display executive summary
            if result.get("executive_summary"):
                print("\n📊 EXECUTIVE SUMMARY:")
                for i, point in enumerate(result["executive_summary"], 1):
                    print(f"  {i}. {point}")
            
            # Display data insights
            if result.get("data_insights"):
                print("\n🔍 DATA INSIGHTS:")
                for i, insight in enumerate(result["data_insights"], 1):
                    print(f"  {i}. {insight}")
            
            # Display hidden patterns
            if result.get("hidden_patterns"):
                print("\n🕵️ HIDDEN PATTERNS:")
                for i, pattern in enumerate(result["hidden_patterns"], 1):
                    print(f"  {i}. {pattern}")
            
            # Display recommendations
            if result.get("recommendations"):
                print("\n💡 RECOMMENDATIONS:")
                for i, rec in enumerate(result["recommendations"], 1):
                    print(f"  {i}. {rec}")
            
            # Display metadata
            if result.get("chart_metadata"):
                print("\n📋 METADATA:")
                for key, value in result["chart_metadata"].items():
                    print(f"  • {key}: {value}")
            
            return True
            
        else:
            print(f"\n❌ FAILED - Status Code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"\n❌ REQUEST ERROR: {str(e)}")
        return False
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_unified_insights()
    if success:
        print("\n🎉 Unified insights test passed!")
    else:
        print("\n⚠️ Unified insights test failed!")
    sys.exit(0 if success else 1)

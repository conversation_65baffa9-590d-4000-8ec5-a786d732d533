#!/usr/bin/env python3
"""
Test script for unified insights parsing improvements
"""

import json
from app.services.chart_insights.unified_insights_service import UnifiedInsightsService

def test_json_parsing():
    """Test JSON parsing with various response formats."""
    print("Testing JSON Parsing")
    print("=" * 40)
    
    service = UnifiedInsightsService()
    
    # Test case 1: Perfect JSON response
    perfect_json = '''
    {
        "executive_summary": [
            "Sales increased by 25% compared to last quarter",
            "Market share expanded in key demographics"
        ],
        "data_insights": [
            "Average transaction value rose to $150",
            "Customer retention rate improved to 85%"
        ],
        "hidden_patterns": [
            "Peak sales occur on weekends",
            "Mobile users convert 30% more than desktop"
        ],
        "recommendations": [
            "Increase weekend marketing campaigns",
            "Optimize mobile checkout experience"
        ]
    }
    '''
    
    print("Test 1: Perfect JSON")
    result1 = service._parse_unified_response([perfect_json])
    print(f"✅ Success: {len(result1)} categories parsed")
    
    # Test case 2: JSON with markdown code blocks
    markdown_json = '''
    ```json
    {
        "executive_summary": ["Summary point 1", "Summary point 2"],
        "data_insights": ["Insight 1", "Insight 2"],
        "hidden_patterns": ["Pattern 1", "Pattern 2"],
        "recommendations": ["Recommendation 1", "Recommendation 2"]
    }
    ```
    '''
    
    print("\nTest 2: JSON with markdown")
    result2 = service._parse_unified_response([markdown_json])
    print(f"✅ Success: {len(result2)} categories parsed")
    
    # Test case 3: Mixed content with JSON
    mixed_content = '''
    Here's my analysis of the data:
    
    {
        "executive_summary": ["Mixed content summary 1", "Mixed content summary 2"],
        "data_insights": ["Mixed insight 1", "Mixed insight 2"],
        "hidden_patterns": ["Mixed pattern 1", "Mixed pattern 2"],
        "recommendations": ["Mixed recommendation 1", "Mixed recommendation 2"]
    }
    
    I hope this helps with your analysis.
    '''
    
    print("\nTest 3: Mixed content with JSON")
    result3 = service._parse_unified_response([mixed_content])
    print(f"✅ Success: {len(result3)} categories parsed")
    
    # Test case 4: Pure natural language (should trigger fallback)
    natural_language = '''
    The mean value of the distribution is approximately 250 units, with a median of 248.5 units, indicating a slightly right-skewed distribution. The data shows strong performance in the first quarter. Sales trends indicate growth potential. Customer satisfaction remains high. We recommend focusing on digital channels. Mobile optimization should be prioritized. Market expansion looks promising. Investment in technology will yield returns.
    '''
    
    print("\nTest 4: Natural language (fallback)")
    result4 = service._parse_unified_response([natural_language])
    print(f"✅ Success: {len(result4)} categories parsed")
    print(f"Executive summary: {result4['executive_summary'][0][:100]}...")
    
    return True

def test_json_extraction():
    """Test JSON extraction from mixed content."""
    print("\nTesting JSON Extraction")
    print("=" * 40)
    
    service = UnifiedInsightsService()
    
    # Test mixed content
    mixed_text = '''
    Based on my analysis, here are the insights:
    
    {
        "executive_summary": ["Test summary"],
        "data_insights": ["Test insight"],
        "hidden_patterns": ["Test pattern"],
        "recommendations": ["Test recommendation"]
    }
    
    This completes the analysis.
    '''
    
    extracted = service._extract_json_from_text(mixed_text)
    if extracted:
        print("✅ Successfully extracted JSON from mixed content")
        print(f"Extracted: {extracted[:100]}...")
        
        # Verify it's valid JSON
        try:
            parsed = json.loads(extracted)
            print(f"✅ Extracted JSON is valid with {len(parsed)} keys")
        except json.JSONDecodeError:
            print("❌ Extracted content is not valid JSON")
    else:
        print("❌ Failed to extract JSON from mixed content")
    
    return extracted is not None

def test_natural_language_parsing():
    """Test natural language parsing fallback."""
    print("\nTesting Natural Language Parsing")
    print("=" * 40)
    
    service = UnifiedInsightsService()
    
    # Test with structured natural language
    structured_text = '''
    The analysis reveals strong performance indicators. Sales have increased significantly over the past quarter. 
    Customer engagement metrics show positive trends. Market penetration has improved in key segments.
    Data quality appears consistent across all channels. Revenue streams are diversifying effectively.
    Seasonal patterns indicate peak performance in Q4. Geographic distribution shows concentration in urban areas.
    We should focus on digital transformation initiatives. Investment in customer experience will drive growth.
    Expanding into emerging markets presents opportunities. Technology upgrades should be prioritized for efficiency.
    '''
    
    result = service._parse_natural_language_response(structured_text)
    if result:
        print("✅ Successfully parsed natural language")
        for category, items in result.items():
            print(f"  {category}: {len(items)} items")
            for item in items:
                print(f"    - {item[:80]}...")
    else:
        print("❌ Failed to parse natural language")
    
    return result is not None

def main():
    """Main test function."""
    print("Unified Insights Parsing Test")
    print("=" * 60)
    
    # Run all tests
    json_test = test_json_parsing()
    extraction_test = test_json_extraction()
    nl_test = test_natural_language_parsing()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    tests_passed = sum([json_test, extraction_test, nl_test])
    total_tests = 3
    
    print(f"Tests passed: {tests_passed}/{total_tests}")
    print(f"✅ JSON parsing: {'PASS' if json_test else 'FAIL'}")
    print(f"✅ JSON extraction: {'PASS' if extraction_test else 'FAIL'}")
    print(f"✅ Natural language parsing: {'PASS' if nl_test else 'FAIL'}")
    
    if tests_passed == total_tests:
        print("\n🎉 All parsing tests passed!")
        print("The unified insights service should now handle various response formats.")
    else:
        print(f"\n⚠️  {total_tests - tests_passed} test(s) failed.")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

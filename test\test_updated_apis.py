#!/usr/bin/env python3
"""
Test script to verify all updated APIs work correctly with the new common headers system.
"""

import requests
import uuid
import json
import time
from datetime import datetime

# Base URL for the API
BASE_URL = "http://localhost:8000/v1"

def generate_test_headers():
    """Generate test headers for API calls."""
    return {
        # Request tracking
        "x-request-id": str(uuid.uuid4()),
        "x-correlation-id": str(uuid.uuid4()),
        "x-session-id": f"test-session-{uuid.uuid4().hex[:8]}",
        "x-device-id": f"test-device-{uuid.uuid4().hex[:8]}",
        
        # Client information
        "x-client-version": "test-1.0.0",
        "x-platform": "test-client",
        "x-timezone": "America/New_York",
        "accept-language": "en-US,en;q=0.9",
        
        # Standard headers
        "user-agent": "CommonHeadersTestClient/1.0",
        "content-type": "application/json",
        "accept": "application/json"
    }

def test_health_endpoint():
    """Test the updated health endpoint."""
    print("Testing Health Endpoint...")
    
    headers = generate_test_headers()
    
    try:
        response = requests.get(f"{BASE_URL}/health", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("   ✓ Health endpoint responded successfully")
            print(f"   ✓ Request ID: {data.get('request_id', 'Not found')}")
            print(f"   ✓ Client Platform: {data.get('client_info', {}).get('platform', 'Not found')}")
            print(f"   ✓ Client Version: {data.get('client_info', {}).get('client_version', 'Not found')}")
            return True
        else:
            print(f"   ✗ Health endpoint failed: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ⚠️  Server not running. Start with: uvicorn app.main:app --reload")
        return False
    except Exception as e:
        print(f"   ✗ Error testing health endpoint: {e}")
        return False

def test_chart_insights_endpoint():
    """Test the updated chart insights endpoint."""
    print("\nTesting Chart Insights Endpoint...")
    
    headers = generate_test_headers()
    
    # Sample chart data for insights generation
    chart_data = {
        "chart_data": {
            "data": [
                {"x": "Q1", "y": 100},
                {"x": "Q2", "y": 150},
                {"x": "Q3", "y": 120},
                {"x": "Q4", "y": 180}
            ]
        },
        "chart_type": "bar",
        "chart_title": "Quarterly Sales Test",
        "include_executive_summary": True,
        "include_data_insights": True,
        "include_hidden_patterns": False,
        "include_recommendations": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/chart/insights", headers=headers, json=chart_data)
        
        if response.status_code == 200:
            data = response.json()
            print("   ✓ Chart insights endpoint responded successfully")
            print(f"   ✓ Executive summary items: {len(data.get('executive_summary', []))}")
            print(f"   ✓ Data insights items: {len(data.get('data_insights', []))}")
            return True
        else:
            print(f"   ✗ Chart insights endpoint failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ⚠️  Server not running. Start with: uvicorn app.main:app --reload")
        return False
    except Exception as e:
        print(f"   ✗ Error testing chart insights endpoint: {e}")
        return False

def test_authentication_endpoint():
    """Test the updated authentication endpoint."""
    print("\nTesting Authentication Endpoint...")
    
    headers = generate_test_headers()
    
    # Use debug login for testing
    auth_data = {
        "email": "<EMAIL>"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/debug-login", headers=headers, json=auth_data)
        
        if response.status_code == 200:
            data = response.json()
            print("   ✓ Authentication endpoint responded successfully")
            print(f"   ✓ Access token received: {'access_token' in data}")
            print(f"   ✓ Refresh token received: {'refresh_token' in data}")
            return True, data.get('access_token')
        else:
            print(f"   ✗ Authentication endpoint failed: {response.status_code}")
            return False, None
            
    except requests.exceptions.ConnectionError:
        print("   ⚠️  Server not running. Start with: uvicorn app.main:app --reload")
        return False, None
    except Exception as e:
        print(f"   ✗ Error testing authentication endpoint: {e}")
        return False, None

def test_csv_upload_endpoint(access_token):
    """Test the updated CSV upload endpoint."""
    print("\nTesting CSV Upload Endpoint...")
    
    headers = generate_test_headers()
    if access_token:
        headers["Authorization"] = f"Bearer {access_token}"
    
    # Create a simple test CSV content
    csv_content = """Name,Age,City
John,25,New York
Jane,30,Los Angeles
Bob,35,Chicago"""
    
    try:
        # Create a file-like object for testing
        files = {
            'file': ('test.csv', csv_content, 'text/csv')
        }
        
        # Remove content-type header for multipart upload
        upload_headers = {k: v for k, v in headers.items() if k != 'content-type'}
        
        response = requests.post(f"{BASE_URL}/data/file-upload", headers=upload_headers, files=files)
        
        if response.status_code == 200:
            data = response.json()
            print("   ✓ CSV upload endpoint responded successfully")
            print(f"   ✓ Data processed: {'data' in data}")
            return True
        elif response.status_code == 401:
            print("   ⚠️  CSV upload requires authentication (expected)")
            return True  # This is expected behavior
        else:
            print(f"   ✗ CSV upload endpoint failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ⚠️  Server not running. Start with: uvicorn app.main:app --reload")
        return False
    except Exception as e:
        print(f"   ✗ Error testing CSV upload endpoint: {e}")
        return False

def test_analytics_endpoint():
    """Test the updated analytics endpoint."""
    print("\nTesting Analytics Endpoint...")
    
    headers = generate_test_headers()
    
    # Sample analytics event
    event_data = {
        "event_type": "api_test",
        "page": "/test",
        "timestamp": datetime.now().isoformat(),
        "metadata": {
            "test_run": True,
            "source": "test_script"
        }
    }
    
    try:
        response = requests.post(f"{BASE_URL}/example/analytics", headers=headers, json=event_data)
        
        if response.status_code == 200:
            data = response.json()
            print("   ✓ Analytics endpoint responded successfully")
            print(f"   ✓ Event processed: {data.get('analytics_data', {}).get('processed', False)}")
            print(f"   ✓ User authenticated: {data.get('user_authenticated', False)}")
            return True
        else:
            print(f"   ✗ Analytics endpoint failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ⚠️  Server not running. Start with: uvicorn app.main:app --reload")
        return False
    except Exception as e:
        print(f"   ✗ Error testing analytics endpoint: {e}")
        return False

def test_example_endpoints():
    """Test the example endpoints to verify the system works."""
    print("\nTesting Example Endpoints...")
    
    headers = generate_test_headers()
    
    endpoints_to_test = [
        ("/example/public", "Public endpoint"),
        ("/example/headers-summary", "Headers summary"),
        ("/example/optional-auth", "Optional auth")
    ]
    
    results = []
    
    for endpoint, description in endpoints_to_test:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
            
            if response.status_code == 200:
                print(f"   ✓ {description} - SUCCESS")
                results.append(True)
            else:
                print(f"   ✗ {description} - FAILED ({response.status_code})")
                results.append(False)
                
        except Exception as e:
            print(f"   ✗ {description} - ERROR: {e}")
            results.append(False)
    
    return all(results)

def test_backward_compatibility():
    """Test that legacy header formats still work."""
    print("\nTesting Backward Compatibility...")
    
    # Legacy headers format
    legacy_headers = {
        "X-Interaction-Id": str(uuid.uuid4()),
        "X-Session-Id": f"legacy-session-{uuid.uuid4().hex[:8]}",
        "X-Device-Id": f"legacy-device-{uuid.uuid4().hex[:8]}",
        "User-Agent": "LegacyTestClient/1.0",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/health", headers=legacy_headers)
        
        if response.status_code == 200:
            print("   ✓ Legacy headers still work")
            return True
        else:
            print(f"   ✗ Legacy headers failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ✗ Error testing legacy headers: {e}")
        return False

def main():
    """Run all API tests."""
    print("=" * 70)
    print("TESTING UPDATED APIs WITH COMMON HEADERS SYSTEM")
    print("=" * 70)
    print("This script tests all updated API endpoints to ensure they work")
    print("correctly with the new common headers system.")
    print("=" * 70)
    
    # Run all tests
    tests = [
        ("Health Endpoint", test_health_endpoint),
        ("Chart Insights Endpoint", test_chart_insights_endpoint),
        ("Example Endpoints", test_example_endpoints),
        ("Backward Compatibility", test_backward_compatibility)
    ]
    
    results = []
    access_token = None
    
    # Test authentication first to get token
    auth_success, access_token = test_authentication_endpoint()
    results.append(("Authentication Endpoint", auth_success))
    
    # Test CSV upload with token
    csv_success = test_csv_upload_endpoint(access_token)
    results.append(("CSV Upload Endpoint", csv_success))
    
    # Test analytics
    analytics_success = test_analytics_endpoint()
    results.append(("Analytics Endpoint", analytics_success))
    
    # Run other tests
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Print results
    print("\n" + "=" * 70)
    print("TEST RESULTS")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All API tests passed!")
        print("The common headers system is working correctly across all endpoints.")
        print("\nNext steps:")
        print("1. Update your frontend to send the standard headers")
        print("2. Use the UI Integration Guide for implementation details")
        print("3. Monitor the enhanced logging and analytics data")
    else:
        print(f"\n⚠️  {total - passed} tests failed.")
        print("Please check the server logs and ensure all dependencies are properly configured.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

#!/usr/bin/env python3
"""
Test script to verify that basic chart recommender generates maximum possible combinations.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.chart_recommendations.basic_charts import BasicChartRecommender

def test_basic_chart_combinations():
    """Test that basic chart recommender generates all possible combinations."""
    print("=== Testing Basic Chart Recommender Optimization ===")
    
    # Create test metadata with multiple categorical and numerical columns
    metadata = {
        "categorical_columns": ["Category1", "Category2", "Category3"],
        "numerical_columns": ["Sales", "Profit", "Quantity"],
        "unique_counts": {
            "Category1": 4,   # Good for pie, doughnut, semi_circle, horizontal_bar, column
            "Category2": 6,   # Good for pie, doughnut, semi_circle, horizontal_bar, column
            "Category3": 10   # Good for horizontal_bar, bar, column
        },
        "sample_data": [
            {"column": "Category1", "sample": ["A", "B", "C", "D"]},
            {"column": "Category2", "sample": ["X", "Y", "Z", "W", "V", "U"]},
            {"column": "Category3", "sample": ["Type1", "Type2", "Type3", "Type4", "Type5", "Type6", "Type7", "Type8", "Type9", "Type10"]}
        ],
        "column_characteristics": {
            "Category1": {"avg_text_length": 5.0, "null_ratio": 0.0},
            "Category2": {"avg_text_length": 4.0, "null_ratio": 0.0},
            "Category3": {"avg_text_length": 8.0, "null_ratio": 0.0},
            "Sales": {"null_ratio": 0.0},
            "Profit": {"null_ratio": 0.0},
            "Quantity": {"null_ratio": 0.0}
        }
    }
    
    print("Test data:")
    print(f"  - Categorical columns: {len(metadata['categorical_columns'])}")
    print(f"  - Numerical columns: {len(metadata['numerical_columns'])}")
    print(f"  - Unique counts: {metadata['unique_counts']}")
    
    recommender = BasicChartRecommender()
    recommendations = recommender.get_recommendations(metadata)
    
    print(f"\nGenerated {len(recommendations)} basic chart recommendations")
    
    # Count by chart type
    chart_types = {}
    for rec in recommendations:
        chart_type = rec.get('chart_type', 'unknown')
        chart_types[chart_type] = chart_types.get(chart_type, 0) + 1
    
    print("\nChart type breakdown:")
    for chart_type, count in sorted(chart_types.items()):
        print(f"  {chart_type}: {count}")
    
    # Show some sample recommendations
    print(f"\nSample recommendations:")
    for i, rec in enumerate(recommendations[:5]):
        fields = rec.get('fields', {})
        print(f"  [{i+1}] {rec.get('chart_type')} - {fields.get('x')} + {fields.get('numeric')} ({fields.get('aggregation')})")
    
    # Expected combinations analysis:
    # Each categorical column can combine with: count + 3 numerical columns * 2 aggregations = 1 + 6 = 7 combinations per category
    # Total combinations: 3 categories * 7 = 21 combinations
    # Each combination can be used by multiple chart types based on category count constraints
    
    print(f"\nExpected combinations analysis:")
    print(f"  - Category1 (4 unique): suitable for pie, doughnut, semi_circle, horizontal_bar, column = 5 chart types")
    print(f"  - Category2 (6 unique): suitable for pie, doughnut, semi_circle, horizontal_bar, column = 5 chart types") 
    print(f"  - Category3 (10 unique): suitable for horizontal_bar, bar, column = 3 chart types")
    print(f"  - Each category has 7 combinations (1 count + 6 numerical)")
    print(f"  - Expected total: (5+5+3) * 7 = 91 recommendations")
    
    return len(recommendations)

if __name__ == "__main__":
    print("Testing optimized basic chart recommender for maximum combinations...")
    
    basic_count = test_basic_chart_combinations()
    
    print(f"\n=== SUMMARY ===")
    print(f"Basic chart recommendations: {basic_count}")
    
    if basic_count > 50:
        print("✅ Optimization successful! Basic chart recommender now generates many more combinations.")
    else:
        print("❌ Optimization may need further work - expected more recommendations.")

#!/usr/bin/env python3
"""
Test script for datetime component extraction functionality.
"""

import pandas as pd
import sys
import os
from datetime import datetime, timedelta

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.data_processor import DataProcessor
from app.core.logging_config import configure_logging

# Configure logging
logger = configure_logging('test_datetime_components')

def test_datetime_component_extraction():
    """Test the datetime component extraction functionality."""
    
    # Initialize the data processor
    processor = DataProcessor()
    
    # Test Case 1: Multi-day dataset with datetime
    print("\n=== Test Case 1: Multi-day dataset ===")
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    num_dates = len(dates)
    df1 = pd.DataFrame({
        'timestamp': dates,
        'category': (['A', 'B', 'C'] * (num_dates // 3 + 1))[:num_dates],
        'value': list(range(num_dates))
    })
    
    print(f"Original columns: {list(df1.columns)}")
    print(f"Date range: {df1['timestamp'].min()} to {df1['timestamp'].max()}")
    
    result1 = processor.process_data(df1)
    print(f"Processed columns: {list(result1['df'].columns)}")
    print(f"Categorical columns: {result1['column_types']['categorical']}")
    print(f"Datetime columns: {result1['column_types']['datetime']}")
    
    # Test Case 2: Single-day dataset with hourly data
    print("\n=== Test Case 2: Single-day dataset ===")
    base_date = datetime(2023, 6, 15)
    hourly_dates = [base_date + timedelta(hours=i) for i in range(24)]
    df2 = pd.DataFrame({
        'timestamp': hourly_dates,
        'sensor': ['Sensor1', 'Sensor2'] * 12,
        'temperature': [20 + i for i in range(24)]
    })
    
    print(f"Original columns: {list(df2.columns)}")
    print(f"Date range: {df2['timestamp'].min()} to {df2['timestamp'].max()}")
    
    result2 = processor.process_data(df2)
    print(f"Processed columns: {list(result2['df'].columns)}")
    print(f"Categorical columns: {result2['column_types']['categorical']}")
    print(f"Datetime columns: {result2['column_types']['datetime']}")
    
    # Test Case 3: Dataset with multiple years but few unique months
    print("\n=== Test Case 3: Multi-year dataset with seasonal pattern ===")
    seasonal_dates = []
    for year in [2021, 2022, 2023]:
        for month in [6, 7, 8]:  # Summer months only
            for day in range(1, 11):  # First 10 days of each month
                seasonal_dates.append(datetime(year, month, day))
    
    num_seasonal = len(seasonal_dates)
    df3 = pd.DataFrame({
        'date': seasonal_dates,
        'product': (['Product A', 'Product B', 'Product C'] * (num_seasonal // 3 + 1))[:num_seasonal],
        'sales': list(range(num_seasonal))
    })
    
    print(f"Original columns: {list(df3.columns)}")
    print(f"Date range: {df3['date'].min()} to {df3['date'].max()}")
    
    result3 = processor.process_data(df3)
    print(f"Processed columns: {list(result3['df'].columns)}")
    print(f"Categorical columns: {result3['column_types']['categorical']}")
    print(f"Datetime columns: {result3['column_types']['datetime']}")
    
    # Show sample data for extracted components
    for col in result3['df'].columns:
        if any(suffix in col for suffix in ['_date', '_month', '_year', '_hour']):
            print(f"\nSample values for {col}:")
            print(result3['df'][col].value_counts().head())

if __name__ == "__main__":
    test_datetime_component_extraction()

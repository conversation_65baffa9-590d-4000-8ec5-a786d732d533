#!/usr/bin/env python3
"""
Test script to verify debug logging for chart recommendations.
"""

import os
import sys
import pandas as pd

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.logging_config import configure_logging
from app.services.chart_recommender import ChartData
from app.services.data_processor import DataProcessor

# Configure logging
logger = configure_logging()

def test_chart_recommendations_debug():
    """Test chart recommendations with debug logging enabled."""
    
    print("=== Testing Chart Recommendations Debug Logging ===")
    print(f"LOG_LEVEL from environment: {os.environ.get('LOG_LEVEL', 'Not set')}")
    print(f"VERBOSE_LOGGING from environment: {os.environ.get('VERBOSE_LOGGING', 'Not set')}")
    
    # Create sample data for testing
    sample_data = {
        'Category': ['A', 'B', 'C', 'D', 'E', 'A', 'B', 'C'],
        'Region': ['North', 'South', 'East', 'West', 'North', 'South', 'East', 'West'],
        'Sales': [100, 200, 150, 300, 120, 180, 160, 250],
        'Profit': [20, 40, 30, 60, 25, 35, 32, 50],
        'Date': ['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', 
                '2023-01-05', '2023-01-06', '2023-01-07', '2023-01-08']
    }
    
    df = pd.DataFrame(sample_data)
    
    # Process the data to get metadata
    processor = DataProcessor()
    processed_result = processor.process_data(df)
    metadata = processed_result['metadata']
    
    print(f"\nDataset metadata:")
    print(f"- Rows: {len(df)}")
    print(f"- Categorical columns: {metadata.get('categorical_columns', [])}")
    print(f"- Numerical columns: {metadata.get('numerical_columns', [])}")
    print(f"- Unique counts: {metadata.get('unique_counts', {})}")
    
    # Get chart recommendations
    chart_recommender = ChartData()
    recommendations = chart_recommender.get_rule_based_recommendations(metadata)

    print(f"\nGenerated {len(recommendations)} recommendations")

    # Show heatmap recommendations specifically
    heatmap_recs = [r for r in recommendations if r.get('chart_type') == 'heatmap']
    print(f"Heatmap recommendations: {len(heatmap_recs)}")
    for i, rec in enumerate(heatmap_recs[:5]):
        fields = rec.get('fields', {})
        print(f"  [{i+1}] heatmap - X: {fields.get('x', 'N/A')}, Y: {fields.get('y', 'N/A')}, "
              f"Numeric: {fields.get('numeric', 'N/A')}, Agg: {fields.get('aggregation', 'N/A')}")

    # Now test the chart data processing
    print(f"\n=== Testing Chart Data Processing ===")
    from app.services.chart_data import ChartData as ChartDataService
    chart_data_service = ChartDataService()

    processed_charts = chart_data_service.process_chart_recommendations(df, recommendations)
    print(f"Processed {len(processed_charts)} charts from {len(recommendations)} recommendations")

    # Show processed heatmap charts
    processed_heatmaps = [c for c in processed_charts if c.get('type') == 'heatmap']
    print(f"Processed heatmap charts: {len(processed_heatmaps)}")

    # Show all processed charts to understand the structure
    print(f"\nAll processed charts by type:")
    chart_types = {}
    for chart in processed_charts:
        chart_type = chart.get('type', 'unknown')
        if chart_type not in chart_types:
            chart_types[chart_type] = []
        chart_types[chart_type].append(chart)

    for chart_type, charts in chart_types.items():
        print(f"  {chart_type}: {len(charts)} charts")
        if chart_type == 'heatmap' and len(charts) > 0:
            # Show first heatmap structure
            chart = charts[0]
            print(f"    Sample heatmap structure:")
            print(f"      Title: {chart.get('title', 'N/A')}")
            print(f"      X: {chart.get('x', [])[:3] if chart.get('x') else 'N/A'}")
            print(f"      Y: {chart.get('y', [])[:3] if chart.get('y') else 'N/A'}")
            print(f"      Z shape: {len(chart.get('z', []))}x{len(chart.get('z', [[]])[0]) if chart.get('z') and chart.get('z')[0] else 0}")
            print(f"      Keys: {list(chart.keys())}")

    # Let's also check what's happening before uniqueness filtering
    print(f"\nBefore uniqueness filtering:")
    all_charts_before = []
    for chart_rec in recommendations:
        if not chart_data_service.is_valid_chart_recommendation(chart_rec):
            continue
        try:
            processed_chart = chart_data_service.data_aggregator.prepare_chart_data(
                df=df,
                chart_type=chart_rec['chart_type'],
                fields=chart_rec['fields']
            )
            if processed_chart:
                all_charts_before.append(processed_chart)
        except Exception as e:
            continue

    heatmaps_before = [c for c in all_charts_before if c.get('type') == 'heatmap']
    print(f"Heatmaps before uniqueness filtering: {len(heatmaps_before)}")
    for i, chart in enumerate(heatmaps_before[:3]):
        print(f"  [{i+1}] Title: {chart.get('title', 'N/A')}")
        print(f"      X: {chart.get('x', [])[:3] if chart.get('x') else 'N/A'}")
        print(f"      Y: {chart.get('y', [])[:3] if chart.get('y') else 'N/A'}")
        print(f"      Z shape: {len(chart.get('z', []))}x{len(chart.get('z', [[]])[0]) if chart.get('z') and chart.get('z')[0] else 0}")

if __name__ == "__main__":
    test_chart_recommendations_debug()

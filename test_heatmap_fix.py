#!/usr/bin/env python3
"""
Test the heatmap uniqueness fix.
"""

import os
import sys
import pandas as pd

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.logging_config import configure_logging
from app.services.chart_data import ChartData
from app.services.data_processor import DataProcessor

# Configure logging
logger = configure_logging()

def test_heatmap_fix():
    """Test that multiple heatmaps are now returned."""
    
    print("=== Testing Heatmap Fix ===")
    
    # Create sample data
    data = {
        'Region': ['North', 'South', 'East', 'West', 'North', 'South', 'East', 'West'],
        'Category': ['A', 'A', 'A', 'A', 'B', 'B', 'B', 'B'],
        'Sales': [100, 200, 150, 300, 250, 180, 220, 400],
        'Profit': [20, 40, 30, 60, 50, 36, 44, 80],
        'Date': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', 
                               '2023-01-05', '2023-01-06', '2023-01-07', '2023-01-08'])
    }
    df = pd.DataFrame(data)
    
    # Get metadata
    processor = DataProcessor()
    metadata = processor.get_dataset_metadata(df)
    
    # Get chart recommendations and process them
    chart_data_service = ChartData()
    processed_charts = chart_data_service.process_chart_recommendations(df, 
                                                                       chart_data_service.get_rule_based_recommendations(metadata))
    
    # Count charts by type
    chart_counts = {}
    for chart in processed_charts:
        chart_type = chart.get('chart_type', 'unknown')
        chart_counts[chart_type] = chart_counts.get(chart_type, 0) + 1
    
    print(f"Total processed charts: {len(processed_charts)}")
    print(f"Chart counts by type:")
    for chart_type, count in sorted(chart_counts.items()):
        print(f"  {chart_type}: {count}")
    
    # Show heatmap details
    heatmaps = [c for c in processed_charts if c.get('chart_type') == 'heatmap']
    print(f"\nHeatmap charts: {len(heatmaps)}")
    
    for i, chart in enumerate(heatmaps[:5]):  # Show first 5
        layout = chart.get('layout', {})
        title = layout.get('title', 'N/A')
        data = chart.get('data', {})
        x_labels = data.get('x', [])
        y_labels = data.get('y', [])
        z_shape = f"{len(data.get('z', []))}x{len(data.get('z', [[]])[0]) if data.get('z') and data.get('z')[0] else 0}"
        
        print(f"  [{i+1}] {title}")
        print(f"      X: {x_labels[:3]}... ({len(x_labels)} total)")
        print(f"      Y: {y_labels[:3]}... ({len(y_labels)} total)")
        print(f"      Z: {z_shape}")
    
    return len(heatmaps)

if __name__ == "__main__":
    heatmap_count = test_heatmap_fix()
    print(f"\n✅ Test completed! Found {heatmap_count} unique heatmap charts.")
    if heatmap_count > 1:
        print("🎉 Fix successful - multiple heatmaps are now being returned!")
    else:
        print("❌ Issue still exists - only one heatmap returned.")

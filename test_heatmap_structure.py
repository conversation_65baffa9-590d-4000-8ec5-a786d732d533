#!/usr/bin/env python3
"""
Simple test to understand heatmap chart structure.
"""

import os
import sys
import pandas as pd

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.logging_config import configure_logging
from app.services.chart_data import ChartData
from app.services.data_processor import DataProcessor

# Configure logging
logger = configure_logging()

def test_heatmap_structure():
    """Test heatmap chart structure to understand uniqueness issue."""
    
    print("=== Testing Heatmap Chart Structure ===")
    
    # Create sample data
    data = {
        'Region': ['North', 'South', 'East', 'West', 'North', 'South', 'East', 'West'],
        'Category': ['A', 'A', 'A', 'A', 'B', 'B', 'B', 'B'],
        'Sales': [100, 200, 150, 300, 250, 180, 220, 400],
        'Profit': [20, 40, 30, 60, 50, 36, 44, 80],
        'Date': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', 
                               '2023-01-05', '2023-01-06', '2023-01-07', '2023-01-08'])
    }
    df = pd.DataFrame(data)
    
    # Get metadata
    processor = DataProcessor()
    metadata = processor.get_dataset_metadata(df)
    
    # Get heatmap recommendations
    chart_data = ChartData()
    recommendations = chart_data.get_rule_based_recommendations(metadata)
    
    heatmap_recs = [r for r in recommendations if r.get('chart_type') == 'heatmap']
    print(f"Found {len(heatmap_recs)} heatmap recommendations")
    
    # Process first 3 heatmap recommendations
    processed_heatmaps = []
    for i, rec in enumerate(heatmap_recs[:3]):
        print(f"\n--- Processing Heatmap {i+1} ---")
        print(f"Recommendation: {rec}")
        
        try:
            chart_data_service = ChartData()
            processed_chart = chart_data_service.data_aggregator.prepare_chart_data(
                df=df,
                chart_type=rec['chart_type'],
                fields=rec['fields']
            )
            
            if processed_chart:
                processed_heatmaps.append(processed_chart)
                print(f"Processed chart keys: {list(processed_chart.keys())}")
                print(f"Title: {processed_chart.get('title', 'N/A')}")
                print(f"Type: {processed_chart.get('type', 'N/A')}")
                print(f"X: {processed_chart.get('x', [])[:3] if processed_chart.get('x') else 'N/A'}")
                print(f"Y: {processed_chart.get('y', [])[:3] if processed_chart.get('y') else 'N/A'}")
                if processed_chart.get('z'):
                    z_array = processed_chart.get('z', [])
                    print(f"Z shape: {len(z_array)}x{len(z_array[0]) if z_array and z_array[0] else 0}")
                    print(f"Z sample: {z_array[0][:3] if z_array and z_array[0] else 'N/A'}")
                else:
                    print(f"Z: N/A")
            else:
                print("Failed to process chart")
                
        except Exception as e:
            print(f"Error processing chart: {str(e)}")
    
    # Test uniqueness logic
    print(f"\n--- Testing Uniqueness Logic ---")
    seen_combinations = set()
    
    for i, chart in enumerate(processed_heatmaps):
        chart_type = chart.get('type', chart.get('chart_type', ''))
        title = chart.get('title', '')
        x_data = str(chart.get('x', [])[:3]) if chart.get('x') else ''
        y_data = str(chart.get('y', [])[:3]) if chart.get('y') else ''
        labels = str(chart.get('labels', [])[:3]) if chart.get('labels') else ''
        
        z_data = ''
        if chart.get('z'):
            z_array = chart.get('z', [])
            if z_array and len(z_array) > 0:
                z_data = f"{len(z_array)}x{len(z_array[0]) if z_array[0] else 0}"
        
        combination_key = (chart_type, title, x_data, y_data, labels, z_data)
        
        print(f"Chart {i+1}:")
        print(f"  Type: {chart_type}")
        print(f"  Title: {title}")
        print(f"  X: {x_data}")
        print(f"  Y: {y_data}")
        print(f"  Labels: {labels}")
        print(f"  Z: {z_data}")
        print(f"  Combination key: {combination_key}")
        print(f"  Is unique: {combination_key not in seen_combinations}")
        
        seen_combinations.add(combination_key)
        print()

if __name__ == "__main__":
    test_heatmap_structure()

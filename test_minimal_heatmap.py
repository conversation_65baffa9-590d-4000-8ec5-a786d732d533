#!/usr/bin/env python3
"""
Minimal test for heatmap uniqueness fix.
"""

import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_uniqueness_logic():
    """Test the uniqueness logic directly."""
    
    print("=== Testing Heatmap Uniqueness Logic ===")
    
    # Mock heatmap chart data structures (based on actual structure)
    heatmap1 = {
        "chart_type": "heatmap",
        "library": "plotly",
        "data": {
            "z": [[1, 2], [3, 4]],
            "x": ["North", "South"],
            "y": ["A", "B"],
            "type": "heatmap",
            "colorscale": "Viridis",
            "hoverongaps": False
        },
        "layout": {
            "title": "Region vs Category (count)",
            "xaxis": {"title": "Region", "side": "bottom", "type": "category"},
            "yaxis": {"title": "Category", "type": "category"}
        },
        "time_aggregation": None
    }
    
    heatmap2 = {
        "chart_type": "heatmap",
        "library": "plotly",
        "data": {
            "z": [[100, 200], [250, 180]],
            "x": ["North", "South"],
            "y": ["A", "B"],
            "type": "heatmap",
            "colorscale": "Viridis",
            "hoverongaps": False
        },
        "layout": {
            "title": "Region vs Category (sum of Sales)",
            "xaxis": {"title": "Region", "side": "bottom", "type": "category"},
            "yaxis": {"title": "Category", "type": "category"}
        },
        "time_aggregation": None
    }
    
    heatmap3 = {
        "chart_type": "heatmap",
        "library": "plotly",
        "data": {
            "z": [[50, 100], [125, 90]],
            "x": ["North", "South"],
            "y": ["A", "B"],
            "type": "heatmap",
            "colorscale": "Viridis",
            "hoverongaps": False
        },
        "layout": {
            "title": "Region vs Category (avg of Sales)",
            "xaxis": {"title": "Region", "side": "bottom", "type": "category"},
            "yaxis": {"title": "Category", "type": "category"}
        },
        "time_aggregation": None
    }
    
    chart_data_list = [heatmap1, heatmap2, heatmap3]
    
    # Test the uniqueness logic
    seen_combinations = set()
    unique_charts = []
    
    for i, chart in enumerate(chart_data_list):
        # Extract identifying information (same logic as in the fix)
        chart_type = chart.get('chart_type', '')
        layout = chart.get('layout', {})
        title = layout.get('title', '')
        data = chart.get('data', {})
        x_data = str(data.get('x', [])[:3]) if data.get('x') else ''
        y_data = str(data.get('y', [])[:3]) if data.get('y') else ''
        labels = str(chart.get('labels', [])[:3]) if chart.get('labels') else ''
        
        z_data = ''
        if data.get('z'):
            z_array = data.get('z', [])
            if z_array and len(z_array) > 0:
                z_data = f"{len(z_array)}x{len(z_array[0]) if z_array[0] else 0}"
        
        time_agg = chart.get('time_aggregation', '')
        
        combination_key = (chart_type, title, x_data, y_data, labels, z_data, time_agg)
        
        print(f"\nChart {i+1}:")
        print(f"  Type: {chart_type}")
        print(f"  Title: {title}")
        print(f"  X: {x_data}")
        print(f"  Y: {y_data}")
        print(f"  Z: {z_data}")
        print(f"  Time Agg: {time_agg}")
        print(f"  Combination key: {combination_key}")
        print(f"  Is unique: {combination_key not in seen_combinations}")
        
        if combination_key not in seen_combinations:
            seen_combinations.add(combination_key)
            unique_charts.append(chart)
            print(f"  ✅ Added as unique chart")
        else:
            print(f"  ❌ Skipped as duplicate")
    
    print(f"\n=== Results ===")
    print(f"Total input charts: {len(chart_data_list)}")
    print(f"Unique charts: {len(unique_charts)}")
    
    for i, chart in enumerate(unique_charts):
        title = chart.get('layout', {}).get('title', 'N/A')
        print(f"  [{i+1}] {title}")
    
    return len(unique_charts)

if __name__ == "__main__":
    unique_count = test_uniqueness_logic()
    print(f"\n✅ Test completed! {unique_count} unique heatmaps identified.")
    if unique_count == 3:
        print("🎉 Fix successful - all 3 heatmaps are unique!")
    else:
        print("❌ Issue still exists - not all heatmaps are unique.")

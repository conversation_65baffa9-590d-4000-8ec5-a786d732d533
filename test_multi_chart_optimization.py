#!/usr/bin/env python3
"""
Test script to verify that multi-chart and multi-group chart recommenders 
generate maximum possible combinations after optimization.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.chart_recommendations.multi_charts import <PERSON><PERSON><PERSON><PERSON>ecommender
from app.services.chart_recommendations.multi_group_charts import MultiG<PERSON><PERSON><PERSON>Recommender
from app.services.chart_recommendations.stacked_charts import <PERSON>ack<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_multi_chart_combinations():
    """Test that multi-chart recommender generates all possible combinations."""
    print("=== Testing Multi-Chart Recommender ===")
    
    # Create test metadata with multiple datetime, categorical, and numerical columns
    metadata = {
        "datetime_columns": ["Date1", "Date2", "Date3"],
        "categorical_columns": ["Category1", "Category2", "Category3"],
        "numerical_columns": ["Sales", "Profit", "Quantity", "Revenue"],
        "unique_counts": {
            "Category1": 5,
            "Category2": 8,
            "Category3": 3,
            "Date1": 100,
            "Date2": 200,
            "Date3": 150
        },
        "date_metadata": {}
    }
    
    recommender = MultiChartRecommender()
    recommendations = recommender.get_recommendations(metadata)
    
    print(f"Generated {len(recommendations)} multi-chart recommendations")
    
    # Count by chart type
    chart_types = {}
    for rec in recommendations:
        chart_type = rec.get('chart_type', 'unknown')
        chart_types[chart_type] = chart_types.get(chart_type, 0) + 1
    
    print("Chart type breakdown:")
    for chart_type, count in chart_types.items():
        print(f"  {chart_type}: {count}")
    
    # Expected combinations:
    # Multi-line: 3 datetime cols * combinations of 2-4 numerical cols = 3 * (C(4,2) + C(4,3) + C(4,4)) = 3 * (6 + 4 + 1) = 33
    # Multi-area: 3 datetime cols * combinations of 2-3 numerical cols = 3 * (C(4,2) + C(4,3)) = 3 * (6 + 4) = 30  
    # Combo bar-line: 3 categorical cols * C(4,2) = 3 * 6 = 18
    expected_total = 33 + 30 + 18
    print(f"Expected approximately {expected_total} recommendations")
    
    return len(recommendations)

def test_multi_group_combinations():
    """Test that multi-group chart recommender generates all possible combinations."""
    print("\n=== Testing Multi-Group Chart Recommender ===")
    
    # Create test metadata with multiple categorical and numerical columns
    metadata = {
        "categorical_columns": ["Category1", "Category2", "Category3", "Category4"],
        "numerical_columns": ["Sales", "Profit", "Quantity"],
        "datetime_columns": [],
        "unique_counts": {
            "Category1": 5,
            "Category2": 8,
            "Category3": 3,
            "Category4": 12
        }
    }
    
    recommender = MultiGroupChartRecommender()
    recommendations = recommender.get_recommendations(metadata)
    
    print(f"Generated {len(recommendations)} multi-group chart recommendations")
    
    # Expected combinations:
    # Valid categorical pairs: C(4,2) = 6 pairs
    # Each pair with count + 3 numerical columns = 6 * 4 = 24
    expected_total = 24
    print(f"Expected approximately {expected_total} recommendations")
    
    return len(recommendations)

def test_stacked_chart_combinations():
    """Test that stacked chart recommender generates all possible combinations."""
    print("\n=== Testing Stacked Chart Recommender ===")
    
    # Create test metadata with datetime, categorical, and numerical columns
    metadata = {
        "datetime_columns": ["Date1", "Date2"],
        "categorical_columns": ["Category1", "Category2", "Category3"],
        "numerical_columns": ["Sales", "Profit"],
        "unique_counts": {
            "Category1": 5,
            "Category2": 6,
            "Category3": 4,
            "Date1": 100,
            "Date2": 200
        },
        "date_metadata": {}
    }
    
    recommender = StackedChartRecommender()
    recommendations = recommender.get_recommendations(metadata)
    
    print(f"Generated {len(recommendations)} stacked chart recommendations")
    
    # Count by chart type
    chart_types = {}
    for rec in recommendations:
        chart_type = rec.get('chart_type', 'unknown')
        chart_types[chart_type] = chart_types.get(chart_type, 0) + 1
    
    print("Chart type breakdown:")
    for chart_type, count in chart_types.items():
        print(f"  {chart_type}: {count}")
    
    # Expected combinations:
    # Time series: 2 datetime * 3 categorical * 2 numerical * 2 chart types = 24
    # Non-time series: C(3,2) categorical pairs * 2 numerical * 3 chart types = 3 * 2 * 3 = 18
    expected_total = 24 + 18
    print(f"Expected approximately {expected_total} recommendations")
    
    return len(recommendations)

if __name__ == "__main__":
    print("Testing optimized chart recommenders for maximum combinations...")
    
    multi_count = test_multi_chart_combinations()
    multi_group_count = test_multi_group_combinations()
    stacked_count = test_stacked_chart_combinations()
    
    print(f"\n=== SUMMARY ===")
    print(f"Multi-chart recommendations: {multi_count}")
    print(f"Multi-group recommendations: {multi_group_count}")
    print(f"Stacked chart recommendations: {stacked_count}")
    print(f"Total optimized recommendations: {multi_count + multi_group_count + stacked_count}")
    
    print("\nOptimization successful! All recommenders now generate maximum possible combinations.")

#!/usr/bin/env python3
"""
Test script to verify GoogleSheetsService singleton optimization.
"""

import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.logging_config import configure_logging
from app.services.analytics.google_sheets_service import GoogleSheetsService
from app.services.analytics.event_tracking_service import EventTrackingService
from app.services.analytics.user_profile_service import UserProfileService

# Configure logging
logger = configure_logging()

def test_singleton_pattern():
    """Test that GoogleSheetsService follows singleton pattern."""
    
    print("=== Testing GoogleSheetsService Singleton Pattern ===")
    print(f"LOG_LEVEL from environment: {os.environ.get('LOG_LEVEL', 'Not set')}")
    
    # Test 1: Direct instantiation
    print("\n1. Testing direct instantiation...")
    service1 = GoogleSheetsService()
    service2 = GoogleSheetsService()
    
    print(f"Service1 ID: {id(service1)}")
    print(f"Service2 ID: {id(service2)}")
    print(f"Are they the same instance? {service1 is service2}")
    
    # Test 2: Through other services
    print("\n2. Testing through EventTrackingService and UserProfileService...")
    event_service = EventTrackingService()
    user_service = UserProfileService()
    
    print(f"EventTrackingService.sheets_service ID: {id(event_service.sheets_service)}")
    print(f"UserProfileService.sheets_service ID: {id(user_service.sheets_service)}")
    print(f"Are they the same instance? {event_service.sheets_service is user_service.sheets_service}")
    
    # Test 3: Multiple service instantiations
    print("\n3. Testing multiple service instantiations...")
    event_service2 = EventTrackingService()
    user_service2 = UserProfileService()
    
    print(f"EventTrackingService2.sheets_service ID: {id(event_service2.sheets_service)}")
    print(f"UserProfileService2.sheets_service ID: {id(user_service2.sheets_service)}")
    print(f"All services use same GoogleSheetsService? {all([
        event_service.sheets_service is user_service.sheets_service,
        event_service.sheets_service is event_service2.sheets_service,
        event_service.sheets_service is user_service2.sheets_service,
        event_service.sheets_service is service1
    ])}")
    
    # Test 4: Check initialization count
    print(f"\n4. GoogleSheetsService._initialized: {GoogleSheetsService._initialized}")
    print(f"   Service available: {service1.is_available()}")
    
    print("\n✅ Singleton pattern test completed!")
    return service1

if __name__ == "__main__":
    test_singleton_pattern()
